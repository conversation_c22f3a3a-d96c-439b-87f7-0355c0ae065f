"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/records/[recordId]/page",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/ViewsRootLayout.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/workspace/main/views/ViewsRootLayout.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewContext: function() { return /* binding */ ViewContext; },\n/* harmony export */   ViewsRootLayout: function() { return /* binding */ ViewsRootLayout; },\n/* harmony export */   useViewContext: function() { return /* binding */ useViewContext; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _typings_page__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/typings/page */ \"(app-pages-browser)/./src/typings/page.ts\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* harmony import */ var _api_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/api/page */ \"(app-pages-browser)/./src/api/page.ts\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _utils_clipboard__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/clipboard */ \"(app-pages-browser)/./src/utils/clipboard.ts\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_columnsReorder__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/common/columnsReorder */ \"(app-pages-browser)/./src/components/workspace/main/views/common/columnsReorder.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewCreator */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewCreator.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSwitcher__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSwitcher */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSwitcher.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewFilter */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewFilter.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSort */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSort.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewMoreOptions */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewMoreOptions.tsx\");\n/* harmony import */ var _components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/summaryTable/renderers/header */ \"(app-pages-browser)/./src/components/workspace/main/views/summaryTable/renderers/header.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/common */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/common.js\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/workspace/main/emails/sendEmailWrapper */ \"(app-pages-browser)/./src/components/workspace/main/emails/sendEmailWrapper.tsx\");\n/* harmony import */ var _components_workspace_main_common_updateRecords__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/workspace/main/common/updateRecords */ \"(app-pages-browser)/./src/components/workspace/main/common/updateRecords.tsx\");\n/* harmony import */ var _api_account__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/api/account */ \"(app-pages-browser)/./src/api/account.ts\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_shareView__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/workspace/main/views/common/shareView */ \"(app-pages-browser)/./src/components/workspace/main/views/common/shareView.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _components_workspace_main_record_components_recordExtras__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @/components/workspace/main/record/components/recordExtras */ \"(app-pages-browser)/./src/components/workspace/main/record/components/recordExtras.tsx\");\n/* harmony import */ var _yudiel_react_qr_scanner__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @yudiel/react-qr-scanner */ \"(app-pages-browser)/./node_modules/@yudiel/react-qr-scanner/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _providers_recordTabViews__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @/providers/recordTabViews */ \"(app-pages-browser)/./src/providers/recordTabViews.tsx\");\n/* harmony import */ var _components_workspace_main_views_addrecordmodal__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! @/components/workspace/main/views/addrecordmodal */ \"(app-pages-browser)/./src/components/workspace/main/views/addrecordmodal.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* __next_internal_client_entry_do_not_use__ ViewContext,useViewContext,ViewsRootLayout auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import {ScrollArea} from \"@/components/ui/scroll-area\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ViewContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    context: \"page\"\n});\nconst useViewContext = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ViewContext);\n};\n_s(useViewContext, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nconst ViewsRootLayout = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo, _maybeRecord_recordInfo_record1, _maybeRecord_recordInfo1;\n    _s1();\n    const { url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_30__.useAuth)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const documentId = searchParams.get(\"documentId\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { viewsMap, accessLevel, page } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_6__.usePage)();\n    const [newView, setNewView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { createRecords, deleteRecords, smartUpdateViewDefinition, peekRecordId, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViews)();\n    const { filter, sorts, search, setFilter, setSorts, setSearch } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewSelection)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_34__.useMaybeRecord)();\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__.useStackedPeek)();\n    // State for AddRecordModal\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // For record_tab context, get view from database metadata\n    let view = null;\n    if (props.context === \"record_tab\") {\n        var _databaseStore_props_parentId;\n        const database = (_databaseStore_props_parentId = databaseStore[props.parentId]) === null || _databaseStore_props_parentId === void 0 ? void 0 : _databaseStore_props_parentId.database;\n        if (database && database.meta && database.meta.recordViewsMap) {\n            view = database.meta.recordViewsMap[props.viewId] || null;\n        }\n    }\n    if (!view) {\n        view = viewsMap[props.viewId] || null;\n    }\n    let viewType = view === null || view === void 0 ? void 0 : view.type;\n    // Context-aware update function that automatically detects record tab vs regular views\n    const contextAwareUpdateViewDefinition = (update)=>{\n        const isRecordTab = props.context === \"record_tab\";\n        const databaseId = isRecordTab ? props.parentId : undefined;\n        return smartUpdateViewDefinition((view === null || view === void 0 ? void 0 : view.id) || \"\", (view === null || view === void 0 ? void 0 : view.pageId) || \"\", update, {\n            databaseId,\n            isRecordTab\n        });\n    };\n    const canContact = false;\n    const canEdit = accessLevel && [\n        _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Full,\n        _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Edit\n    ].includes(accessLevel);\n    const hasFullAccess = accessLevel && accessLevel === _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Full;\n    let viewFilter = {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.Match.All\n    };\n    if (viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board || viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) {\n        const definition = view.definition;\n        viewFilter = definition.filter || viewFilter;\n    }\n    const handleAddRecord = ()=>{\n        if (!view) return;\n        if (view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board && view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) return;\n        // Different behavior based on context\n        if (props.context === \"record_tab\") {\n            // In record tabs: Use smart modal (especially for cross-database)\n            setShowAddModal(true);\n        } else {\n            // In main views: Direct record creation (original behavior)\n            handleDirectAddRecord();\n        }\n    };\n    const handleDirectAddRecord = async ()=>{\n        var _filter_conditions, _viewFilter_conditions;\n        if (!view) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        // Create record directly without modal (original behavior)\n        const rS = await createRecords(definition.databaseId, [\n            {}\n        ]);\n        // Only peek if there are filters/search (original logic)\n        const shouldPeek = search && search.trim() || (filter === null || filter === void 0 ? void 0 : (_filter_conditions = filter.conditions) === null || _filter_conditions === void 0 ? void 0 : _filter_conditions.length) > 0 || viewFilter && viewFilter.conditions && (viewFilter === null || viewFilter === void 0 ? void 0 : (_viewFilter_conditions = viewFilter.conditions) === null || _viewFilter_conditions === void 0 ? void 0 : _viewFilter_conditions.length) > 0;\n        if (rS && rS.records && rS.records.length > 0 && shouldPeek) {\n            setPeekRecordId(rS.records[0].id);\n        }\n    };\n    const handleRecordCreated = (recordId)=>{\n        if (!view) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        // In record tabs: Stack the newly created record\n        openRecord(recordId, definition.databaseId);\n        setShowAddModal(false);\n    };\n    const deleteSelected = async ()=>{\n        if (!view) return;\n        if (view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board && view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        const { databaseId } = definition;\n        await deleteRecords(databaseId, selectedIds);\n    };\n    const copySharedUrl = ()=>{\n        if (!view) return;\n        const { id, name } = view;\n        const url = (0,_api_page__WEBPACK_IMPORTED_MODULE_12__.getViewPublicUrl)(id, name);\n        (0,_utils_clipboard__WEBPACK_IMPORTED_MODULE_14__.copyToClipboard)(url);\n        toast.success(\"Link copied to clipboard\");\n    };\n    let database = null;\n    let dbId = \"\";\n    if (view && [\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Form,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Calendar,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.ListView\n    ].includes(viewType)) {\n        dbId = view.definition.databaseId;\n        database = databaseStore[dbId] ? databaseStore[dbId].database : null;\n    }\n    const currentViewIdRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(props.viewId || \"\");\n    const viewId = props.viewId;\n    const pageId = (view === null || view === void 0 ? void 0 : view.pageId) || \"\";\n    const workspaceId = workspace.workspace.id;\n    const viewExists = !!view;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!viewExists) return;\n        const timeout = setTimeout(async ()=>{\n            const databaseId = dbId ? dbId : undefined;\n            await (0,_api_account__WEBPACK_IMPORTED_MODULE_29__.pushEvent)((token === null || token === void 0 ? void 0 : token.token) || \"\", {\n                workspaceId,\n                pageId,\n                event: _api_account__WEBPACK_IMPORTED_MODULE_29__.EventType.View,\n                databaseId,\n                viewId\n            });\n        }, 3000);\n        return ()=>{\n            if (timeout) clearTimeout(timeout);\n        };\n    }, [\n        dbId,\n        token === null || token === void 0 ? void 0 : token.token,\n        workspaceId,\n        pageId,\n        viewId,\n        viewExists\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (props.viewId === currentViewIdRef.current) return;\n        currentViewIdRef.current = props.viewId;\n        setSorts([]);\n        setFilter({\n            conditions: [],\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.Match.All\n        });\n        setSearch(\"\");\n        setSelectedIds([]);\n        setPeekRecordId(\"\");\n    }, [\n        props.viewId\n    ]);\n    // , [props.viewId, setFilter, setSorts, setSearch, setSelectedIds, setPeekRecordId])\n    // const [peekRecordId, setPeekRecordId] = useState(\"\")\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewContext.Provider, {\n        value: {\n            context: props.context\n        },\n        children: [\n            !view && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__.PageLoader, {\n                    size: \"full\",\n                    error: \"The requested content does not exists\",\n                    cta: {\n                        label: \"Go Home\",\n                        onClick: ()=>router.replace(url())\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            view && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full overflow-hidden flex flex-col\",\n                children: [\n                    viewType !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Calendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 h-12 flex items-center border-b border-neutral-300 gap-0.5\",\n                        children: [\n                            props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSwitcher__WEBPACK_IMPORTED_MODULE_18__.ViewSwitcher, {\n                                        context: props.context,\n                                        viewId: props.viewId,\n                                        creatable: canEdit,\n                                        editable: canEdit,\n                                        deletable: hasFullAccess,\n                                        cloneable: hasFullAccess,\n                                        requestNewView: ()=>setNewView(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_17__.ViewCreator, {\n                                        context: props.context,\n                                        open: newView,\n                                        setOpen: setNewView\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    !maybeTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_shareView__WEBPACK_IMPORTED_MODULE_31__.ShareView, {\n                                        view: view,\n                                        page: page,\n                                        documentId: documentId || \"\",\n                                        domain: workspace.workspace.domain,\n                                        triggerAlign: \"start\",\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_24__.cn)(\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\", view.isPublished && \"text-blue-600 font-semibold\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpFromArcIcon, {\n                                                    className: \"size-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 33\n                                                }, void 0),\n                                                \" Share View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 38\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 44\n                                    }, undefined),\n                                    props.context === \"page\" && database && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageContextSourceDatabase, {\n                                            database: database\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            props.context === \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 56\n                            }, undefined),\n                            database && [\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board\n                            ].includes(viewType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: !view.definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: handleAddRecord,\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.CirclePlusIcon, {\n                                                        className: \"size-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    \"Add\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false),\n                                        selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-xs text-blue-600 select-none\",\n                                                    children: [\n                                                        selectedIds.length,\n                                                        \" \\xa0selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_updateRecords__WEBPACK_IMPORTED_MODULE_28__.UpdateRecords, {\n                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        variant: \"ghost\",\n                                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.SquarePenIcon, {\n                                                                className: \"size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 41\n                                                            }, void 0),\n                                                            \"Update\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 46\n                                                    }, void 0),\n                                                    database: database,\n                                                    ids: selectedIds,\n                                                    onUpdate: ()=>setSelectedIds([])\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    onClick: deleteSelected,\n                                                    className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.TrashListIcon, {\n                                                            className: \"size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 37\n                                                        }, undefined),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false),\n                            database && [\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.ListView\n                            ].includes(viewType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canContact && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_47__[\"default\"], {\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    \"Enrich\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_48__[\"default\"], {\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    \"Message\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    canEdit && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_27__.SendEmailWrapperForView, {\n                                        database: database,\n                                        view: view\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_19__.ViewFilter, {\n                                        database: database,\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.FilterListIcon, {\n                                                    className: \"size-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                filter.conditions.length > 0 ? \"\".concat(filter.conditions.length, \" filters\") : \"Filter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 33\n                                        }, void 0),\n                                        filter: filter,\n                                        onChange: setFilter,\n                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.id,\n                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.databaseId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    (viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table || viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_20__.ViewSort, {\n                                            database: database,\n                                            sorts: sorts,\n                                            onChange: setSorts,\n                                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpWideShortIcon, {\n                                                        className: \"size-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    sorts.length > 0 ? \"\".concat(sorts.length, \" sorts\") : \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-accent focus:bg-accent active:bg-accent items-center whitespace-nowrap font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                form: \"search-input\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_49__[\"default\"], {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search\",\n                                                value: search,\n                                                onChange: (e)=>setSearch(e.target.value),\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScannableCodeScanner, {\n                                        onRecordScan: setPeekRecordId,\n                                        database: database,\n                                        viewFilter: viewFilter,\n                                        filter: filter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    canEdit && viewType !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_columnsReorder__WEBPACK_IMPORTED_MODULE_16__.ColumnsReorder, {\n                                            onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                            view: view\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__.SummaryColumnGroupBy, {\n                                                onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                                view: view\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__.SummaryColumnsReorder, {\n                                                onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                                view: view\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.ViewMoreOptions, {\n                                        // definition={view.definition as TableViewDefinition}\n                                        disabled: !canEdit,\n                                        view: view,\n                                        database: database,\n                                        selectedIds: selectedIds,\n                                        filter: filter,\n                                        sorts: sorts,\n                                        search: search,\n                                        onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id,\n                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo1 = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo1 === void 0 ? void 0 : (_maybeRecord_recordInfo_record1 = _maybeRecord_recordInfo1.record) === null || _maybeRecord_recordInfo_record1 === void 0 ? void 0 : _maybeRecord_recordInfo_record1.databaseId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            canEdit && database && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Form && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.FormViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    database: database,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false),\n                            canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Dashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.DashboardViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false),\n                            canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Document && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.DocViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 52\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden pr=1\",\n                        children: props.children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 233,\n                columnNumber: 22\n            }, undefined),\n            database && peekRecordId && !maybeRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PeekRecord, {\n                canEdit: canEdit,\n                onClose: ()=>setPeekRecordId(\"\"),\n                recordId: peekRecordId,\n                databaseId: database.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 475,\n                columnNumber: 58\n            }, undefined),\n            database && showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_addrecordmodal__WEBPACK_IMPORTED_MODULE_45__.AddRecordModal, {\n                open: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                databaseId: database.id,\n                viewFilter: viewFilter || undefined,\n                contextualFilter: filter,\n                onRecordCreated: handleRecordCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 482,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n        lineNumber: 224,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(ViewsRootLayout, \"jhp8THB4JYOh4feSj9B/pDpog7g=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_30__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_page__WEBPACK_IMPORTED_MODULE_6__.usePage,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewSelection,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert,\n        _providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate,\n        _providers_record__WEBPACK_IMPORTED_MODULE_34__.useMaybeRecord,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__.useStackedPeek\n    ];\n});\n_c = ViewsRootLayout;\nconst PageContextSourceDatabase = (param)=>{\n    let { database } = param;\n    _s2();\n    const { databasePageStore, databasePagesId } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const db = databasePageStore[database.id];\n    const databaseId = database.id;\n    const dbItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const items = [];\n        const databaseIds = [\n            ...databasePagesId\n        ];\n        if (!databaseIds.includes(databaseId)) databaseIds.push(databaseId);\n        for (const id of databaseIds){\n            const db = databasePageStore[id];\n            if (!db) continue;\n            const { page } = db;\n            const emoji = page.icon && page.icon.type === opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__.ObjectType.Emoji ? page.icon.emoji : \"\\uD83D\\uDCD5\";\n            const item = {\n                color: undefined,\n                data: undefined,\n                id,\n                title: \"\".concat(emoji, \" \").concat(db.page.name),\n                value: id\n            };\n            items.push(item);\n        }\n        return items;\n    }, [\n        databasePageStore,\n        databasePagesId,\n        databaseId\n    ]);\n    if (!db) return null;\n    const { page } = db;\n    const emoji = page.icon && page.icon.type === opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__.ObjectType.Emoji ? page.icon.emoji : \"\\uD83D\\uDCD5\";\n    const title = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            emoji,\n            \" \\xa0 \",\n            db.page.name\n        ]\n    }, void 0, true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenu, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenuTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"outline\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_24__.cn)(\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start max-w-48 items-center\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.DatabaseIcon, {\n                                className: \"size-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate text-black font-medium\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenuContent, {\n                    className: \"p-0 rounded-none min-w-80\",\n                    align: \"start\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"db-select\",\n                                className: \"text-xs text-neutral-500\",\n                                children: \"Source database\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_26__.CustomSelect, {\n                                onChange: (v)=>{},\n                                disabled: true,\n                                selectedIds: [\n                                    databaseId\n                                ],\n                                placeholder: \"Choose a database\",\n                                options: dbItems\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 529,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s2(PageContextSourceDatabase, \"HF+HEFapgkk21Bf612NPlXB3zzU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace\n    ];\n});\n_c1 = PageContextSourceDatabase;\nconst PeekRecord = (props)=>{\n    _s3();\n    const { databaseStore, members, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const template = (0,_providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate)();\n    const shared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_39__.useMaybeShared)();\n    const canExpand = !shared && !template;\n    const database = databaseStore[props.databaseId];\n    const record = databaseStore[props.databaseId].recordsIdMap[props.recordId];\n    const onOpenChange = (o)=>{\n        if (!o) {\n            var _props_onClose;\n            (_props_onClose = props.onClose) === null || _props_onClose === void 0 ? void 0 : _props_onClose.call(props);\n        }\n    };\n    if (!database || !record) return null;\n    const recordInfo = record;\n    let processedRecord = null;\n    const persons = (0,_components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_35__.membersToPersons)(members);\n    const linkedDatabaseId = Object.values(database.database.definition.columnsMap).filter((c)=>c.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.DatabaseFieldDataType.Linked && c.databaseId).map((c)=>c.databaseId);\n    const linkedDatabases = {};\n    for (const id of linkedDatabaseId){\n        const db = databaseStore[id];\n        if (db) {\n            linkedDatabases[id] = {\n                id,\n                definition: db.database.definition,\n                recordsMap: {},\n                srcPackageName: db.database.srcPackageName\n            };\n            for (let r of Object.values(db.recordsIdMap)){\n                linkedDatabases[id].recordsMap[r.record.id] = r.record;\n            }\n        }\n    }\n    const records = [\n        recordInfo.record\n    ];\n    const processedRecords = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_36__.transformRawRecords)(database.database.definition, records, persons, linkedDatabases);\n    processedRecord = processedRecords[0];\n    let href = url(\"/databases/\".concat(record.record.databaseId, \"/records/\").concat(record.record.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.Sheet, {\n            defaultOpen: true,\n            onOpenChange: onOpenChange,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetTrigger, {\n                    asChild: true,\n                    children: props.trigger\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetContent, {\n                    className: \"!w-[50vw] !min-w-[400px] !max-w-full bg-white p-0 pt-8\",\n                    children: [\n                        canExpand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            asChild: true,\n                            className: \"absolute right-12 top-2.5 !size-6 !p-1.5 rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                href: href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpRightAndArrowDownLeftFromCenterIcon, {\n                                    className: \"size-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"size-full flex flex-col overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetHeader, {\n                                    className: \"hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetTitle, {\n                                            className: \"font-bold text-base\",\n                                            children: \"Peek Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetDescription, {\n                                            className: \"hidden\",\n                                            children: \"Make changes to your record here. Click save when you're done.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_recordTabViews__WEBPACK_IMPORTED_MODULE_44__.RecordTabViewsProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_record__WEBPACK_IMPORTED_MODULE_34__.RecordProvider, {\n                                            recordInfo: {\n                                                ...record,\n                                                processedRecord\n                                            },\n                                            database: database.database,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_recordExtras__WEBPACK_IMPORTED_MODULE_40__.RecordExtras, {\n                                                showOverview: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 25\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetFooter, {\n                                    className: \"hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetClose, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            type: \"submit\",\n                                            className: \"rounded-full\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 25\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 621,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s3(PeekRecord, \"AsUdGlLRyHFbDg8CUaGjnjZ3LoM=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_39__.useMaybeShared\n    ];\n});\n_c2 = PeekRecord;\nconst ScannableCodeScanner = (param)=>{\n    let { database, filter, viewFilter, onRecordScan } = param;\n    _s4();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert)();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const databaseStoreItem = databaseStore[database.id];\n    let hasScannableField = false;\n    for (let value of Object.values(database.definition.columnsMap)){\n        if (value.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.DatabaseFieldDataType.ScannableCode) {\n            hasScannableField = true;\n            break;\n        }\n    }\n    if (!hasScannableField) return null;\n    const onScan = (results)=>{\n        const detected = results[0];\n        if (!detected) {\n            toast.error(\"Nothing to search\");\n            return;\n        }\n        const rawValue = detected.rawValue;\n        if (!rawValue) {\n            toast.error(\"Nothing to search\");\n            return;\n        }\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__.filterAndSortRecords)(databaseStoreItem, members, databaseStore, viewFilter, filter, [], workspace.workspaceMember.userId);\n        console.log(\"Scanned value: \", {\n            rawValue,\n            rows\n        });\n        const searched = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__.searchFilteredRecords)(rawValue, rows);\n        if (!searched || searched.length === 0) {\n            toast.error(\"No records found\");\n            return;\n        }\n        onRecordScan(searched[0].record.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.Popover, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.PopoverTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"ghost\",\n                        className: \"text-xs rounded-full p-1 size-7 gap-2 justify-center items-center font-medium\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.BarcodeReadIcon, {\n                            className: \"size-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 733,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.PopoverContent, {\n                    className: \"w-80 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"mb-4 text-xs font-medium\",\n                                children: \"Find record by scanning barcode/QR code\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_yudiel_react_qr_scanner__WEBPACK_IMPORTED_MODULE_41__.Scanner, {\n                                allowMultiple: true,\n                                onScan: onScan\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 743,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 732,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s4(ScannableCodeScanner, \"ovSne5WK1YXbIK6ADZ04IdwnxrQ=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace\n    ];\n});\n_c3 = ScannableCodeScanner;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ViewsRootLayout\");\n$RefreshReg$(_c1, \"PageContextSourceDatabase\");\n$RefreshReg$(_c2, \"PeekRecord\");\n$RefreshReg$(_c3, \"ScannableCodeScanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/ViewsRootLayout.tsx\n"));

/***/ })

});