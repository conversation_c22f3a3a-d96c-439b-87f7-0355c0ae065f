"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Compute caret position relative to viewport.\n                let rect = currentRange.getBoundingClientRect();\n                // If the rectangle is all zeros, create a temporary marker to compute correct coordinates.\n                if (rect.width === 0 && rect.height === 0) {\n                    var _marker_parentNode;\n                    const marker = document.createElement(\"span\");\n                    marker.textContent = \"​\"; // zero width space\n                    currentRange.insertNode(marker);\n                    rect = marker.getBoundingClientRect();\n                    (_marker_parentNode = marker.parentNode) === null || _marker_parentNode === void 0 ? void 0 : _marker_parentNode.removeChild(marker);\n                    sel.removeAllRanges();\n                    sel.addRange(currentRange);\n                }\n            // Position will be calculated in useEffect to ensure proper positioning\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Smart positioning: check if dropdown should appear above or below cursor position\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current && atRange) {\n            const updatePosition = ()=>{\n                // Get cursor position from the stored range\n                let cursorRect = atRange.getBoundingClientRect();\n                // If the rectangle is empty, try to get a better position\n                if (cursorRect.width === 0 && cursorRect.height === 0) {\n                    const sel = window.getSelection();\n                    if (sel && sel.rangeCount > 0) {\n                        const currentRange = sel.getRangeAt(0);\n                        cursorRect = currentRange.getBoundingClientRect();\n                    }\n                }\n                // If still no good position, fall back to input element position\n                if (cursorRect.width === 0 && cursorRect.height === 0) {\n                    var _divRef_current;\n                    const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                    if (inputRect) {\n                        cursorRect = inputRect;\n                    }\n                }\n                const spaceBelow = window.innerHeight - cursorRect.bottom;\n                const spaceAbove = cursorRect.top;\n                const dropdownHeight = 250; // approximate max height of dropdown\n                // If not enough space below (less than dropdown height), try to show above\n                if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {\n                    setShowAbove(true);\n                } else {\n                    setShowAbove(false);\n                }\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode,\n        atRange\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 586,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"absolute z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: showAbove ? {\n                    bottom: \"100%\",\n                    left: \"0\",\n                    marginBottom: \"2px\"\n                } : {\n                    top: \"100%\",\n                    left: \"0\",\n                    marginTop: \"2px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 633,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 602,\n                columnNumber: 17\n            }, this),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 585,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"P2/CRSNwihOzgUKHan2mPDJ45XA=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbS11aS9tZW50aW9uSW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUM5QjtBQUM2RDtBQUM1QztBQUNBO0FBQ0U7QUFDZjtBQThCeEIsU0FBU1ksYUFBYSxLQWFvQjtRQWJwQixFQUNJQyxNQUFNLEVBQ05DLEtBQUssRUFDTEMsZUFBZSxFQUFFLEVBQ2pCQyxRQUFRLEVBQ1JDLE1BQU0sRUFDTkMsZ0JBQWdCLEVBQ2hCQyxvQkFBb0IsR0FBRyxFQUN2QkMsV0FBVyxFQUNYQyxTQUFTLEVBQ1RDLEVBQUUsRUFDRkMsV0FBVyxLQUFLLEVBQ2hCQyxjQUFjLEtBQUssRUFDSCxHQWJwQjtRQXltQm1EQzs7SUEzbEI1RSxNQUFNQyxTQUFTeEIsNkNBQU1BLENBQWlCO0lBRXRDLHVCQUF1QjtJQUN2QixNQUFNeUIsZ0JBQWdCekIsNkNBQU1BLENBQVM7SUFFckMsd0JBQXdCO0lBQ3hCLE1BQU0wQixtQkFBbUIxQiw2Q0FBTUEsQ0FBd0I7SUFHdkQsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQzJCLGFBQWFDLGVBQWUsR0FBRzNCLCtDQUFRQSxDQUFDLFFBQVEseUJBQXlCO0lBQ2hGLE1BQU0sQ0FBQzRCLGVBQWVDLGlCQUFpQixHQUFHN0IsK0NBQVFBLENBQUMsS0FBSyxxQ0FBcUM7SUFDN0YsTUFBTThCLFdBQVcvQiw2Q0FBTUEsQ0FBaUIsT0FBTyxzQkFBc0I7SUFDckUsTUFBTSxDQUFDZ0MsV0FBV0MsYUFBYSxHQUFHaEMsK0NBQVFBLENBQUMsUUFBUSx1Q0FBdUM7SUFFMUYsMERBQTBEO0lBQzFELE1BQU0sQ0FBQ2lDLFNBQVNDLFdBQVcsR0FBR2xDLCtDQUFRQSxDQUFlO0lBQ3JELDBDQUEwQztJQUMxQyxNQUFNbUMsa0JBQWtCcEMsNkNBQU1BLENBQW1CO0lBRWpELHlGQUF5RjtJQUN6RixNQUFNcUMsc0JBQXNCckMsNkNBQU1BLENBQUM7SUFFbkMseURBQXlEO0lBQ3pELE1BQU0sQ0FBQ3VCLFlBQVllLGNBQWMsR0FBR3JDLCtDQUFRQSxDQUFvQjtRQUM1RHNDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLGdCQUFnQjtJQUNwQjtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUczQywrQ0FBUUEsQ0FBQztJQUlqRCxzRUFBc0U7SUFFdEUsdURBQXVEO0lBQ3ZELFNBQVM0QyxnQkFBZ0JDLElBQVk7UUFDakNBLE9BQU9DLE9BQU9ELFFBQVE7UUFDdEIsTUFBTUUsZUFBZTtRQUNyQixJQUFJQyxZQUFZO1FBQ2hCLElBQUlDLGFBQWE7UUFFakIsS0FBSyxNQUFNQyxTQUFTTCxLQUFLTSxRQUFRLENBQUNKLGNBQWU7Z0JBQy9CRztZQUFkLE1BQU1FLFFBQVFGLENBQUFBLGVBQUFBLE1BQU1HLEtBQUssY0FBWEgsMEJBQUFBLGVBQWU7WUFDN0IsSUFBSUUsUUFBUUosV0FBVztnQkFDbkIsSUFBSU0sUUFBUUMsU0FBU1YsS0FBS1csS0FBSyxDQUFDUixXQUFXSTtnQkFDM0MsSUFBSS9CLGFBQWE7b0JBQ2JpQyxRQUFRQSxNQUFNRyxPQUFPLENBQUMsT0FBTztnQkFDakM7Z0JBQ0FSLGNBQWNLO1lBQ2xCO1lBRUEsTUFBTWQsYUFBYVUsS0FBSyxDQUFDLEVBQUU7WUFDM0IsTUFBTVEsaUJBQWlCUixLQUFLLENBQUMsRUFBRSxJQUFJO1lBRW5DLGtDQUFrQztZQUNsQyxNQUFNUyxZQUFZQyx1QkFBdUJsRCxRQUFROEI7WUFDakQsTUFBTXFCLE9BQU9GLFlBQVlqRCxNQUFNLENBQUNpRCxVQUFVLEdBQUc7WUFFN0MsSUFBSUUsTUFBTTtnQkFDTixJQUFJQyxRQUFRRCxLQUFLQyxLQUFLO2dCQUN0QixJQUFJSixlQUFlSyxJQUFJLE9BQU8sSUFBSTtvQkFDOUJELFNBQVMsTUFBcUIsT0FBZko7Z0JBQ25CO2dCQUVBLE1BQU1NLFlBQ0ZOLGVBQWVLLElBQUksT0FBTyxLQUNwQixLQUFlLE9BQVZKLFdBQVUsUUFDZixLQUFrQkQsT0FBYkMsV0FBVSxLQUFrQixPQUFmRCxnQkFBZTtnQkFFM0MsTUFBTU8sV0FBVywyQ0FBcUVWLE9BQTFCVyxXQUFXRixZQUFXLE1BQW9CLE9BQWhCVCxTQUFTTyxRQUFPO2dCQUN0R2IsY0FBY2dCO1lBQ2xCLE9BQU87Z0JBQ0hoQixjQUFjTSxTQUFTTCxLQUFLLENBQUMsRUFBRTtZQUNuQztZQUVBRixZQUFZSSxRQUFRRixLQUFLLENBQUMsRUFBRSxDQUFDaUIsTUFBTTtRQUN2QztRQUVBLElBQUluQixZQUFZSCxLQUFLc0IsTUFBTSxFQUFFO1lBQ3pCLElBQUliLFFBQVFDLFNBQVNWLEtBQUtXLEtBQUssQ0FBQ1I7WUFDaEMsSUFBSTNCLGFBQWE7Z0JBQ2JpQyxRQUFRQSxNQUFNRyxPQUFPLENBQUMsT0FBTztZQUNqQztZQUNBUixjQUFjSztRQUNsQjtRQUVBLE9BQU9MO0lBQ1g7SUFFQSxrREFBa0Q7SUFDbEQsU0FBU1csdUJBQXVCUSxHQUF3QixFQUFFQyxHQUFXO1FBQ2pFLE1BQU1DLFdBQVdELElBQUlFLFdBQVc7UUFDaEMsS0FBSyxNQUFNQyxLQUFLQyxPQUFPQyxJQUFJLENBQUNOLEtBQU07WUFDOUIsSUFBSUksRUFBRUQsV0FBVyxPQUFPRCxVQUFVO2dCQUM5QixPQUFPRTtZQUNYO1FBQ0o7UUFDQSxPQUFPO0lBQ1g7SUFFQSxTQUFTakIsU0FBU29CLEdBQVc7UUFDekIsT0FBT0EsSUFDRmxCLE9BQU8sQ0FBQyxNQUFNLFNBQ2RBLE9BQU8sQ0FBQyxNQUFNLFFBQ2RBLE9BQU8sQ0FBQyxNQUFNO0lBQ3ZCO0lBRUEsU0FBU1MsV0FBV1MsR0FBVztRQUMzQixPQUFPQSxJQUFJbEIsT0FBTyxDQUFDLE1BQU0sVUFBVUEsT0FBTyxDQUFDLE1BQU07SUFDckQ7SUFFQSwwRUFBMEU7SUFDMUUzRCxnREFBU0EsQ0FBQztRQUNOLElBQUlhLFVBQVVpRSxhQUFhaEUsaUJBQWlCZ0UsYUFBYXJELE9BQU9zRCxPQUFPLElBQUksQ0FBQ3pDLG9CQUFvQnlDLE9BQU8sRUFBRTtZQUNyRyxNQUFNQyxjQUFjdkQsT0FBT3NELE9BQU8sQ0FBQ0UsU0FBUztZQUM1QyxNQUFNQyxVQUFVcEMsZ0JBQWdCaEMsZ0JBQWdCO1lBQ2hELElBQUlrRSxnQkFBZ0JFLFNBQVM7Z0JBQ3pCekQsT0FBT3NELE9BQU8sQ0FBQ0UsU0FBUyxHQUFHQztZQUMvQjtRQUNKO0lBQ0osR0FBRztRQUFDcEU7UUFBY0Q7UUFBT0Q7S0FBTztJQUVoQyw0REFBNEQ7SUFDNUQsMEVBQTBFO0lBQzFFWixnREFBU0EsQ0FBQztRQUNOLElBQUlhLFVBQVVpRSxhQUFhckQsT0FBT3NELE9BQU8sSUFBSSxDQUFDekMsb0JBQW9CeUMsT0FBTyxFQUFFO1lBQ3ZFLE1BQU1DLGNBQWN2RCxPQUFPc0QsT0FBTyxDQUFDRSxTQUFTO1lBQzVDLE1BQU1DLFVBQVVwQyxnQkFBZ0JqQztZQUNoQyxtRkFBbUY7WUFDbkYsSUFBSXNFLFNBQVNDLGFBQWEsS0FBSzNELE9BQU9zRCxPQUFPLElBQUlDLGdCQUFnQkUsU0FBUztnQkFDdEV6RCxPQUFPc0QsT0FBTyxDQUFDRSxTQUFTLEdBQUdDO1lBQy9CO1FBQ0o7SUFDSixHQUFHO1FBQUNyRTtRQUFPRDtLQUFPO0lBRWxCLGlEQUFpRDtJQUNqRCxTQUFTeUU7UUFDTCxJQUFJLENBQUM1RCxPQUFPc0QsT0FBTyxFQUFFLE9BQU87UUFDNUIsSUFBSU8sU0FBUztRQUViLFNBQVNDLFNBQVNDLElBQVU7WUFDeEIsSUFBSUEsS0FBS0MsUUFBUSxLQUFLQyxLQUFLQyxTQUFTLEVBQUU7Z0JBQ2xDTCxVQUFVRSxLQUFLSSxTQUFTLElBQUk7WUFDaEMsT0FBTyxJQUFJSixLQUFLQyxRQUFRLEtBQUtDLEtBQUtHLFlBQVksRUFBRTtnQkFDNUMsTUFBTUMsS0FBS047Z0JBQ1gsSUFBSU0sR0FBR0MsWUFBWSxDQUFDLGFBQWE7b0JBQzdCVCxVQUFVUSxHQUFHRSxZQUFZLENBQUM7Z0JBQzlCLE9BQU8sSUFBSUYsR0FBR0csUUFBUSxLQUFLLE1BQU07b0JBQzdCLElBQUkxRSxhQUFhK0QsVUFBVTtnQkFDL0IsT0FBTztvQkFDSFksTUFBTUMsSUFBSSxDQUFDTCxHQUFHTSxVQUFVLEVBQUVDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU2YsU0FBU2U7b0JBQ3BELElBQUkvRSxlQUFnQnVFLENBQUFBLEdBQUdHLFFBQVEsS0FBSyxTQUFTSCxHQUFHRyxRQUFRLEtBQUssR0FBRSxHQUFJO3dCQUMvRFgsVUFBVTtvQkFDZDtnQkFDSjtZQUNKO1FBQ0o7UUFFQVksTUFBTUMsSUFBSSxDQUFDMUUsT0FBT3NELE9BQU8sQ0FBQ3FCLFVBQVUsRUFBRUMsT0FBTyxDQUFDQyxDQUFBQSxRQUFTZixTQUFTZTtRQUNoRSxPQUFPaEI7SUFDWDtJQUVBLGdEQUFnRDtJQUNoRCxTQUFTaUI7UUFDTCxNQUFNQyxTQUFTbkI7UUFDZjNELGNBQWNxRCxPQUFPLEdBQUd5QjtRQUN4QnpGLHFCQUFBQSwrQkFBQUEsU0FBV3lGO1FBRVgsMEJBQTBCO1FBQzFCLElBQUl2RixrQkFBa0I7WUFDbEIsSUFBSVUsaUJBQWlCb0QsT0FBTyxFQUFFO2dCQUMxQjBCLGFBQWE5RSxpQkFBaUJvRCxPQUFPO1lBQ3pDO1lBRUFwRCxpQkFBaUJvRCxPQUFPLEdBQUcyQixXQUFXO2dCQUNsQ3pGLGlCQUFpQnVGO2dCQUNqQjdFLGlCQUFpQm9ELE9BQU8sR0FBRztZQUMvQixHQUFHN0Q7UUFDUDtJQUNKO0lBRUEsb0JBQW9CO0lBQ3BCLFNBQVN5RjtRQUNMLElBQUlyRixVQUFVO1FBQ2QsSUFBSU0sYUFBYTtRQUNqQlosbUJBQUFBLDZCQUFBQSxPQUFTVSxjQUFjcUQsT0FBTyxJQUFJTTtJQUN0QztJQUVBLFNBQVN1QjtRQUNMLElBQUl0RixVQUFVO1FBQ2RpRjtJQUNKO0lBRUEsMEVBQTBFO0lBQzFFLHNCQUFzQjtJQUN0QiwwRUFBMEU7SUFFMUUsK0VBQStFO0lBQy9FLFNBQVNNLFVBQVVDLENBQXNDO1FBQ3JELElBQUl4RixVQUFVO1FBQ2QsSUFBSXdGLEVBQUV2QyxHQUFHLEtBQUssS0FBSztnQkFDSHdDLHNCQUFBQTtZQUFaLE1BQU1DLE9BQU1ELHVCQUFBQSxDQUFBQSxVQUFBQSxRQUFPRSxZQUFZLGNBQW5CRiwyQ0FBQUEsMEJBQUFBO1lBQ1osSUFBSUMsT0FBT0EsSUFBSUUsVUFBVSxHQUFHLEdBQUc7Z0JBQzNCLDREQUE0RDtnQkFDNUQsTUFBTUMsZUFBZUgsSUFBSUksVUFBVSxDQUFDLEdBQUdDLFVBQVU7Z0JBQ2pEakYsV0FBVytFO2dCQUNYLCtDQUErQztnQkFDL0MsSUFBSUcsT0FBT0gsYUFBYUkscUJBQXFCO2dCQUM3QywyRkFBMkY7Z0JBQzNGLElBQUlELEtBQUtFLEtBQUssS0FBSyxLQUFLRixLQUFLRyxNQUFNLEtBQUssR0FBRzt3QkFLdkNDO29CQUpBLE1BQU1BLFNBQVN2QyxTQUFTd0MsYUFBYSxDQUFDO29CQUN0Q0QsT0FBT0UsV0FBVyxHQUFHLEtBQVUsbUJBQW1CO29CQUNsRFQsYUFBYVUsVUFBVSxDQUFDSDtvQkFDeEJKLE9BQU9JLE9BQU9ILHFCQUFxQjtxQkFDbkNHLHFCQUFBQSxPQUFPSSxVQUFVLGNBQWpCSix5Q0FBQUEsbUJBQW1CSyxXQUFXLENBQUNMO29CQUMvQlYsSUFBSWdCLGVBQWU7b0JBQ25CaEIsSUFBSWlCLFFBQVEsQ0FBQ2Q7Z0JBQ2pCO1lBRUEsd0VBQXdFO1lBQzVFO1lBQ0F0RixlQUFlO1lBQ2ZFLGlCQUFpQjtRQUNyQjtJQUNKO0lBRUEsMkJBQTJCO0lBQzNCLFNBQVNtRztRQUNMckcsZUFBZTtRQUNmRSxpQkFBaUI7UUFDakJLLFdBQVc7SUFDZjtJQUVBLDREQUE0RDtJQUM1RCxTQUFTK0YsWUFBWUMsS0FBYSxFQUFFQyxLQUFlO1FBQy9DLElBQUksQ0FBQ0QsTUFBTW5FLElBQUksSUFBSSxPQUFPb0UsTUFBTTNFLEtBQUssQ0FBQyxHQUFHO1FBQ3pDLE1BQU00RSxrQkFBa0JGLE1BQU16RSxPQUFPLENBQUMsUUFBUSxJQUFJYyxXQUFXO1FBRzdELE1BQU04RCxVQUFvQixFQUFFO1FBRTVCLEtBQUssTUFBTWhFLE9BQU84RCxNQUFPO1lBQ3JCLE1BQU10RSxPQUFPbkQsTUFBTSxDQUFDMkQsSUFBSTtZQUN4QixNQUFNaUUsZ0JBQWdCakUsSUFBSVosT0FBTyxDQUFDLFFBQVEsSUFBSWMsV0FBVztZQUN6RCxNQUFNZ0Usa0JBQWtCMUUsS0FBS0MsS0FBSyxDQUFDTCxPQUFPLENBQUMsUUFBUSxJQUFJYyxXQUFXO1lBRWxFLHFDQUFxQztZQUNyQywyQkFBMkI7WUFDM0IsNkJBQTZCO1lBQzdCLHdCQUF3QjtZQUN4QiwwQkFBMEI7WUFFMUIsSUFBSStELGNBQWNFLFVBQVUsQ0FBQ0osa0JBQWtCO2dCQUMzQ0MsUUFBUUksSUFBSSxDQUFDO29CQUFDQyxNQUFNckU7b0JBQUtzRSxNQUFNO2dCQUFDO1lBQ3BDLE9BQU8sSUFBSUosZ0JBQWdCQyxVQUFVLENBQUNKLGtCQUFrQjtnQkFDcERDLFFBQVFJLElBQUksQ0FBQztvQkFBQ0MsTUFBTXJFO29CQUFLc0UsTUFBTTtnQkFBQztZQUNwQyxPQUFPLElBQUlMLGNBQWNNLFFBQVEsQ0FBQ1Isa0JBQWtCO2dCQUNoREMsUUFBUUksSUFBSSxDQUFDO29CQUFDQyxNQUFNckU7b0JBQUtzRSxNQUFNO2dCQUFDO1lBQ3BDLE9BQU8sSUFBSUosZ0JBQWdCSyxRQUFRLENBQUNSLGtCQUFrQjtnQkFDbERDLFFBQVFJLElBQUksQ0FBQztvQkFBQ0MsTUFBTXJFO29CQUFLc0UsTUFBTTtnQkFBQztZQUNwQztRQUNKO1FBRUFOLFFBQVFRLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFSCxJQUFJLEdBQUdJLEVBQUVKLElBQUk7UUFDdEMsT0FBT04sUUFBUVcsR0FBRyxDQUFDLENBQUNDLElBQU1BLEVBQUVQLElBQUksRUFBRWxGLEtBQUssQ0FBQyxHQUFHO0lBQy9DO0lBRUEsTUFBTTBGLFVBQVV6RSxPQUFPQyxJQUFJLENBQUNoRTtJQUM1QixNQUFNeUksaUJBQWlCekgsY0FBY3VHLFlBQVlyRyxlQUFlc0gsV0FBVyxFQUFFO0lBRTdFLDZDQUE2QztJQUM3QyxTQUFTRSxnQkFBZ0I1RyxVQUFrQjtZQTBFdkNEO1FBekVBLElBQUksQ0FBQ2hCLE9BQU9zRCxPQUFPLEVBQUU7UUFDckIsTUFBTWlDLE1BQU1ELE9BQU9FLFlBQVk7UUFDL0IsSUFBSSxDQUFDRCxLQUFLO1lBQ05rQjtZQUNBO1FBQ0o7UUFDQSw0Q0FBNEM7UUFDNUMsSUFBSXFCLFFBQXNCcEgsV0FBWTZFLENBQUFBLElBQUlFLFVBQVUsR0FBRyxJQUFJRixJQUFJSSxVQUFVLENBQUMsS0FBSyxJQUFHO1FBQ2xGLElBQUksQ0FBQ21DLE9BQU87WUFDUnJCO1lBQ0E7UUFDSjtRQUVBLDZFQUE2RTtRQUM3RSxTQUFTc0IscUJBQXFCQyxRQUFjLEVBQUVDLEdBQVc7WUFDckQsTUFBTTNHLE9BQU8wRyxTQUFTRSxJQUFJO1lBQzFCLE1BQU1DLGNBQWNDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHSixNQUFNO1lBQ3RDLE1BQU1LLFlBQVlGLEtBQUtHLEdBQUcsQ0FBQ2pILEtBQUtzQixNQUFNLEVBQUVxRixNQUFNO1lBQzlDLElBQUssSUFBSU8sSUFBSUYsWUFBWSxHQUFHRSxLQUFLTCxhQUFhSyxJQUFLO2dCQUMvQyxJQUFJbEgsS0FBS21ILE1BQU0sQ0FBQ0QsT0FBTyxLQUFLO29CQUN4QlIsU0FBU0UsSUFBSSxHQUFHNUcsS0FBS29ILFNBQVMsQ0FBQyxHQUFHRixLQUFLbEgsS0FBS29ILFNBQVMsQ0FBQ0YsSUFBSTtvQkFDMUQsTUFBTUcsV0FBV2pGLFNBQVNrRixXQUFXO29CQUNyQ0QsU0FBU0UsUUFBUSxDQUFDYixVQUFVUTtvQkFDNUJHLFNBQVNHLFFBQVEsQ0FBQztvQkFDbEIsT0FBT0g7Z0JBQ1g7WUFDSjtZQUNBLE9BQU87UUFDWDtRQUVBLGdEQUFnRDtRQUNoRCxJQUFJYixNQUFNaUIsY0FBYyxDQUFDL0UsUUFBUSxLQUFLQyxLQUFLQyxTQUFTLEVBQUU7WUFDbEQsTUFBTThELFdBQVdGLE1BQU1pQixjQUFjO1lBQ3JDLE1BQU1kLE1BQU1ILE1BQU1rQixXQUFXO1lBQzdCLE1BQU1DLFNBQVNsQixxQkFBcUJDLFVBQVVDO1lBQzlDLElBQUlnQixRQUFRO2dCQUNSbkIsUUFBUW1CO1lBQ1o7UUFDSixPQUFPO1lBQ0gsbUZBQW1GO1lBQ25GLE1BQU1DLFlBQVlwQixNQUFNaUIsY0FBYztZQUN0QyxJQUFJRyxVQUFVdkUsVUFBVSxDQUFDL0IsTUFBTSxHQUFHLEtBQUtrRixNQUFNa0IsV0FBVyxHQUFHLEdBQUc7Z0JBQzFELE1BQU1HLE9BQU9ELFVBQVV2RSxVQUFVLENBQUNtRCxNQUFNa0IsV0FBVyxHQUFHLEVBQUU7Z0JBQ3hELElBQUlHLFFBQVFBLEtBQUtuRixRQUFRLEtBQUtDLEtBQUtDLFNBQVMsRUFBRTtvQkFDMUMsTUFBTThELFdBQVdtQjtvQkFDakIsTUFBTUYsU0FBU2xCLHFCQUFxQkMsVUFBVUEsU0FBU0UsSUFBSSxDQUFDdEYsTUFBTTtvQkFDbEUsSUFBSXFHLFFBQVE7d0JBQ1JuQixRQUFRbUI7b0JBQ1o7Z0JBQ0o7WUFDSjtRQUNKO1FBRUEsTUFBTTNHLE9BQU9uRCxNQUFNLENBQUM4QixXQUFXO1FBQy9CLElBQUksQ0FBQ3FCLE1BQU07WUFDUG1FO1lBQ0E7UUFDSjtRQUVBLHNDQUFzQztRQUN0QyxNQUFNekYsT0FBTzBDLFNBQVN3QyxhQUFhLENBQUM7UUFDcENsRixLQUFLb0ksZUFBZSxHQUFHO1FBQ3ZCcEksS0FBS3FJLFlBQVksQ0FBQyxZQUFZL0csS0FBS2dILEdBQUc7UUFDdEN0SSxLQUFLbUYsV0FBVyxHQUFHN0QsS0FBS0MsS0FBSztRQUM3QnZCLEtBQUt1SSxVQUFVLEdBQUc7WUFDZEMsaUJBQWlCeEk7UUFDckI7UUFFQUgsb0JBQW9CeUMsT0FBTyxHQUFHO1FBQzlCd0UsTUFBTTFCLFVBQVUsQ0FBQ3BGO1FBRWpCLGdFQUFnRTtRQUNoRSxNQUFNeUksU0FBUy9GLFNBQVNnRyxjQUFjLENBQUM7U0FDdkMxSSxtQkFBQUEsS0FBS3FGLFVBQVUsY0FBZnJGLHVDQUFBQSxpQkFBaUIySSxZQUFZLENBQUNGLFFBQVF6SSxLQUFLNEksV0FBVztRQUV0RCw2Q0FBNkM7UUFDN0MsTUFBTWpCLFdBQVdqRixTQUFTa0YsV0FBVztRQUNyQ0QsU0FBU2tCLGFBQWEsQ0FBQ0o7UUFDdkJkLFNBQVNHLFFBQVEsQ0FBQztRQUNsQnZELElBQUlnQixlQUFlO1FBQ25CaEIsSUFBSWlCLFFBQVEsQ0FBQ21DO1FBRWIzSSxPQUFPc0QsT0FBTyxDQUFDd0csS0FBSztRQUNwQnJEO1FBQ0EzQjtRQUVBRyxXQUFXO1lBQ1BwRSxvQkFBb0J5QyxPQUFPLEdBQUc7UUFDbEMsR0FBRztJQUNQO0lBRUEsbURBQW1EO0lBQ25EL0UsZ0RBQVNBLENBQUM7UUFDTixTQUFTd0wsTUFBTTFFLENBQWdCO1lBQzNCLElBQUlBLEVBQUV2QyxHQUFHLEtBQUssVUFBVTtnQkFDcEIyRDtZQUNKO1FBQ0o7UUFFQSxTQUFTdUQsZUFBZTNFLENBQWE7WUFDakMsTUFBTTRFLFNBQVM1RSxFQUFFNEUsTUFBTTtZQUN2QiwrREFBK0Q7WUFDL0QsSUFBSTFKLFNBQVMrQyxPQUFPLElBQUksQ0FBQy9DLFNBQVMrQyxPQUFPLENBQUM0RyxRQUFRLENBQUNELFdBQy9DakssT0FBT3NELE9BQU8sSUFBSSxDQUFDdEQsT0FBT3NELE9BQU8sQ0FBQzRHLFFBQVEsQ0FBQ0QsU0FBUztnQkFDcER4RDtZQUNKO1FBQ0o7UUFFQSxJQUFJdEcsYUFBYTtZQUNidUQsU0FBU3lHLGdCQUFnQixDQUFDLFdBQVdKO1lBQ3JDckcsU0FBU3lHLGdCQUFnQixDQUFDLGFBQWFIO1FBQzNDO1FBQ0EsT0FBTztZQUNIdEcsU0FBUzBHLG1CQUFtQixDQUFDLFdBQVdMO1lBQ3hDckcsU0FBUzBHLG1CQUFtQixDQUFDLGFBQWFKO1FBQzlDO0lBQ0osR0FBRztRQUFDN0o7S0FBWTtJQUVoQiwwREFBMEQ7SUFDMUQ1QixnREFBU0EsQ0FBQztRQUNOLElBQUk0QixhQUFhO1lBQ2JrSyxzQkFBc0I7b0JBQ2xCeko7aUJBQUFBLDJCQUFBQSxnQkFBZ0IwQyxPQUFPLGNBQXZCMUMsK0NBQUFBLHlCQUF5QmtKLEtBQUs7WUFDbEM7UUFDSjtJQUNKLEdBQUc7UUFBQzNKO0tBQVk7SUFFaEIsb0ZBQW9GO0lBQ3BGNUIsZ0RBQVNBLENBQUM7UUFDTixJQUFJNEIsZUFBZUgsT0FBT3NELE9BQU8sSUFBSTVDLFNBQVM7WUFDMUMsTUFBTTRKLGlCQUFpQjtnQkFDbkIsNENBQTRDO2dCQUM1QyxJQUFJQyxhQUFhN0osUUFBUW9GLHFCQUFxQjtnQkFFOUMsMERBQTBEO2dCQUMxRCxJQUFJeUUsV0FBV3hFLEtBQUssS0FBSyxLQUFLd0UsV0FBV3ZFLE1BQU0sS0FBSyxHQUFHO29CQUNuRCxNQUFNVCxNQUFNRCxPQUFPRSxZQUFZO29CQUMvQixJQUFJRCxPQUFPQSxJQUFJRSxVQUFVLEdBQUcsR0FBRzt3QkFDM0IsTUFBTUMsZUFBZUgsSUFBSUksVUFBVSxDQUFDO3dCQUNwQzRFLGFBQWE3RSxhQUFhSSxxQkFBcUI7b0JBQ25EO2dCQUNKO2dCQUVBLGlFQUFpRTtnQkFDakUsSUFBSXlFLFdBQVd4RSxLQUFLLEtBQUssS0FBS3dFLFdBQVd2RSxNQUFNLEtBQUssR0FBRzt3QkFDakNoRztvQkFBbEIsTUFBTXdLLGFBQVl4SyxrQkFBQUEsT0FBT3NELE9BQU8sY0FBZHRELHNDQUFBQSxnQkFBZ0I4RixxQkFBcUI7b0JBQ3ZELElBQUkwRSxXQUFXO3dCQUNYRCxhQUFhQztvQkFDakI7Z0JBQ0o7Z0JBRUEsTUFBTUMsYUFBYW5GLE9BQU9vRixXQUFXLEdBQUdILFdBQVdJLE1BQU07Z0JBQ3pELE1BQU1DLGFBQWFMLFdBQVdNLEdBQUc7Z0JBQ2pDLE1BQU1DLGlCQUFpQixLQUFLLHFDQUFxQztnQkFFakUsMkVBQTJFO2dCQUMzRSxJQUFJTCxhQUFhSyxrQkFBa0JGLGFBQWFFLGdCQUFnQjtvQkFDNURySyxhQUFhO2dCQUNqQixPQUFPO29CQUNIQSxhQUFhO2dCQUNqQjtZQUNKO1lBRUE2SjtZQUVBLDZDQUE2QztZQUM3Q2hGLE9BQU82RSxnQkFBZ0IsQ0FBQyxVQUFVRztZQUNsQ2hGLE9BQU82RSxnQkFBZ0IsQ0FBQyxVQUFVRyxnQkFBZ0I7WUFFbEQsT0FBTztnQkFDSGhGLE9BQU84RSxtQkFBbUIsQ0FBQyxVQUFVRTtnQkFDckNoRixPQUFPOEUsbUJBQW1CLENBQUMsVUFBVUUsZ0JBQWdCO1lBQ3pEO1FBQ0o7SUFDSixHQUFHO1FBQUNuSztRQUFhTztLQUFRO0lBSXpCLDBFQUEwRTtJQUMxRSw0REFBNEQ7SUFDNUQsMEVBQTBFO0lBRTFFLFNBQVM4SSxpQkFBaUJ4SSxJQUFxQjtRQUMzQyxJQUFJbkIsVUFBVTtRQUNkLE1BQU1rTCxlQUFlL0osS0FBS3VELFlBQVksQ0FBQyxlQUFlO1FBQ3RELE1BQU15RyxVQUFVO1FBQ2hCLE1BQU1ySixRQUFRb0osYUFBYXBKLEtBQUssQ0FBQ3FKO1FBQ2pDLElBQUksQ0FBQ3JKLE9BQU87UUFFWixNQUFNVixhQUFhVSxLQUFLLENBQUMsRUFBRTtRQUMzQixNQUFNc0osa0JBQWtCdEosS0FBSyxDQUFDLEVBQUUsSUFBSTtRQUNwQ2IsY0FBYztZQUNWQyxRQUFRO1lBQ1JDO1lBQ0FDO1lBQ0FDLGdCQUFnQitKO1FBQ3BCO1FBQ0E3SixnQkFBZ0I2SjtJQUNwQjtJQUVBLFNBQVNDO1FBQ0wsTUFBTSxFQUFDbEssSUFBSSxFQUFFQyxVQUFVLEVBQUMsR0FBR2xCO1FBQzNCLElBQUksQ0FBQ2lCLE1BQU07WUFDUG1LO1lBQ0E7UUFDSjtRQUNBLE1BQU03SSxPQUFPbkQsTUFBTSxDQUFDOEIsV0FBVztRQUMvQixJQUFJLENBQUNxQixNQUFNO1lBQ1A2STtZQUNBO1FBQ0o7UUFFQSxNQUFNQyxjQUFjakssYUFBYXFCLElBQUk7UUFDckMsSUFBSTZJLFdBQVcsS0FBZ0IsT0FBWHBLLFlBQVc7UUFDL0IsSUFBSXFLLFdBQVdoSixLQUFLQyxLQUFLO1FBQ3pCLElBQUk2SSxnQkFBZ0IsSUFBSTtZQUNwQkMsV0FBVyxLQUFtQkQsT0FBZG5LLFlBQVcsS0FBZSxPQUFabUssYUFBWTtZQUMxQ0UsV0FBVyxHQUFtQkYsT0FBaEI5SSxLQUFLQyxLQUFLLEVBQUMsT0FBaUIsT0FBWjZJO1FBQ2xDO1FBRUFwSyxLQUFLcUksWUFBWSxDQUFDLFlBQVlnQztRQUM5QnJLLEtBQUttRixXQUFXLEdBQUdtRjtRQUNuQnRLLEtBQUt1SSxVQUFVLEdBQUc7WUFDZEMsaUJBQWlCeEk7UUFDckI7UUFFQThEO1FBQ0FxRztJQUNKO0lBRUEsU0FBU0E7UUFDTHJLLGNBQWM7WUFDVkMsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ3BCO0lBQ0o7SUFFQSxpRUFBaUU7SUFDakUzQyxnREFBU0EsQ0FBQztRQUNOLElBQUksQ0FBQ3lCLE9BQU9zRCxPQUFPLElBQUl6RCxVQUFVO1FBQ2pDLE1BQU0wTCxlQUFldkwsT0FBT3NELE9BQU8sQ0FBQ2tJLGdCQUFnQixDQUFDO1FBQ3JERCxhQUFhM0csT0FBTyxDQUFDLENBQUNQO1lBQ2xCLE1BQU1yRCxPQUFPcUQ7WUFDYixJQUFJLENBQUNyRCxLQUFLdUksVUFBVSxFQUFFO2dCQUNsQnZJLEtBQUt1SSxVQUFVLEdBQUc7b0JBQ2RDLGlCQUFpQnhJO2dCQUNyQjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUNuQjtLQUFTO0lBRWIsMEVBQTBFO0lBQzFFLFNBQVM7SUFDVCwwRUFBMEU7SUFFMUUsd0RBQXdEO0lBRXhELHFCQUNJLDhEQUFDNEw7UUFBSTlMLFdBQVU7OzBCQUNYLDhEQUFDOEw7Z0JBQ0c3TCxJQUFJQTtnQkFDSjhMLEtBQUsxTDtnQkFDTCxnSEFBZ0g7Z0JBQ2hITCxXQUFXViw4Q0FBRUEsQ0FBRSwySkFBMEpVO2dCQUN6S3lKLGlCQUFpQixDQUFDdko7Z0JBQ2xCOEwsT0FBTztvQkFBQ0MsWUFBWTtnQkFBVTtnQkFDOUJDLDhCQUE4QjtnQkFDOUJDLG9CQUFrQixDQUFDak0sV0FBV0gsY0FBYztnQkFDNUN5RixTQUFTQTtnQkFDVEMsV0FBV0E7Z0JBQ1g3RixRQUFRMkY7Z0JBQ1I2RyxpQkFBZWxNOzs7Ozs7WUFHbEJNLGVBQWUsQ0FBQ04sMEJBQ2IsOERBQUM0TDtnQkFDR0MsS0FBS25MO2dCQUNMWixXQUFVO2dCQUNWZ00sT0FBT25MLFlBQVk7b0JBQ2ZtSyxRQUFRO29CQUNScUIsTUFBTTtvQkFDTkMsY0FBYztnQkFDbEIsSUFBSTtvQkFDQXBCLEtBQUs7b0JBQ0xtQixNQUFNO29CQUNORSxXQUFXO2dCQUNmOzBCQUNBLDRFQUFDVDtvQkFBSTlMLFdBQVU7O3NDQUNYLDhEQUFDOEw7NEJBQUk5TCxXQUFVO3NDQUNYLDRFQUFDd007Z0NBQ0dULEtBQUs5SztnQ0FDTGpCLFdBQVU7Z0NBQ1ZELGFBQVk7Z0NBQ1pOLE9BQU9pQjtnQ0FDUGYsVUFBVSxDQUFDK0YsSUFBTS9FLGlCQUFpQitFLEVBQUU0RSxNQUFNLENBQUM3SyxLQUFLOzs7Ozs7Ozs7OztzQ0FHeEQsOERBQUNxTTs0QkFBSTlMLFdBQVU7c0NBQ1ZpSSxlQUFlaEYsTUFBTSxLQUFLLGtCQUN2Qiw4REFBQzZJO2dDQUFJOUwsV0FBVTswQ0FBb0Q7Ozs7O3VDQUlsRWlJLGVBQWVILEdBQUcsQ0FBQyxDQUFDMkU7Z0NBQ2hCLE1BQU05SixPQUFPbkQsTUFBTSxDQUFDaU4sS0FBSztnQ0FDekIscUJBQ0ksOERBQUNYO29DQUVHOUwsV0FBVTtvQ0FDVjBNLGFBQWEsQ0FBQ2hIO3dDQUNWQSxFQUFFaUgsY0FBYzt3Q0FDaEJ6RSxnQkFBZ0J1RTtvQ0FDcEI7OENBQ0M5SixLQUFLQyxLQUFLO21DQU5ONko7Ozs7OzRCQVNqQjs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPcEJyTSxXQUFXZ0IsTUFBTSxJQUFJLENBQUNsQiwwQkFBWTswQkFDL0IsNEVBQUNuQix5REFBTUE7b0JBQUM2TixXQUFXO29CQUFDQyxjQUFjckI7OEJBQzlCLDRFQUFDeE0sZ0VBQWFBO3dCQUFDZ0IsV0FBVTs7MENBQ3JCLDhEQUFDZiwrREFBWUE7MENBQ1QsNEVBQUNDLDhEQUFXQTtvQ0FBQ2MsV0FBVTs7d0NBQVk7d0NBQWlCSSxFQUFBQSxtQkFBQUEsV0FBV2lCLElBQUksY0FBZmpCLHVDQUFBQSxpQkFBaUIwTSxTQUFTLEtBQUkxTSxXQUFXa0IsVUFBVTs7Ozs7Ozs7Ozs7OzBDQUUzRyw4REFBQ3dLO2dDQUFJOUwsV0FBVTs7a0RBQ1gsOERBQUM4TDt3Q0FBSTlMLFdBQVU7OzBEQUNYLDhEQUFDYix1REFBS0E7Z0RBQUNhLFdBQVU7O29EQUE4QztvREFDeENJLFdBQVdtQixjQUFjO29EQUFDOzs7Ozs7OzBEQUVqRCw4REFBQ25DLHVEQUFLQTtnREFDRjJOLE1BQUs7Z0RBQ0xDLGdCQUFlO2dEQUNmQyxhQUFZO2dEQUNaQyxjQUFlO2dEQUNmek4sT0FBTytCO2dEQUNQekIsYUFBWTtnREFDWkosVUFBVSxDQUFDK0YsSUFBTWpFLGdCQUFnQmlFLEVBQUU0RSxNQUFNLENBQUM3SyxLQUFLO2dEQUMvQ08sV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdsQiw4REFBQzhMO2tEQUNHLDRFQUFDek0seURBQU1BOzRDQUNIOE4sU0FBUzVCOzRDQUNUdkwsV0FBVTtzREFBdUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXJIO0dBeG9CZ0JUO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbS11aS9tZW50aW9uSW5wdXQudHN4P2IzOGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7dXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IFwiLi9tZW50aW9uSW5wdXQuY3NzXCJcclxuaW1wb3J0IHtEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGV9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCI7XHJcbmltcG9ydCB7TGFiZWx9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIjtcclxuaW1wb3J0IHtJbnB1dH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xyXG5pbXBvcnQge0J1dHRvbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHtjbn0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XHJcblxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBNZW50aW9uSW5mbyB7XHJcbiAgICBsYWJlbDogc3RyaW5nO1xyXG4gICAgdGFnOiBzdHJpbmc7IC8vIGUuZy4gXCJ7e2ZpcnN0TmFtZX19XCJcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBNZW50aW9uSW5wdXRQcm9wcyB7XHJcbiAgICBrZXlNYXA6IFJlY29yZDxzdHJpbmcsIE1lbnRpb25JbmZvPjsgLy8geyBba2V5XTogeyBsYWJlbCwgdmFsdWUgfSB9XHJcbiAgICB2YWx1ZT86IHN0cmluZzsgLy8gQ29udHJvbGxlZCB1c2FnZSAoZmluYWwgdGV4dCB3aXRoIHBsYWNlaG9sZGVycylcclxuICAgIGRlZmF1bHRWYWx1ZT86IHN0cmluZzsgLy8gVW5jb250cm9sbGVkIHVzYWdlIChpbml0aWFsIEhUTUwpXHJcbiAgICBvbkNoYW5nZT86IChmaW5hbFRleHQ6IHN0cmluZykgPT4gdm9pZDsgLy8gZmluYWwgcHJvY2Vzc2VkIHRleHRcclxuICAgIHBsYWNlaG9sZGVyPzogc3RyaW5nO1xyXG4gICAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gICAgZGlzYWJsZWQ/OiBib29sZWFuOyAvLyBBZGQgZGlzYWJsZWQgcHJvcFxyXG4gICAga2VlcE5ld0xpbmU/OiBib29sZWFuOyAvLyBBZGQga2VlcE5ld0xpbmUgcHJvcFxyXG4gICAgaWQ/OiBzdHJpbmc7XHJcbiAgICBvbkJsdXI/OiAoZmluYWxUZXh0OiBzdHJpbmcpID0+IHZvaWQ7IC8vIEFkZCBvbkJsdXIgcHJvcFxyXG4gICAgb25EZWJvdW5jZUNoYW5nZT86IChmaW5hbFRleHQ6IHN0cmluZykgPT4gdm9pZDsgLy8gQWRkIG9uRGVib3VuY2VDaGFuZ2UgcHJvcFxyXG4gICAgZGVib3VuY2VUaW1lb3V0TVM/OiBudW1iZXI7IC8vIE1ha2UgZGVib3VuY2UgdGltZW91dCBjb25maWd1cmFibGVcclxufVxyXG5cclxuaW50ZXJmYWNlIE1lbnRpb25Nb2RhbFN0YXRlIHtcclxuICAgIGlzT3BlbjogYm9vbGVhbjtcclxuICAgIHNwYW46IEhUTUxTcGFuRWxlbWVudCB8IG51bGw7XHJcbiAgICBtZW50aW9uS2V5OiBzdHJpbmc7XHJcbiAgICBjdXJyZW50RGVmYXVsdDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gTWVudGlvbklucHV0KHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5TWFwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlID0gXCJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWJvdW5jZUNoYW5nZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVib3VuY2VUaW1lb3V0TVMgPSA1MDAsIC8vIERlZmF1bHQgdG8gNTAwbXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkID0gZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtlZXBOZXdMaW5lID0gZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTogTWVudGlvbklucHV0UHJvcHMpIHtcclxuICAgIGNvbnN0IGRpdlJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcblxyXG4gICAgLy8gQ3VycmVudCBvdXRwdXQgdmFsdWVcclxuICAgIGNvbnN0IGN1cnJlbnRPdXRwdXQgPSB1c2VSZWY8c3RyaW5nPihcIlwiKTtcclxuXHJcbiAgICAvLyBGb3IgZGVib3VuY2VkIHVwZGF0ZXNcclxuICAgIGNvbnN0IGRlYm91bmNlVGltZXJSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuXHJcblxyXG4gICAgLy8gRm9yIHRoZSBtZW50aW9uIHBvcHVwXHJcbiAgICBjb25zdCBbbWVudGlvbk1vZGUsIHNldE1lbnRpb25Nb2RlXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gc2hvd2luZyBtZW50aW9uIHBvcHVwP1xyXG4gICAgY29uc3QgW21lbnRpb25TZWFyY2gsIHNldE1lbnRpb25TZWFyY2hdID0gdXNlU3RhdGUoXCJcIik7IC8vIHRoZSB1c2VyIHR5cGVkIGluIHRoZSBwb3B1cCBzZWFyY2hcclxuICAgIGNvbnN0IHBvcHVwUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTsgLy8gcG9wdXAgY29udGFpbmVyIHJlZlxyXG4gICAgY29uc3QgW3Nob3dBYm92ZSwgc2V0U2hvd0Fib3ZlXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gd2hldGhlciB0byBzaG93IGRyb3Bkb3duIGFib3ZlIGlucHV0XHJcblxyXG4gICAgLy8gU3RvcmUgdGhlIGxvY2F0aW9uIG9mIHRoZSBcIkBcIiB0aGF0IHRyaWdnZXJlZCB0aGUgcG9wdXAuXHJcbiAgICBjb25zdCBbYXRSYW5nZSwgc2V0QXRSYW5nZV0gPSB1c2VTdGF0ZTxSYW5nZSB8IG51bGw+KG51bGwpO1xyXG4gICAgLy8gUmVmIGZvciB0aGUgbWVudGlvbiBwb3B1cCBzZWFyY2ggaW5wdXQuXHJcbiAgICBjb25zdCBtZW50aW9uSW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbCk7XHJcblxyXG4gICAgLy8gVXNlZCB0byB0ZW1wb3JhcmlseSBieXBhc3MgdGhlIGNvbnRyb2xsZWQgaW5uZXJIVE1MIHVwZGF0ZSAod2hpY2ggY2FuIHJlc2V0IHRoZSBjYXJldClcclxuICAgIGNvbnN0IHNraXBJbm5lckh0bWxVcGRhdGUgPSB1c2VSZWYoZmFsc2UpO1xyXG5cclxuICAgIC8vIE1vZGFsIHN0YXRlIGZvciBzZXR0aW5nIGEgZGVmYXVsdCB2YWx1ZSBmb3IgYSBtZW50aW9uLlxyXG4gICAgY29uc3QgW21vZGFsU3RhdGUsIHNldE1vZGFsU3RhdGVdID0gdXNlU3RhdGU8TWVudGlvbk1vZGFsU3RhdGU+KHtcclxuICAgICAgICBpc09wZW46IGZhbHNlLFxyXG4gICAgICAgIHNwYW46IG51bGwsXHJcbiAgICAgICAgbWVudGlvbktleTogXCJcIixcclxuICAgICAgICBjdXJyZW50RGVmYXVsdDogXCJcIixcclxuICAgIH0pO1xyXG4gICAgY29uc3QgW2RyYWZ0RGVmYXVsdCwgc2V0RHJhZnREZWZhdWx0XSA9IHVzZVN0YXRlKFwiXCIpO1xyXG5cclxuXHJcblxyXG4gICAgLy8gY29uc29sZS5sb2coXCJNZW50aW9uSW5wdXQ6XCIsIHtwb3B1cFBvc2l0aW9uLCBtZW50aW9uTW9kZSwgYXRSYW5nZX0pXHJcblxyXG4gICAgLy8gQ29udmVydCBmaW5hbCB0ZXh0IHZhbHVlIHRvIEhUTUwgd2l0aCBtZW50aW9uIHNwYW5zLlxyXG4gICAgZnVuY3Rpb24gcGFyc2VUZXh0VG9IdG1sKHRleHQ6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgICAgICAgdGV4dCA9IFN0cmluZyh0ZXh0IHx8ICcnKTtcclxuICAgICAgICBjb25zdCBtZW50aW9uUmVnZXggPSAvXFx7XFx7KFteL31dKykoPzpcXC8oLio/KSk/XFx9XFx9L2c7XHJcbiAgICAgICAgbGV0IGxhc3RJbmRleCA9IDA7XHJcbiAgICAgICAgbGV0IHJlc3VsdEh0bWwgPSBcIlwiO1xyXG5cclxuICAgICAgICBmb3IgKGNvbnN0IG1hdGNoIG9mIHRleHQubWF0Y2hBbGwobWVudGlvblJlZ2V4KSkge1xyXG4gICAgICAgICAgICBjb25zdCBzdGFydCA9IG1hdGNoLmluZGV4ID8/IDA7XHJcbiAgICAgICAgICAgIGlmIChzdGFydCA+IGxhc3RJbmRleCkge1xyXG4gICAgICAgICAgICAgICAgbGV0IHBsYWluID0gc2FmZUh0bWwodGV4dC5zbGljZShsYXN0SW5kZXgsIHN0YXJ0KSk7XHJcbiAgICAgICAgICAgICAgICBpZiAoa2VlcE5ld0xpbmUpIHtcclxuICAgICAgICAgICAgICAgICAgICBwbGFpbiA9IHBsYWluLnJlcGxhY2UoL1xcbi9nLCBcIjxicj5cIik7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICByZXN1bHRIdG1sICs9IHBsYWluO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBjb25zdCBtZW50aW9uS2V5ID0gbWF0Y2hbMV07XHJcbiAgICAgICAgICAgIGNvbnN0IG1lbnRpb25EZWZhdWx0ID0gbWF0Y2hbMl0gfHwgXCJcIjtcclxuXHJcbiAgICAgICAgICAgIC8vIEZpbmQgY2FzZS1pbnNlbnNpdGl2ZSBrZXkgbWF0Y2hcclxuICAgICAgICAgICAgY29uc3QgYWN0dWFsS2V5ID0gZmluZENhc2VJbnNlbnNpdGl2ZUtleShrZXlNYXAsIG1lbnRpb25LZXkpO1xyXG4gICAgICAgICAgICBjb25zdCBpbmZvID0gYWN0dWFsS2V5ID8ga2V5TWFwW2FjdHVhbEtleV0gOiBudWxsO1xyXG5cclxuICAgICAgICAgICAgaWYgKGluZm8pIHtcclxuICAgICAgICAgICAgICAgIGxldCBsYWJlbCA9IGluZm8ubGFiZWw7XHJcbiAgICAgICAgICAgICAgICBpZiAobWVudGlvbkRlZmF1bHQudHJpbSgpICE9PSBcIlwiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWwgKz0gYCAvICR7bWVudGlvbkRlZmF1bHR9YDtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhVmFsdWUgPVxyXG4gICAgICAgICAgICAgICAgICAgIG1lbnRpb25EZWZhdWx0LnRyaW0oKSA9PT0gXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGB7eyR7YWN0dWFsS2V5fX19YFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGB7eyR7YWN0dWFsS2V5fS8ke21lbnRpb25EZWZhdWx0fX19YDtcclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBzcGFuSHRtbCA9IGA8c3BhbiBjb250ZW50ZWRpdGFibGU9XCJmYWxzZVwiIGRhdGEtdGFnPVwiJHtlc2NhcGVBdHRyKGRhdGFWYWx1ZSl9XCI+JHtzYWZlSHRtbChsYWJlbCl9PC9zcGFuPmA7XHJcbiAgICAgICAgICAgICAgICByZXN1bHRIdG1sICs9IHNwYW5IdG1sO1xyXG4gICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgcmVzdWx0SHRtbCArPSBzYWZlSHRtbChtYXRjaFswXSk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGxhc3RJbmRleCA9IHN0YXJ0ICsgbWF0Y2hbMF0ubGVuZ3RoO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKGxhc3RJbmRleCA8IHRleHQubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgIGxldCBwbGFpbiA9IHNhZmVIdG1sKHRleHQuc2xpY2UobGFzdEluZGV4KSk7XHJcbiAgICAgICAgICAgIGlmIChrZWVwTmV3TGluZSkge1xyXG4gICAgICAgICAgICAgICAgcGxhaW4gPSBwbGFpbi5yZXBsYWNlKC9cXG4vZywgXCI8YnI+XCIpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJlc3VsdEh0bWwgKz0gcGxhaW47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXR1cm4gcmVzdWx0SHRtbDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBIZWxwZXIgZnVuY3Rpb24gZm9yIGNhc2UtaW5zZW5zaXRpdmUga2V5IGxvb2t1cFxyXG4gICAgZnVuY3Rpb24gZmluZENhc2VJbnNlbnNpdGl2ZUtleShvYmo6IFJlY29yZDxzdHJpbmcsIGFueT4sIGtleTogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XHJcbiAgICAgICAgY29uc3QgbG93ZXJLZXkgPSBrZXkudG9Mb3dlckNhc2UoKTtcclxuICAgICAgICBmb3IgKGNvbnN0IGsgb2YgT2JqZWN0LmtleXMob2JqKSkge1xyXG4gICAgICAgICAgICBpZiAoay50b0xvd2VyQ2FzZSgpID09PSBsb3dlcktleSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGs7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgZnVuY3Rpb24gc2FmZUh0bWwoc3RyOiBzdHJpbmcpIHtcclxuICAgICAgICByZXR1cm4gc3RyXHJcbiAgICAgICAgICAgIC5yZXBsYWNlKC8mL2csIFwiJmFtcDtcIilcclxuICAgICAgICAgICAgLnJlcGxhY2UoLzwvZywgXCImbHQ7XCIpXHJcbiAgICAgICAgICAgIC5yZXBsYWNlKC8+L2csIFwiJmd0O1wiKTtcclxuICAgIH1cclxuXHJcbiAgICBmdW5jdGlvbiBlc2NhcGVBdHRyKHN0cjogc3RyaW5nKSB7XHJcbiAgICAgICAgcmV0dXJuIHN0ci5yZXBsYWNlKC9cIi9nLCBcIiZxdW90O1wiKS5yZXBsYWNlKC8nL2csIFwiJiMzOTtcIik7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gRm9yIHVuY29udHJvbGxlZCBtb2RlOiBvbiBmaXJzdCBtb3VudCBmaWxsIGlubmVySFRNTCB3aXRoIGRlZmF1bHRWYWx1ZS5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQgJiYgZGVmYXVsdFZhbHVlICE9PSB1bmRlZmluZWQgJiYgZGl2UmVmLmN1cnJlbnQgJiYgIXNraXBJbm5lckh0bWxVcGRhdGUuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50SHRtbCA9IGRpdlJlZi5jdXJyZW50LmlubmVySFRNTDtcclxuICAgICAgICAgICAgY29uc3QgbmV3SHRtbCA9IHBhcnNlVGV4dFRvSHRtbChkZWZhdWx0VmFsdWUgfHwgJycpO1xyXG4gICAgICAgICAgICBpZiAoY3VycmVudEh0bWwgIT09IG5ld0h0bWwpIHtcclxuICAgICAgICAgICAgICAgIGRpdlJlZi5jdXJyZW50LmlubmVySFRNTCA9IG5ld0h0bWw7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9LCBbZGVmYXVsdFZhbHVlLCB2YWx1ZSwga2V5TWFwXSk7XHJcblxyXG4gICAgLy8gRm9yIGNvbnRyb2xsZWQgbW9kZTogdXBkYXRlIGlubmVySFRNTCB3aGVuIHZhbHVlIGNoYW5nZXMuXHJcbiAgICAvLyBXZSBza2lwIHRoaXMgdXBkYXRlIGltbWVkaWF0ZWx5IGFmdGVyIGEgbWVudGlvbiBpbnNlcnRpb24gaWYgbmVjZXNzYXJ5LlxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCAmJiBkaXZSZWYuY3VycmVudCAmJiAhc2tpcElubmVySHRtbFVwZGF0ZS5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRIdG1sID0gZGl2UmVmLmN1cnJlbnQuaW5uZXJIVE1MO1xyXG4gICAgICAgICAgICBjb25zdCBuZXdIdG1sID0gcGFyc2VUZXh0VG9IdG1sKHZhbHVlKTtcclxuICAgICAgICAgICAgLy8gT25seSB1cGRhdGUgaW5uZXJIVE1MIGlmIHRoZSBkaXYgaXMgbm90IGZvY3VzZWQgKHRvIHByZXNlcnZlIHRoZSBjYXJldCBwb3NpdGlvbilcclxuICAgICAgICAgICAgaWYgKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgIT09IGRpdlJlZi5jdXJyZW50ICYmIGN1cnJlbnRIdG1sICE9PSBuZXdIdG1sKSB7XHJcbiAgICAgICAgICAgICAgICBkaXZSZWYuY3VycmVudC5pbm5lckhUTUwgPSBuZXdIdG1sO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW3ZhbHVlLCBrZXlNYXBdKTtcclxuXHJcbiAgICAvLyBCdWlsZCB0aGUgcHJvY2Vzc2VkIGZpbmFsIHRleHQgZnJvbSBpbm5lckhUTUwuXHJcbiAgICBmdW5jdGlvbiBnZXRGaW5hbE91dHB1dCgpOiBzdHJpbmcge1xyXG4gICAgICAgIGlmICghZGl2UmVmLmN1cnJlbnQpIHJldHVybiBcIlwiO1xyXG4gICAgICAgIGxldCByZXN1bHQgPSBcIlwiO1xyXG5cclxuICAgICAgICBmdW5jdGlvbiB0cmF2ZXJzZShub2RlOiBOb2RlKSB7XHJcbiAgICAgICAgICAgIGlmIChub2RlLm5vZGVUeXBlID09PSBOb2RlLlRFWFRfTk9ERSkge1xyXG4gICAgICAgICAgICAgICAgcmVzdWx0ICs9IG5vZGUubm9kZVZhbHVlIHx8IFwiXCI7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5FTEVNRU5UX05PREUpIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGVsID0gbm9kZSBhcyBIVE1MRWxlbWVudDtcclxuICAgICAgICAgICAgICAgIGlmIChlbC5oYXNBdHRyaWJ1dGUoXCJkYXRhLXRhZ1wiKSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdCArPSBlbC5nZXRBdHRyaWJ1dGUoXCJkYXRhLXRhZ1wiKTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZWwubm9kZU5hbWUgPT09IFwiQlJcIikge1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChrZWVwTmV3TGluZSkgcmVzdWx0ICs9IFwiXFxuXCI7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIEFycmF5LmZyb20oZWwuY2hpbGROb2RlcykuZm9yRWFjaChjaGlsZCA9PiB0cmF2ZXJzZShjaGlsZCkpO1xyXG4gICAgICAgICAgICAgICAgICAgIGlmIChrZWVwTmV3TGluZSAmJiAoZWwubm9kZU5hbWUgPT09IFwiRElWXCIgfHwgZWwubm9kZU5hbWUgPT09IFwiUFwiKSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXN1bHQgKz0gXCJcXG5cIjtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIEFycmF5LmZyb20oZGl2UmVmLmN1cnJlbnQuY2hpbGROb2RlcykuZm9yRWFjaChjaGlsZCA9PiB0cmF2ZXJzZShjaGlsZCkpO1xyXG4gICAgICAgIHJldHVybiByZXN1bHQ7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVXBkYXRlIHZhbHVlIGFuZCB0cmlnZ2VyIG9uQ2hhbmdlIGltbWVkaWF0ZWx5XHJcbiAgICBmdW5jdGlvbiB1cGRhdGVWYWx1ZSgpIHtcclxuICAgICAgICBjb25zdCBvdXRwdXQgPSBnZXRGaW5hbE91dHB1dCgpO1xyXG4gICAgICAgIGN1cnJlbnRPdXRwdXQuY3VycmVudCA9IG91dHB1dDtcclxuICAgICAgICBvbkNoYW5nZT8uKG91dHB1dCk7XHJcblxyXG4gICAgICAgIC8vIFNldCB1cCBkZWJvdW5jZWQgdXBkYXRlXHJcbiAgICAgICAgaWYgKG9uRGVib3VuY2VDaGFuZ2UpIHtcclxuICAgICAgICAgICAgaWYgKGRlYm91bmNlVGltZXJSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KGRlYm91bmNlVGltZXJSZWYuY3VycmVudCk7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIGRlYm91bmNlVGltZXJSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgb25EZWJvdW5jZUNoYW5nZShvdXRwdXQpO1xyXG4gICAgICAgICAgICAgICAgZGVib3VuY2VUaW1lclJlZi5jdXJyZW50ID0gbnVsbDtcclxuICAgICAgICAgICAgfSwgZGVib3VuY2VUaW1lb3V0TVMpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAvLyBIYW5kbGUgYmx1ciBldmVudFxyXG4gICAgZnVuY3Rpb24gaGFuZGxlQmx1cigpIHtcclxuICAgICAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICBpZiAobWVudGlvbk1vZGUpIHJldHVybjtcclxuICAgICAgICBvbkJsdXI/LihjdXJyZW50T3V0cHV0LmN1cnJlbnQgfHwgZ2V0RmluYWxPdXRwdXQoKSk7XHJcbiAgICB9XHJcblxyXG4gICAgZnVuY3Rpb24gb25JbnB1dCgpIHtcclxuICAgICAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICB1cGRhdGVWYWx1ZSgpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbiAgICAvLyBNZW50aW9uIHBvcHVwIGxvZ2ljXHJcbiAgICAvLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG5cclxuICAgIC8vIFdoZW4gdGhlIHVzZXIgdHlwZXMgXCJAXCIsIGxldCBpdCBpbnNlcnQgbm9ybWFsbHkgYnV0IHN0b3JlIHRoZSBjdXJyZW50IFJhbmdlLlxyXG4gICAgZnVuY3Rpb24gb25LZXlEb3duKGU6IFJlYWN0LktleWJvYXJkRXZlbnQ8SFRNTERpdkVsZW1lbnQ+KSB7XHJcbiAgICAgICAgaWYgKGRpc2FibGVkKSByZXR1cm47XHJcbiAgICAgICAgaWYgKGUua2V5ID09PSBcIkBcIikge1xyXG4gICAgICAgICAgICBjb25zdCBzZWwgPSB3aW5kb3cuZ2V0U2VsZWN0aW9uPy4oKTtcclxuICAgICAgICAgICAgaWYgKHNlbCAmJiBzZWwucmFuZ2VDb3VudCA+IDApIHtcclxuICAgICAgICAgICAgICAgIC8vIFNhdmUgYSBjbG9uZSBvZiB0aGUgY3VycmVudCByYW5nZSB3aGVyZSBcIkBcIiB3YXMgaW5zZXJ0ZWQuXHJcbiAgICAgICAgICAgICAgICBjb25zdCBjdXJyZW50UmFuZ2UgPSBzZWwuZ2V0UmFuZ2VBdCgwKS5jbG9uZVJhbmdlKCk7XHJcbiAgICAgICAgICAgICAgICBzZXRBdFJhbmdlKGN1cnJlbnRSYW5nZSk7XHJcbiAgICAgICAgICAgICAgICAvLyBDb21wdXRlIGNhcmV0IHBvc2l0aW9uIHJlbGF0aXZlIHRvIHZpZXdwb3J0LlxyXG4gICAgICAgICAgICAgICAgbGV0IHJlY3QgPSBjdXJyZW50UmFuZ2UuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgICAgICAgICAgICAvLyBJZiB0aGUgcmVjdGFuZ2xlIGlzIGFsbCB6ZXJvcywgY3JlYXRlIGEgdGVtcG9yYXJ5IG1hcmtlciB0byBjb21wdXRlIGNvcnJlY3QgY29vcmRpbmF0ZXMuXHJcbiAgICAgICAgICAgICAgICBpZiAocmVjdC53aWR0aCA9PT0gMCAmJiByZWN0LmhlaWdodCA9PT0gMCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1hcmtlciA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIpO1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmtlci50ZXh0Q29udGVudCA9IFwiXFx1MjAwQlwiOyAvLyB6ZXJvIHdpZHRoIHNwYWNlXHJcbiAgICAgICAgICAgICAgICAgICAgY3VycmVudFJhbmdlLmluc2VydE5vZGUobWFya2VyKTtcclxuICAgICAgICAgICAgICAgICAgICByZWN0ID0gbWFya2VyLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmtlci5wYXJlbnROb2RlPy5yZW1vdmVDaGlsZChtYXJrZXIpO1xyXG4gICAgICAgICAgICAgICAgICAgIHNlbC5yZW1vdmVBbGxSYW5nZXMoKTtcclxuICAgICAgICAgICAgICAgICAgICBzZWwuYWRkUmFuZ2UoY3VycmVudFJhbmdlKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgLy8gUG9zaXRpb24gd2lsbCBiZSBjYWxjdWxhdGVkIGluIHVzZUVmZmVjdCB0byBlbnN1cmUgcHJvcGVyIHBvc2l0aW9uaW5nXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgc2V0TWVudGlvbk1vZGUodHJ1ZSk7XHJcbiAgICAgICAgICAgIHNldE1lbnRpb25TZWFyY2goXCJcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENsb3NlIHRoZSBtZW50aW9uIHBvcHVwLlxyXG4gICAgZnVuY3Rpb24gY2xvc2VNZW50aW9uUG9wdXAoKSB7XHJcbiAgICAgICAgc2V0TWVudGlvbk1vZGUoZmFsc2UpO1xyXG4gICAgICAgIHNldE1lbnRpb25TZWFyY2goXCJcIik7XHJcbiAgICAgICAgc2V0QXRSYW5nZShudWxsKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBFbmhhbmNlZCBmdXp6eSBmaWx0ZXJpbmcgdGhhdCBjaGVja3MgYm90aCBrZXlzIGFuZCBsYWJlbHNcclxuICAgIGZ1bmN0aW9uIGZ1enp5RmlsdGVyKHF1ZXJ5OiBzdHJpbmcsIGl0ZW1zOiBzdHJpbmdbXSk6IHN0cmluZ1tdIHtcclxuICAgICAgICBpZiAoIXF1ZXJ5LnRyaW0oKSkgcmV0dXJuIGl0ZW1zLnNsaWNlKDAsIDMwKTtcclxuICAgICAgICBjb25zdCBub3JtYWxpemVkUXVlcnkgPSBxdWVyeS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpLnRvTG93ZXJDYXNlKCk7XHJcblxyXG4gICAgICAgIHR5cGUgUmFua2VkID0geyBpdGVtOiBzdHJpbmc7IHJhbms6IG51bWJlciB9O1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdHM6IFJhbmtlZFtdID0gW107XHJcblxyXG4gICAgICAgIGZvciAoY29uc3Qga2V5IG9mIGl0ZW1zKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGluZm8gPSBrZXlNYXBba2V5XTtcclxuICAgICAgICAgICAgY29uc3Qgbm9ybWFsaXplZEtleSA9IGtleS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpLnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRMYWJlbCA9IGluZm8ubGFiZWwucmVwbGFjZSgvXFxzKy9nLCBcIlwiKS50b0xvd2VyQ2FzZSgpO1xyXG5cclxuICAgICAgICAgICAgLy8gUmFuayBwcmlvcml0aWVzIChsb3dlciBpcyBiZXR0ZXIpOlxyXG4gICAgICAgICAgICAvLyAxOiBLZXkgc3RhcnRzIHdpdGggcXVlcnlcclxuICAgICAgICAgICAgLy8gMjogTGFiZWwgc3RhcnRzIHdpdGggcXVlcnlcclxuICAgICAgICAgICAgLy8gMzogS2V5IGNvbnRhaW5zIHF1ZXJ5XHJcbiAgICAgICAgICAgIC8vIDQ6IExhYmVsIGNvbnRhaW5zIHF1ZXJ5XHJcblxyXG4gICAgICAgICAgICBpZiAobm9ybWFsaXplZEtleS5zdGFydHNXaXRoKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiAxfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9ybWFsaXplZExhYmVsLnN0YXJ0c1dpdGgobm9ybWFsaXplZFF1ZXJ5KSkge1xyXG4gICAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHtpdGVtOiBrZXksIHJhbms6IDJ9KTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChub3JtYWxpemVkS2V5LmluY2x1ZGVzKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiAzfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9ybWFsaXplZExhYmVsLmluY2x1ZGVzKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiA0fSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJlc3VsdHMuc29ydCgoYSwgYikgPT4gYS5yYW5rIC0gYi5yYW5rKTtcclxuICAgICAgICByZXR1cm4gcmVzdWx0cy5tYXAoKHIpID0+IHIuaXRlbSkuc2xpY2UoMCwgMzApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGFsbEtleXMgPSBPYmplY3Qua2V5cyhrZXlNYXApO1xyXG4gICAgY29uc3QgbWVudGlvbk1hdGNoZXMgPSBtZW50aW9uTW9kZSA/IGZ1enp5RmlsdGVyKG1lbnRpb25TZWFyY2gsIGFsbEtleXMpIDogW107XHJcblxyXG4gICAgLy8gV2hlbiBhIG1lbnRpb24gaXMgc2VsZWN0ZWQgZnJvbSB0aGUgcG9wdXAuXHJcbiAgICBmdW5jdGlvbiBvbk1lbnRpb25TZWxlY3QobWVudGlvbktleTogc3RyaW5nKSB7XHJcbiAgICAgICAgaWYgKCFkaXZSZWYuY3VycmVudCkgcmV0dXJuO1xyXG4gICAgICAgIGNvbnN0IHNlbCA9IHdpbmRvdy5nZXRTZWxlY3Rpb24oKTtcclxuICAgICAgICBpZiAoIXNlbCkge1xyXG4gICAgICAgICAgICBjbG9zZU1lbnRpb25Qb3B1cCgpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIEFsd2F5cyB1c2UgdGhlIHN0b3JlZCByYW5nZSBpZiBhdmFpbGFibGUuXHJcbiAgICAgICAgbGV0IHJhbmdlOiBSYW5nZSB8IG51bGwgPSBhdFJhbmdlIHx8IChzZWwucmFuZ2VDb3VudCA+IDAgPyBzZWwuZ2V0UmFuZ2VBdCgwKSA6IG51bGwpO1xyXG4gICAgICAgIGlmICghcmFuZ2UpIHtcclxuICAgICAgICAgICAgY2xvc2VNZW50aW9uUG9wdXAoKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gSGVscGVyOiBTZWFyY2ggYmFja3dhcmRzIGluIGEgdGV4dCBub2RlIGZyb20gYSBnaXZlbiBvZmZzZXQgdG8gcmVtb3ZlIFwiQFwiLlxyXG4gICAgICAgIGZ1bmN0aW9uIHJlbW92ZUF0RnJvbVRleHROb2RlKHRleHROb2RlOiBUZXh0LCBwb3M6IG51bWJlcik6IFJhbmdlIHwgbnVsbCB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRleHQgPSB0ZXh0Tm9kZS5kYXRhO1xyXG4gICAgICAgICAgICBjb25zdCBzZWFyY2hTdGFydCA9IE1hdGgubWF4KDAsIHBvcyAtIDUpO1xyXG4gICAgICAgICAgICBjb25zdCBzZWFyY2hFbmQgPSBNYXRoLm1pbih0ZXh0Lmxlbmd0aCwgcG9zICsgNSk7XHJcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSBzZWFyY2hFbmQgLSAxOyBpID49IHNlYXJjaFN0YXJ0OyBpLS0pIHtcclxuICAgICAgICAgICAgICAgIGlmICh0ZXh0LmNoYXJBdChpKSA9PT0gXCJAXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0Tm9kZS5kYXRhID0gdGV4dC5zdWJzdHJpbmcoMCwgaSkgKyB0ZXh0LnN1YnN0cmluZyhpICsgMSk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3UmFuZ2UgPSBkb2N1bWVudC5jcmVhdGVSYW5nZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgIG5ld1JhbmdlLnNldFN0YXJ0KHRleHROb2RlLCBpKTtcclxuICAgICAgICAgICAgICAgICAgICBuZXdSYW5nZS5jb2xsYXBzZSh0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3UmFuZ2U7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBUcnkgdG8gcmVtb3ZlIFwiQFwiIGZyb20gdGhlIGN1cnJlbnQgdGV4dCBub2RlLlxyXG4gICAgICAgIGlmIChyYW5nZS5zdGFydENvbnRhaW5lci5ub2RlVHlwZSA9PT0gTm9kZS5URVhUX05PREUpIHtcclxuICAgICAgICAgICAgY29uc3QgdGV4dE5vZGUgPSByYW5nZS5zdGFydENvbnRhaW5lciBhcyBUZXh0O1xyXG4gICAgICAgICAgICBjb25zdCBwb3MgPSByYW5nZS5zdGFydE9mZnNldDtcclxuICAgICAgICAgICAgY29uc3QgbmV3Um5nID0gcmVtb3ZlQXRGcm9tVGV4dE5vZGUodGV4dE5vZGUsIHBvcyk7XHJcbiAgICAgICAgICAgIGlmIChuZXdSbmcpIHtcclxuICAgICAgICAgICAgICAgIHJhbmdlID0gbmV3Um5nO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8gSWYgbm90IGEgdGV4dCBub2RlLCBjaGVjayBwcmV2aW91cyBzaWJsaW5nIChpZiB0ZXh0KSBmcm9tIHRoZSBjdXJyZW50IGNvbnRhaW5lci5cclxuICAgICAgICAgICAgY29uc3QgY29udGFpbmVyID0gcmFuZ2Uuc3RhcnRDb250YWluZXI7XHJcbiAgICAgICAgICAgIGlmIChjb250YWluZXIuY2hpbGROb2Rlcy5sZW5ndGggPiAwICYmIHJhbmdlLnN0YXJ0T2Zmc2V0ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcHJldiA9IGNvbnRhaW5lci5jaGlsZE5vZGVzW3JhbmdlLnN0YXJ0T2Zmc2V0IC0gMV07XHJcbiAgICAgICAgICAgICAgICBpZiAocHJldiAmJiBwcmV2Lm5vZGVUeXBlID09PSBOb2RlLlRFWFRfTk9ERSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRleHROb2RlID0gcHJldiBhcyBUZXh0O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1JuZyA9IHJlbW92ZUF0RnJvbVRleHROb2RlKHRleHROb2RlLCB0ZXh0Tm9kZS5kYXRhLmxlbmd0aCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5ld1JuZykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByYW5nZSA9IG5ld1JuZztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGluZm8gPSBrZXlNYXBbbWVudGlvbktleV07XHJcbiAgICAgICAgaWYgKCFpbmZvKSB7XHJcbiAgICAgICAgICAgIGNsb3NlTWVudGlvblBvcHVwKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIENyZWF0ZSBhbmQgaW5zZXJ0IHRoZSBtZW50aW9uIHNwYW4uXHJcbiAgICAgICAgY29uc3Qgc3BhbiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIpO1xyXG4gICAgICAgIHNwYW4uY29udGVudEVkaXRhYmxlID0gXCJmYWxzZVwiO1xyXG4gICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKFwiZGF0YS10YWdcIiwgaW5mby50YWcpO1xyXG4gICAgICAgIHNwYW4udGV4dENvbnRlbnQgPSBpbmZvLmxhYmVsO1xyXG4gICAgICAgIHNwYW4ub25kYmxjbGljayA9ICgpID0+IHtcclxuICAgICAgICAgICAgb3BlbkRlZmF1bHRNb2RhbChzcGFuKTtcclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBza2lwSW5uZXJIdG1sVXBkYXRlLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICAgIHJhbmdlLmluc2VydE5vZGUoc3Bhbik7XHJcblxyXG4gICAgICAgIC8vIEluc2VydCBhIHplcm8td2lkdGggc3BhY2UgZmlsbGVyIG5vZGUgYWZ0ZXIgdGhlIG1lbnRpb24gc3Bhbi5cclxuICAgICAgICBjb25zdCBmaWxsZXIgPSBkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShcIlxcdTIwMEJcIik7XHJcbiAgICAgICAgc3Bhbi5wYXJlbnROb2RlPy5pbnNlcnRCZWZvcmUoZmlsbGVyLCBzcGFuLm5leHRTaWJsaW5nKTtcclxuXHJcbiAgICAgICAgLy8gUG9zaXRpb24gdGhlIGN1cnNvciBhZnRlciB0aGUgZmlsbGVyIG5vZGUuXHJcbiAgICAgICAgY29uc3QgbmV3UmFuZ2UgPSBkb2N1bWVudC5jcmVhdGVSYW5nZSgpO1xyXG4gICAgICAgIG5ld1JhbmdlLnNldFN0YXJ0QWZ0ZXIoZmlsbGVyKTtcclxuICAgICAgICBuZXdSYW5nZS5jb2xsYXBzZSh0cnVlKTtcclxuICAgICAgICBzZWwucmVtb3ZlQWxsUmFuZ2VzKCk7XHJcbiAgICAgICAgc2VsLmFkZFJhbmdlKG5ld1JhbmdlKTtcclxuXHJcbiAgICAgICAgZGl2UmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgICBjbG9zZU1lbnRpb25Qb3B1cCgpO1xyXG4gICAgICAgIHVwZGF0ZVZhbHVlKCk7XHJcblxyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBza2lwSW5uZXJIdG1sVXBkYXRlLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgICB9LCAwKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbG9zZSBtZW50aW9uIHBvcHVwIG9uIEVTQyBrZXkgb3Igb3V0c2lkZSBjbGljay5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgZnVuY3Rpb24gb25LZXkoZTogS2V5Ym9hcmRFdmVudCkge1xyXG4gICAgICAgICAgICBpZiAoZS5rZXkgPT09IFwiRXNjYXBlXCIpIHtcclxuICAgICAgICAgICAgICAgIGNsb3NlTWVudGlvblBvcHVwKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGZ1bmN0aW9uIG9uQ2xpY2tPdXRzaWRlKGU6IE1vdXNlRXZlbnQpIHtcclxuICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZS50YXJnZXQgYXMgTm9kZTtcclxuICAgICAgICAgICAgLy8gQ2hlY2sgaWYgY2xpY2sgaXMgb3V0c2lkZSBib3RoIHRoZSBwb3B1cCBhbmQgdGhlIGlucHV0IGZpZWxkXHJcbiAgICAgICAgICAgIGlmIChwb3B1cFJlZi5jdXJyZW50ICYmICFwb3B1cFJlZi5jdXJyZW50LmNvbnRhaW5zKHRhcmdldCkgJiYgXHJcbiAgICAgICAgICAgICAgICBkaXZSZWYuY3VycmVudCAmJiAhZGl2UmVmLmN1cnJlbnQuY29udGFpbnModGFyZ2V0KSkge1xyXG4gICAgICAgICAgICAgICAgY2xvc2VNZW50aW9uUG9wdXAoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKG1lbnRpb25Nb2RlKSB7XHJcbiAgICAgICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIG9uS2V5KTtcclxuICAgICAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLCBvbkNsaWNrT3V0c2lkZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIG9uS2V5KTtcclxuICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLCBvbkNsaWNrT3V0c2lkZSk7XHJcbiAgICAgICAgfTtcclxuICAgIH0sIFttZW50aW9uTW9kZV0pO1xyXG5cclxuICAgIC8vIEF1dG9mb2N1cyB0aGUgbWVudGlvbiBwb3B1cCBzZWFyY2ggaW5wdXQgd2hlbiBpdCBvcGVucy5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKG1lbnRpb25Nb2RlKSB7XHJcbiAgICAgICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBtZW50aW9uSW5wdXRSZWYuY3VycmVudD8uZm9jdXMoKTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW21lbnRpb25Nb2RlXSk7XHJcblxyXG4gICAgLy8gU21hcnQgcG9zaXRpb25pbmc6IGNoZWNrIGlmIGRyb3Bkb3duIHNob3VsZCBhcHBlYXIgYWJvdmUgb3IgYmVsb3cgY3Vyc29yIHBvc2l0aW9uXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChtZW50aW9uTW9kZSAmJiBkaXZSZWYuY3VycmVudCAmJiBhdFJhbmdlKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZVBvc2l0aW9uID0gKCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgLy8gR2V0IGN1cnNvciBwb3NpdGlvbiBmcm9tIHRoZSBzdG9yZWQgcmFuZ2VcclxuICAgICAgICAgICAgICAgIGxldCBjdXJzb3JSZWN0ID0gYXRSYW5nZS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgLy8gSWYgdGhlIHJlY3RhbmdsZSBpcyBlbXB0eSwgdHJ5IHRvIGdldCBhIGJldHRlciBwb3NpdGlvblxyXG4gICAgICAgICAgICAgICAgaWYgKGN1cnNvclJlY3Qud2lkdGggPT09IDAgJiYgY3Vyc29yUmVjdC5oZWlnaHQgPT09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzZWwgPSB3aW5kb3cuZ2V0U2VsZWN0aW9uKCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKHNlbCAmJiBzZWwucmFuZ2VDb3VudCA+IDApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFJhbmdlID0gc2VsLmdldFJhbmdlQXQoMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvclJlY3QgPSBjdXJyZW50UmFuZ2UuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAvLyBJZiBzdGlsbCBubyBnb29kIHBvc2l0aW9uLCBmYWxsIGJhY2sgdG8gaW5wdXQgZWxlbWVudCBwb3NpdGlvblxyXG4gICAgICAgICAgICAgICAgaWYgKGN1cnNvclJlY3Qud2lkdGggPT09IDAgJiYgY3Vyc29yUmVjdC5oZWlnaHQgPT09IDApIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpbnB1dFJlY3QgPSBkaXZSZWYuY3VycmVudD8uZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlucHV0UmVjdCkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3JSZWN0ID0gaW5wdXRSZWN0O1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgY29uc3Qgc3BhY2VCZWxvdyA9IHdpbmRvdy5pbm5lckhlaWdodCAtIGN1cnNvclJlY3QuYm90dG9tO1xyXG4gICAgICAgICAgICAgICAgY29uc3Qgc3BhY2VBYm92ZSA9IGN1cnNvclJlY3QudG9wO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZHJvcGRvd25IZWlnaHQgPSAyNTA7IC8vIGFwcHJveGltYXRlIG1heCBoZWlnaHQgb2YgZHJvcGRvd25cclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgICAgLy8gSWYgbm90IGVub3VnaCBzcGFjZSBiZWxvdyAobGVzcyB0aGFuIGRyb3Bkb3duIGhlaWdodCksIHRyeSB0byBzaG93IGFib3ZlXHJcbiAgICAgICAgICAgICAgICBpZiAoc3BhY2VCZWxvdyA8IGRyb3Bkb3duSGVpZ2h0ICYmIHNwYWNlQWJvdmUgPiBkcm9wZG93bkhlaWdodCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFNob3dBYm92ZSh0cnVlKTtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICAgICAgc2V0U2hvd0Fib3ZlKGZhbHNlKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHVwZGF0ZVBvc2l0aW9uKCk7XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBVcGRhdGUgcG9zaXRpb24gb24gd2luZG93IHJlc2l6ZSBvciBzY3JvbGxcclxuICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHVwZGF0ZVBvc2l0aW9uKTtcclxuICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVBvc2l0aW9uLCB0cnVlKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdXBkYXRlUG9zaXRpb24pO1xyXG4gICAgICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVBvc2l0aW9uLCB0cnVlKTtcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICB9XHJcbiAgICB9LCBbbWVudGlvbk1vZGUsIGF0UmFuZ2VdKTtcclxuXHJcblxyXG5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbiAgICAvLyBEb3VibGUtY2xpY2sgbWVudGlvbiA9PiBvcGVuIGEgbW9kYWwgZm9yIGVkaXRpbmcgZGVmYXVsdC5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG4gICAgZnVuY3Rpb24gb3BlbkRlZmF1bHRNb2RhbChzcGFuOiBIVE1MU3BhbkVsZW1lbnQpIHtcclxuICAgICAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICBjb25zdCBtZW50aW9uVmFsdWUgPSBzcGFuLmdldEF0dHJpYnV0ZShcImRhdGEtdGFnXCIpIHx8IFwiXCI7XHJcbiAgICAgICAgY29uc3QgcGF0dGVybiA9IC9eXFx7XFx7KFteL31dKykoPzpcXC8oLio/KSk/XFx9XFx9XFxzKiQvO1xyXG4gICAgICAgIGNvbnN0IG1hdGNoID0gbWVudGlvblZhbHVlLm1hdGNoKHBhdHRlcm4pO1xyXG4gICAgICAgIGlmICghbWF0Y2gpIHJldHVybjtcclxuXHJcbiAgICAgICAgY29uc3QgbWVudGlvbktleSA9IG1hdGNoWzFdO1xyXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nRGVmYXVsdCA9IG1hdGNoWzJdIHx8IFwiXCI7XHJcbiAgICAgICAgc2V0TW9kYWxTdGF0ZSh7XHJcbiAgICAgICAgICAgIGlzT3BlbjogdHJ1ZSxcclxuICAgICAgICAgICAgc3BhbixcclxuICAgICAgICAgICAgbWVudGlvbktleSxcclxuICAgICAgICAgICAgY3VycmVudERlZmF1bHQ6IGV4aXN0aW5nRGVmYXVsdCxcclxuICAgICAgICB9KTtcclxuICAgICAgICBzZXREcmFmdERlZmF1bHQoZXhpc3RpbmdEZWZhdWx0KTtcclxuICAgIH1cclxuXHJcbiAgICBmdW5jdGlvbiBjb25maXJtRGVmYXVsdCgpIHtcclxuICAgICAgICBjb25zdCB7c3BhbiwgbWVudGlvbktleX0gPSBtb2RhbFN0YXRlO1xyXG4gICAgICAgIGlmICghc3Bhbikge1xyXG4gICAgICAgICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgaW5mbyA9IGtleU1hcFttZW50aW9uS2V5XTtcclxuICAgICAgICBpZiAoIWluZm8pIHtcclxuICAgICAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCB1c2VyRGVmYXVsdCA9IGRyYWZ0RGVmYXVsdC50cmltKCk7XHJcbiAgICAgICAgbGV0IG5ld1ZhbHVlID0gYHt7JHttZW50aW9uS2V5fX19YDtcclxuICAgICAgICBsZXQgbmV3TGFiZWwgPSBpbmZvLmxhYmVsO1xyXG4gICAgICAgIGlmICh1c2VyRGVmYXVsdCAhPT0gXCJcIikge1xyXG4gICAgICAgICAgICBuZXdWYWx1ZSA9IGB7eyR7bWVudGlvbktleX0vJHt1c2VyRGVmYXVsdH19fWA7XHJcbiAgICAgICAgICAgIG5ld0xhYmVsID0gYCR7aW5mby5sYWJlbH0gLyAke3VzZXJEZWZhdWx0fWA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZShcImRhdGEtdGFnXCIsIG5ld1ZhbHVlKTtcclxuICAgICAgICBzcGFuLnRleHRDb250ZW50ID0gbmV3TGFiZWw7XHJcbiAgICAgICAgc3Bhbi5vbmRibGNsaWNrID0gKCkgPT4ge1xyXG4gICAgICAgICAgICBvcGVuRGVmYXVsdE1vZGFsKHNwYW4pO1xyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIHVwZGF0ZVZhbHVlKCk7XHJcbiAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgfVxyXG5cclxuICAgIGZ1bmN0aW9uIGNsb3NlTW9kYWwoKSB7XHJcbiAgICAgICAgc2V0TW9kYWxTdGF0ZSh7XHJcbiAgICAgICAgICAgIGlzT3BlbjogZmFsc2UsXHJcbiAgICAgICAgICAgIHNwYW46IG51bGwsXHJcbiAgICAgICAgICAgIG1lbnRpb25LZXk6IFwiXCIsXHJcbiAgICAgICAgICAgIGN1cnJlbnREZWZhdWx0OiBcIlwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuc3VyZSBleGlzdGluZyBtZW50aW9uIHNwYW5zIGFyZSBjbGlja2FibGUgdG8gb3BlbiB0aGUgbW9kYWwuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmICghZGl2UmVmLmN1cnJlbnQgfHwgZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICBjb25zdCBtZW50aW9uU3BhbnMgPSBkaXZSZWYuY3VycmVudC5xdWVyeVNlbGVjdG9yQWxsKFwic3BhbltkYXRhLXRhZ11cIik7XHJcbiAgICAgICAgbWVudGlvblNwYW5zLmZvckVhY2goKGVsKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHNwYW4gPSBlbCBhcyBIVE1MU3BhbkVsZW1lbnQ7XHJcbiAgICAgICAgICAgIGlmICghc3Bhbi5vbmRibGNsaWNrKSB7XHJcbiAgICAgICAgICAgICAgICBzcGFuLm9uZGJsY2xpY2sgPSAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgb3BlbkRlZmF1bHRNb2RhbChzcGFuKTtcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgIH0sIFtkaXNhYmxlZF0pO1xyXG5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbiAgICAvLyBSZW5kZXJcclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG4gICAgLy8gY29uc29sZS5sb2coXCJSZW5kZXI6XCIsIHtrZXlNYXAsIHZhbHVlLCBkZWZhdWx0VmFsdWV9KVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbUkgcmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgaWQ9e2lkfVxyXG4gICAgICAgICAgICAgICAgcmVmPXtkaXZSZWZ9XHJcbiAgICAgICAgICAgICAgICAvLyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbm9uZSB0ZXh0LXhzIGZvbnQtbWVkaXVtIGZvY3VzOmJvcmRlci1ibGFjayBmb2N1cy13aXRoaW46Ym9yZGVyLWJsYWNrIGdyb3VwXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKGBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLW5vbmUgcHgtMyBweS0xIHNoYWRvdy1zbSB0ZXh0LXhzIG91dGxpbmUtbm9uZSBmb2N1cy13aXRoaW46Ym9yZGVyLWJsYWNrIG1pbi1oLTcgZm9udC1tZWRpdW0gbGVhZGluZy02YCwgY2xhc3NOYW1lKX1cclxuICAgICAgICAgICAgICAgIGNvbnRlbnRFZGl0YWJsZT17IWRpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3t3aGl0ZVNwYWNlOiBcInByZS13cmFwXCJ9fVxyXG4gICAgICAgICAgICAgICAgc3VwcHJlc3NDb250ZW50RWRpdGFibGVXYXJuaW5nXHJcbiAgICAgICAgICAgICAgICBkYXRhLXBsYWNlaG9sZGVyPXshZGlzYWJsZWQgPyBwbGFjZWhvbGRlciA6IFwiXCJ9XHJcbiAgICAgICAgICAgICAgICBvbklucHV0PXtvbklucHV0fVxyXG4gICAgICAgICAgICAgICAgb25LZXlEb3duPXtvbktleURvd259XHJcbiAgICAgICAgICAgICAgICBvbkJsdXI9e2hhbmRsZUJsdXJ9XHJcbiAgICAgICAgICAgICAgICBhcmlhLWRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIHttZW50aW9uTW9kZSAmJiAhZGlzYWJsZWQgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgIHJlZj17cG9wdXBSZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgei1bOTk5OTk5XSBiZy13aGl0ZSBib3JkZXIgcm91bmRlZC1ub25lIHNoYWRvdyB0ZXh0LXhzIG1pbi13LTgwIG1heC13LTJ4bFwiXHJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3Nob3dBYm92ZSA/IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgYm90dG9tOiBcIjEwMCVcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogXCIwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogXCIycHhcIlxyXG4gICAgICAgICAgICAgICAgICAgIH0gOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvcDogXCIxMDAlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6IFwiMFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXJnaW5Ub3A6IFwiMnB4XCJcclxuICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWF4LWgtNjBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LW5vbmVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZj17bWVudGlvbklucHV0UmVmfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1iIHAtMi41IGZvbnQtbWVkaXVtIHctZnVsbCBvdXRsaW5lLW5vbmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGZvciBtZW50aW9uLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bWVudGlvblNlYXJjaH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE1lbnRpb25TZWFyY2goZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3ctYXV0byBmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW50aW9uTWF0Y2hlcy5sZW5ndGggPT09IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNjAwIGZvbnQtbWVkaXVtIHRleHQtc20gaXRhbGljIHAtMi41XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE5vIHJlc3VsdHNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbnRpb25NYXRjaGVzLm1hcCgobUtleSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5mbyA9IGtleU1hcFttS2V5XTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e21LZXl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImN1cnNvci1wb2ludGVyIGhvdmVyOmJnLW5ldXRyYWwtMTAwIHAtMi41IGZvbnQtbWVkaXVtIHRleHQteHMgb3ZlcmZsb3ctaGlkZGVuIHRleHQtZWxsaXBzaXMgd2hpdGVzcGFjZS1ub3dyYXBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRG93bj17KGUpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uTWVudGlvblNlbGVjdChtS2V5KTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtpbmZvLmxhYmVsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAge21vZGFsU3RhdGUuaXNPcGVuICYmICFkaXNhYmxlZCAmJiA8PlxyXG4gICAgICAgICAgICAgICAgPERpYWxvZyBkZWZhdWx0T3BlbiBvbk9wZW5DaGFuZ2U9e2Nsb3NlTW9kYWx9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cIm1heC13LVs2MDBweF0gIXJvdW5kZWQtbm9uZSBwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJmb250LWJvbGRcIj5TZXQgZGVmYXVsdCBmb3Ige21vZGFsU3RhdGUuc3Bhbj8uaW5uZXJUZXh0IHx8IG1vZGFsU3RhdGUubWVudGlvbktleX08L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0yIHB5LTQgcHQtMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGZsZXgtMSBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIGxlYWRpbmctNiB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEN1cnJlbnQgZGVmYXVsdDogXCJ7bW9kYWxTdGF0ZS5jdXJyZW50RGVmYXVsdH1cIiAobGVhdmUgYmxhbmsgdG8gcmVtb3ZlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0NhcGl0YWxpemU9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0NvcnJlY3Q9XCJvZmZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9e2B3b3JrZmxvdy1pbnB1dC1uYW1lYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RyYWZ0RGVmYXVsdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUeXBlIG5ldyBkZWZhdWx0Li4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXREcmFmdERlZmF1bHQoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLW5vbmUgdGV4dC14c1wiLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb25maXJtRGVmYXVsdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBwLTIgcHgtMyBoLWF1dG8gdy1hdXRvIHJvdW5kZWQtZnVsbCBmb250LXNlbWlib2xkIGdhcC0xIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29uZmlybVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgICAgICAgICAgIDwvRGlhbG9nPlxyXG4gICAgICAgICAgICA8Lz59XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJMYWJlbCIsIklucHV0IiwiQnV0dG9uIiwiY24iLCJNZW50aW9uSW5wdXQiLCJrZXlNYXAiLCJ2YWx1ZSIsImRlZmF1bHRWYWx1ZSIsIm9uQ2hhbmdlIiwib25CbHVyIiwib25EZWJvdW5jZUNoYW5nZSIsImRlYm91bmNlVGltZW91dE1TIiwicGxhY2Vob2xkZXIiLCJjbGFzc05hbWUiLCJpZCIsImRpc2FibGVkIiwia2VlcE5ld0xpbmUiLCJtb2RhbFN0YXRlIiwiZGl2UmVmIiwiY3VycmVudE91dHB1dCIsImRlYm91bmNlVGltZXJSZWYiLCJtZW50aW9uTW9kZSIsInNldE1lbnRpb25Nb2RlIiwibWVudGlvblNlYXJjaCIsInNldE1lbnRpb25TZWFyY2giLCJwb3B1cFJlZiIsInNob3dBYm92ZSIsInNldFNob3dBYm92ZSIsImF0UmFuZ2UiLCJzZXRBdFJhbmdlIiwibWVudGlvbklucHV0UmVmIiwic2tpcElubmVySHRtbFVwZGF0ZSIsInNldE1vZGFsU3RhdGUiLCJpc09wZW4iLCJzcGFuIiwibWVudGlvbktleSIsImN1cnJlbnREZWZhdWx0IiwiZHJhZnREZWZhdWx0Iiwic2V0RHJhZnREZWZhdWx0IiwicGFyc2VUZXh0VG9IdG1sIiwidGV4dCIsIlN0cmluZyIsIm1lbnRpb25SZWdleCIsImxhc3RJbmRleCIsInJlc3VsdEh0bWwiLCJtYXRjaCIsIm1hdGNoQWxsIiwic3RhcnQiLCJpbmRleCIsInBsYWluIiwic2FmZUh0bWwiLCJzbGljZSIsInJlcGxhY2UiLCJtZW50aW9uRGVmYXVsdCIsImFjdHVhbEtleSIsImZpbmRDYXNlSW5zZW5zaXRpdmVLZXkiLCJpbmZvIiwibGFiZWwiLCJ0cmltIiwiZGF0YVZhbHVlIiwic3Bhbkh0bWwiLCJlc2NhcGVBdHRyIiwibGVuZ3RoIiwib2JqIiwia2V5IiwibG93ZXJLZXkiLCJ0b0xvd2VyQ2FzZSIsImsiLCJPYmplY3QiLCJrZXlzIiwic3RyIiwidW5kZWZpbmVkIiwiY3VycmVudCIsImN1cnJlbnRIdG1sIiwiaW5uZXJIVE1MIiwibmV3SHRtbCIsImRvY3VtZW50IiwiYWN0aXZlRWxlbWVudCIsImdldEZpbmFsT3V0cHV0IiwicmVzdWx0IiwidHJhdmVyc2UiLCJub2RlIiwibm9kZVR5cGUiLCJOb2RlIiwiVEVYVF9OT0RFIiwibm9kZVZhbHVlIiwiRUxFTUVOVF9OT0RFIiwiZWwiLCJoYXNBdHRyaWJ1dGUiLCJnZXRBdHRyaWJ1dGUiLCJub2RlTmFtZSIsIkFycmF5IiwiZnJvbSIsImNoaWxkTm9kZXMiLCJmb3JFYWNoIiwiY2hpbGQiLCJ1cGRhdGVWYWx1ZSIsIm91dHB1dCIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJoYW5kbGVCbHVyIiwib25JbnB1dCIsIm9uS2V5RG93biIsImUiLCJ3aW5kb3ciLCJzZWwiLCJnZXRTZWxlY3Rpb24iLCJyYW5nZUNvdW50IiwiY3VycmVudFJhbmdlIiwiZ2V0UmFuZ2VBdCIsImNsb25lUmFuZ2UiLCJyZWN0IiwiZ2V0Qm91bmRpbmdDbGllbnRSZWN0Iiwid2lkdGgiLCJoZWlnaHQiLCJtYXJrZXIiLCJjcmVhdGVFbGVtZW50IiwidGV4dENvbnRlbnQiLCJpbnNlcnROb2RlIiwicGFyZW50Tm9kZSIsInJlbW92ZUNoaWxkIiwicmVtb3ZlQWxsUmFuZ2VzIiwiYWRkUmFuZ2UiLCJjbG9zZU1lbnRpb25Qb3B1cCIsImZ1enp5RmlsdGVyIiwicXVlcnkiLCJpdGVtcyIsIm5vcm1hbGl6ZWRRdWVyeSIsInJlc3VsdHMiLCJub3JtYWxpemVkS2V5Iiwibm9ybWFsaXplZExhYmVsIiwic3RhcnRzV2l0aCIsInB1c2giLCJpdGVtIiwicmFuayIsImluY2x1ZGVzIiwic29ydCIsImEiLCJiIiwibWFwIiwiciIsImFsbEtleXMiLCJtZW50aW9uTWF0Y2hlcyIsIm9uTWVudGlvblNlbGVjdCIsInJhbmdlIiwicmVtb3ZlQXRGcm9tVGV4dE5vZGUiLCJ0ZXh0Tm9kZSIsInBvcyIsImRhdGEiLCJzZWFyY2hTdGFydCIsIk1hdGgiLCJtYXgiLCJzZWFyY2hFbmQiLCJtaW4iLCJpIiwiY2hhckF0Iiwic3Vic3RyaW5nIiwibmV3UmFuZ2UiLCJjcmVhdGVSYW5nZSIsInNldFN0YXJ0IiwiY29sbGFwc2UiLCJzdGFydENvbnRhaW5lciIsInN0YXJ0T2Zmc2V0IiwibmV3Um5nIiwiY29udGFpbmVyIiwicHJldiIsImNvbnRlbnRFZGl0YWJsZSIsInNldEF0dHJpYnV0ZSIsInRhZyIsIm9uZGJsY2xpY2siLCJvcGVuRGVmYXVsdE1vZGFsIiwiZmlsbGVyIiwiY3JlYXRlVGV4dE5vZGUiLCJpbnNlcnRCZWZvcmUiLCJuZXh0U2libGluZyIsInNldFN0YXJ0QWZ0ZXIiLCJmb2N1cyIsIm9uS2V5Iiwib25DbGlja091dHNpZGUiLCJ0YXJnZXQiLCJjb250YWlucyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwidXBkYXRlUG9zaXRpb24iLCJjdXJzb3JSZWN0IiwiaW5wdXRSZWN0Iiwic3BhY2VCZWxvdyIsImlubmVySGVpZ2h0IiwiYm90dG9tIiwic3BhY2VBYm92ZSIsInRvcCIsImRyb3Bkb3duSGVpZ2h0IiwibWVudGlvblZhbHVlIiwicGF0dGVybiIsImV4aXN0aW5nRGVmYXVsdCIsImNvbmZpcm1EZWZhdWx0IiwiY2xvc2VNb2RhbCIsInVzZXJEZWZhdWx0IiwibmV3VmFsdWUiLCJuZXdMYWJlbCIsIm1lbnRpb25TcGFucyIsInF1ZXJ5U2VsZWN0b3JBbGwiLCJkaXYiLCJyZWYiLCJzdHlsZSIsIndoaXRlU3BhY2UiLCJzdXBwcmVzc0NvbnRlbnRFZGl0YWJsZVdhcm5pbmciLCJkYXRhLXBsYWNlaG9sZGVyIiwiYXJpYS1kaXNhYmxlZCIsImxlZnQiLCJtYXJnaW5Cb3R0b20iLCJtYXJnaW5Ub3AiLCJpbnB1dCIsIm1LZXkiLCJvbk1vdXNlRG93biIsInByZXZlbnREZWZhdWx0IiwiZGVmYXVsdE9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJpbm5lclRleHQiLCJ0eXBlIiwiYXV0b0NhcGl0YWxpemUiLCJhdXRvQ29ycmVjdCIsImF1dG9Db21wbGV0ZSIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});