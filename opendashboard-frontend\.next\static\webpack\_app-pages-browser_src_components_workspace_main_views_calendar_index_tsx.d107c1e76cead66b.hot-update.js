"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _views_day__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./views/day */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/day.tsx\");\n/* harmony import */ var _views_week__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./views/week */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx\");\n/* harmony import */ var _views_month__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./views/month */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/month.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventitem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventitem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/utils/dragconstraints */ \"(app-pages-browser)/./src/utils/dragconstraints.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisHorizontalIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisHorizontalIcon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewFilter */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewFilter.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSort */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSort.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo, _maybeRecord_recordInfo_record1, _maybeRecord_recordInfo1;\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords, smartUpdateViewDefinition } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (prevPeekRecordId && !peekRecordId) {\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 126,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            // Calculate isMultiDay and isAllDay properties\n            const isMultiDay = !(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(startDate, endDate);\n            const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);\n            const isAllDay = durationHours >= 23 || isMultiDay && durationHours >= 22;\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord,\n                isMultiDay,\n                isAllDay\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to update event.\");\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_41__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_42__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        // Don't allow creating events when content is locked\n        if (!canEditData || definition.lockContent) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        // Don't allow event clicks when content is locked\n        if (definition.lockContent) {\n            return;\n        }\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to delete event\");\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && !definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && !definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 hover:bg-neutral-300 text-black\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenuContent, {\n                                                className: \"w-56 rounded-none py-2 flex flex-col gap-1\",\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_30__.Label, {\n                                                        className: \"text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.LockIcon, {\n                                                                className: \"size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 18\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex-1 capitalize\",\n                                                                children: \"Lock content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 18\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_31__.Switch, {\n                                                                className: \"h-4 w-8\",\n                                                                checked: !!definition.lockContent,\n                                                                onCheckedChange: (lockContent)=>{\n                                                                    // Update the view definition with lock content using the proper method\n                                                                    smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                        lockContent\n                                                                    });\n                                                                },\n                                                                thumbClassName: \"!size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 18\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 16\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_32__.ViewFilter, {\n                                                        database: database.database,\n                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.FilterListIcon, {\n                                                                    className: \"size-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 22\n                                                                }, void 0),\n                                                                \"Default Filters\",\n                                                                definition.filter.conditions.length > 0 && \"(\".concat(definition.filter.conditions.length, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 20\n                                                        }, void 0),\n                                                        filter: definition.filter,\n                                                        onChange: (filter)=>smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                filter\n                                                            }),\n                                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id,\n                                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo1 = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo1 === void 0 ? void 0 : (_maybeRecord_recordInfo_record1 = _maybeRecord_recordInfo1.record) === null || _maybeRecord_recordInfo_record1 === void 0 ? void 0 : _maybeRecord_recordInfo_record1.databaseId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 16\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_33__.ViewSort, {\n                                                        database: database.database,\n                                                        sorts: definition.sorts,\n                                                        onChange: (sorts)=>smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                sorts\n                                                            }),\n                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.ArrowUpWideShortIcon, {\n                                                                    className: \"size-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 22\n                                                                }, void 0),\n                                                                \"Default Sorts\",\n                                                                definition.sorts.length > 0 && \"(\".concat(definition.sorts.length, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 20\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 16\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 427,\n                columnNumber: 6\n            }, undefined),\n            !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_18__.ContentLocked, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 681,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_26__.restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_day__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData && !definition.lockContent,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_week__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData && !definition.lockContent,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_month__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_27__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 685,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 426,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"OY6vt8MasBgLreSUt37ABgeJY50=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        usePrevious,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});