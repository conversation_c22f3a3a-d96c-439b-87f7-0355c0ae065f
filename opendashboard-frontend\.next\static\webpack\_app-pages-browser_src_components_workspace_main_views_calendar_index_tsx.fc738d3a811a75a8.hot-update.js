"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _views_day__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./views/day */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/day.tsx\");\n/* harmony import */ var _views_week__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./views/week */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx\");\n/* harmony import */ var _views_month__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./views/month */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/month.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventitem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventitem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/utils/dragconstraints */ \"(app-pages-browser)/./src/utils/dragconstraints.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisHorizontalIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisHorizontalIcon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewFilter */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewFilter.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSort */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSort.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo, _maybeRecord_recordInfo_record1, _maybeRecord_recordInfo1;\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords, smartUpdateViewDefinition } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (prevPeekRecordId && !peekRecordId) {\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 126,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            // Calculate isMultiDay and isAllDay properties\n            const isMultiDay = !(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(startDate, endDate);\n            const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);\n            const isAllDay = durationHours >= 23 || isMultiDay && durationHours >= 22;\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord,\n                isMultiDay,\n                isAllDay\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to update event.\");\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_41__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_42__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_23__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        // Don't allow event clicks when content is locked\n        if (definition.lockContent) {\n            return;\n        }\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_43__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to delete event\");\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_24__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 571,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 hover:bg-neutral-300 text-black\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_44__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_29__.DropdownMenuContent, {\n                                                className: \"w-56 rounded-none py-2 flex flex-col gap-1\",\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_30__.Label, {\n                                                        className: \"text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.LockIcon, {\n                                                                className: \"size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 18\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex-1 capitalize\",\n                                                                children: \"Lock content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 18\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_31__.Switch, {\n                                                                className: \"h-4 w-8\",\n                                                                checked: !!definition.lockContent,\n                                                                onCheckedChange: (lockContent)=>{\n                                                                    // Update the view definition with lock content using the proper method\n                                                                    smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                        lockContent\n                                                                    });\n                                                                },\n                                                                thumbClassName: \"!size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 18\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 16\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_32__.ViewFilter, {\n                                                        database: database.database,\n                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.FilterListIcon, {\n                                                                    className: \"size-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 22\n                                                                }, void 0),\n                                                                \"Default Filters\",\n                                                                definition.filter.conditions.length > 0 && \"(\".concat(definition.filter.conditions.length, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 20\n                                                        }, void 0),\n                                                        filter: definition.filter,\n                                                        onChange: (filter)=>smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                filter\n                                                            }),\n                                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id,\n                                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo1 = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo1 === void 0 ? void 0 : (_maybeRecord_recordInfo_record1 = _maybeRecord_recordInfo1.record) === null || _maybeRecord_recordInfo_record1 === void 0 ? void 0 : _maybeRecord_recordInfo_record1.databaseId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 16\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_33__.ViewSort, {\n                                                        database: database.database,\n                                                        sorts: definition.sorts,\n                                                        onChange: (sorts)=>smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                sorts\n                                                            }),\n                                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                            variant: \"ghost\",\n                                                            className: \"text-xs rounded-none p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.ArrowUpWideShortIcon, {\n                                                                    className: \"size-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 22\n                                                                }, void 0),\n                                                                \"Default Sorts\",\n                                                                definition.sorts.length > 0 && \"(\".concat(definition.sorts.length, \")\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 20\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 16\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_28__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 426,\n                columnNumber: 6\n            }, undefined),\n            !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_18__.ContentLocked, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 680,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_26__.restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_day__WEBPACK_IMPORTED_MODULE_19__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_week__WEBPACK_IMPORTED_MODULE_20__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_month__WEBPACK_IMPORTED_MODULE_21__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_27__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_22__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 684,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"OY6vt8MasBgLreSUt37ABgeJY50=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        usePrevious,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_25__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});