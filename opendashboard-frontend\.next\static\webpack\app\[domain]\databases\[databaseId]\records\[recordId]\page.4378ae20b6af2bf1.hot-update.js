"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/records/[recordId]/page",{

/***/ "(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/workspace/main/database/configureTitleDialog.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigureTitleDialog: function() { return /* binding */ ConfigureTitleDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _api_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/database */ \"(app-pages-browser)/./src/api/database.ts\");\n/* harmony import */ var _components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom-ui/mentionInput */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConfigureTitleDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst ConfigureTitleDialog = (param)=>{\n    let { database, close } = param;\n    var _database_definition, _database_definition1;\n    _s();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_4__.useAlert)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace)();\n    const { refreshDatabase } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_7__.useViews)();\n    const getInitialTitleFormat = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _database_definition, _database_definition1;\n        if ((database === null || database === void 0 ? void 0 : (_database_definition = database.definition) === null || _database_definition === void 0 ? void 0 : _database_definition.titleFormat) && typeof database.definition.titleFormat === \"string\") {\n            return database.definition.titleFormat;\n        }\n        if ((database === null || database === void 0 ? void 0 : (_database_definition1 = database.definition) === null || _database_definition1 === void 0 ? void 0 : _database_definition1.titleColumnId) && typeof database.definition.titleColumnId === \"string\") {\n            return \"{{\".concat(database.definition.titleColumnId, \"}}\");\n        }\n        return \"\";\n    }, [\n        database === null || database === void 0 ? void 0 : (_database_definition = database.definition) === null || _database_definition === void 0 ? void 0 : _database_definition.titleFormat,\n        database === null || database === void 0 ? void 0 : (_database_definition1 = database.definition) === null || _database_definition1 === void 0 ? void 0 : _database_definition1.titleColumnId\n    ]);\n    const [titleFormat, setTitleFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTitleFormat());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setTitleFormat(getInitialTitleFormat());\n    }, [\n        getInitialTitleFormat\n    ]);\n    const keyMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const map = {};\n        Object.values(database.definition.columnsMap).forEach((column)=>{\n            map[column.id] = {\n                label: column.title,\n                tag: \"{{\".concat(column.id, \"}}\")\n            };\n        });\n        return map;\n    }, [\n        database.definition.columnsMap\n    ]);\n    const handleSave = async ()=>{\n        try {\n            if (!titleFormat || typeof titleFormat !== \"string\" || !titleFormat.trim()) {\n                toast.error(\"Please configure a title format\");\n                return;\n            }\n            if (!token) {\n                toast.error(\"Authentication required\");\n                return;\n            }\n            const initialTitleFormat = getInitialTitleFormat();\n            if (titleFormat.trim() === initialTitleFormat.trim()) {\n                toast.info(\"Title format is already up to date\");\n                return;\n            }\n            const response = await (0,_api_database__WEBPACK_IMPORTED_MODULE_8__.setTitleFormat)(token.token, workspace.workspace.id, database.id, titleFormat);\n            if (response.isSuccess && !response.error) {\n                await refreshDatabase(database.id);\n                toast.success(\"Title format updated successfully\");\n                close();\n            } else {\n                throw new Error(response.error || \"Failed to update title format\");\n            }\n        } catch (error) {\n            console.error(\"Error saving title format:\", error);\n            toast.error(\"Failed to update title format\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: true,\n        onOpenChange: ()=>{},\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"max-w-[95vw] sm:max-w-[600px] max-h-[90vh] overflow-y-auto !rounded-none p-0\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    className: \"p-4 border-b flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: close,\n                                    className: \"p-1 h-6 w-6 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_10__.Cross2Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Configure Record Title\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: close,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSave,\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full bg-black text-white hover:bg-gray-800\",\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 sm:p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                    children: \"Title Format\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__.MentionInput, {\n                                    keyMap: keyMap,\n                                    value: titleFormat,\n                                    onChange: setTitleFormat,\n                                    placeholder: \"Type @ to mention columns... e.g., @firstName @lastName\",\n                                    className: \"w-full min-h-12 text-xs border rounded-none p-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: \"Example: @firstName @lastName - text\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n            lineNumber: 87,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n        lineNumber: 86,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ConfigureTitleDialog, \"skYqUgjDFuuz2j1Icn3ywWuy6aQ=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_4__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_7__.useViews\n    ];\n});\n_c = ConfigureTitleDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfigureTitleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx\n"));

/***/ })

});