"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_board_board_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    const [positionCalculated, setPositionCalculated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // ensure position is calculated before showing\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Calculate position immediately to prevent flash\n                if (divRef.current) {\n                    const inputRect = divRef.current.getBoundingClientRect();\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250;\n                    const minSpaceRequired = 100;\n                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                    setShowAbove(shouldShowAbove);\n                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                    setPopupPosition({\n                        left: inputRect.left + scrollX,\n                        top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                    });\n                    // Mark position as calculated\n                    setPositionCalculated(true);\n                }\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n        setPositionCalculated(false); // Reset position calculated state\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is inside the input field\n            if (divRef.current && divRef.current.contains(target)) {\n                return; // Don't close if clicking inside input\n            }\n            // Check if click is inside the popup (now rendered as portal)\n            if (popupRef.current && popupRef.current.contains(target)) {\n                return; // Don't close if clicking inside popup\n            }\n            // Close if click is outside both input and popup\n            closeMentionPopup();\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            // Use click instead of mousedown to allow dropdown interactions to process first\n            document.addEventListener(\"click\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"click\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Update position on window resize or scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            // Only listen for resize and scroll events, don't run immediately\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 598,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && positionCalculated && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\",\n                    pointerEvents: \"auto\",\n                    border: \"2px solid red\" // Temporary visual indicator\n                },\n                onClick: ()=>console.log(\"Dropdown container clicked\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    style: {\n                                        pointerEvents: \"auto\"\n                                    },\n                                    onClick: (e)=>{\n                                        console.log(\"Dropdown item clicked:\", mKey);\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        onMentionSelect(mKey);\n                                    },\n                                    onMouseEnter: ()=>console.log(\"Mouse enter:\", mKey),\n                                    onMouseDown: (e)=>{\n                                        console.log(\"Mouse down:\", mKey);\n                                        e.preventDefault();\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 635,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 614,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 674,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 673,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 676,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 597,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"LwTjOlhT2DVbFeCb0bHxaVUm1S0=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx":
/*!*************************************************************************************!*\
  !*** ./src/components/workspace/main/views/form/components/element/buttonGroup.tsx ***!
  \*************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonGroupFieldArea: function() { return /* binding */ ButtonGroupFieldArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/utils/buttonActionHelpers */ \"(app-pages-browser)/./src/utils/buttonActionHelpers.ts\");\n/* harmony import */ var _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/utils/buttonAction */ \"(app-pages-browser)/./src/utils/buttonAction.ts\");\n/* __next_internal_client_entry_do_not_use__ ButtonGroupFieldArea auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ButtonGroupFieldArea = (props)=>{\n    _s();\n    const { updateRecordValues, deleteRecords, directDeleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    const { confirm, toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { token, user } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { sendMessage } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const inputDialog = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog)();\n    const { id, columnsMap, databaseId, values } = props;\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek)();\n    const column = columnsMap[id];\n    const buttons = column.buttons || [];\n    const database = databaseStore === null || databaseStore === void 0 ? void 0 : databaseStore[databaseId];\n    if (!database) {\n        return null;\n    }\n    const row = {\n        id: String(values.id || \"\"),\n        recordValues: values\n    };\n    const handleButtonClick = async (button)=>{\n        var _button_actions;\n        if (!(button === null || button === void 0 ? void 0 : (_button_actions = button.actions) === null || _button_actions === void 0 ? void 0 : _button_actions.length)) {\n            toast.info(\"This button has no actions configured\");\n            return;\n        }\n        const context = {\n            record: row,\n            database: database,\n            workspace: workspace,\n            token: token,\n            user: user,\n            databaseId,\n            // Manually add the parent record context if we are in a peek view\n            parentRecord: maybeRecord ? {\n                id: maybeRecord.recordInfo.record.id,\n                databaseId: maybeRecord.database.id\n            } : undefined\n        };\n        const services = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.createActionServices)({\n            updateRecordValues: updateRecordValues,\n            deleteRecords,\n            directDeleteRecords,\n            setPeekRecord: (recordId, databaseId)=>openRecord(recordId, databaseId),\n            confirm,\n            toast,\n            router,\n            forceRender,\n            sendMessage: (message)=>sendMessage(\"info\", \"action\", {\n                    message\n                })\n        }, inputDialog);\n        const urlsToOpen = [];\n        let actionSucceeded = true;\n        for (const action of button.actions){\n            const { success, result } = await (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.executeDeclarativeAction)(action, context, services, databaseStore);\n            if (!success) {\n                actionSucceeded = false;\n                break;\n            }\n            if (action.actionType === \"openUrl\" && (result === null || result === void 0 ? void 0 : result.url)) {\n                urlsToOpen.push(result.url);\n            }\n        }\n        if (urlsToOpen.length > 0) {\n            services.toast.success(\"Opening \".concat(urlsToOpen.length, \" URL(s)...\"));\n            urlsToOpen.forEach((url)=>{\n                window.open(url, \"_blank\", \"noopener,noreferrer\");\n            });\n        }\n        if (actionSucceeded) {} else {}\n        forceRender();\n    };\n    const buttonStates = buttons.map((button)=>({\n            button,\n            state: (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.evaluateButtonState)(button, values || {})\n        }));\n    const visibleButtons = buttons.filter((button)=>{\n        var _buttonStates_find;\n        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n        return (buttonState === null || buttonState === void 0 ? void 0 : buttonState.visible) !== false;\n    });\n    if (!visibleButtons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-wrap gap-2 button-group-container\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 button-group-container\",\n                children: visibleButtons.length === 1 ? (()=>{\n                    var _buttonStates_find;\n                    const button = visibleButtons[0];\n                    const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                    const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                    const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                        disabled: isDisabled || hasError,\n                        variant: \"outline\",\n                        className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                        children: [\n                            hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 19\n                            }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                className: \"size-3 text-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 19\n                            }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate\",\n                                children: button.label || \"Action\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 35\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 15\n                    }, undefined);\n                })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-1 flex-wrap sm:flex-nowrap w-full\",\n                    children: [\n                        (()=>{\n                            var _buttonStates_find;\n                            const button = visibleButtons[0];\n                            const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                            const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                            const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                disabled: isDisabled || hasError,\n                                variant: \"outline\",\n                                className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                                children: [\n                                    hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 21\n                                    }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                        className: \"size-3 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"truncate\",\n                                        children: button.label || \"Action\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 17\n                            }, undefined);\n                        })(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"text-xs rounded-full p-1.5 h-auto\",\n                                        variant: \"outline\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"size-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    className: \"w-48 z-[99999]\",\n                                    sideOffset: 5,\n                                    children: visibleButtons.slice(1).map((button)=>{\n                                        var _buttonStates_find;\n                                        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                                        const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.ERROR;\n                                        const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_18__.ButtonState.DISABLED;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                            onClick: ()=>!isDisabled && !hasError && handleButtonClick(button),\n                                            disabled: isDisabled || hasError,\n                                            className: \"text-xs p-2 gap-2 flex items-center \".concat(hasError ? \"text-gray-600 cursor-not-allowed\" : isDisabled ? \"text-gray-400 cursor-not-allowed\" : \"\"),\n                                            children: [\n                                                hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 25\n                                                }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                    className: \"size-3 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 25\n                                                }, undefined) : (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_14__.getButtonIcon)(button),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"truncate\",\n                                                    children: button.label || \"Action\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, button.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.Dialog, {\n                open: inputDialog.inputDialogOpen,\n                onOpenChange: inputDialog.setInputDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogTitle, {\n                                children: inputDialog.inputDialogTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: inputDialog.inputDialogMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                    value: inputDialog.inputValue,\n                                    onChange: (e)=>inputDialog.setInputValue(e.target.value),\n                                    placeholder: \"Enter your input here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_12__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>inputDialog.setInputDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: inputDialog.handleInputSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\form\\\\components\\\\element\\\\buttonGroup.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ButtonGroupFieldArea, \"KAV3e7PZmvIF5Zhl7XwvdCaQeXk=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_17__.useInputDialog,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_15__.useStackedPeek\n    ];\n});\n_c = ButtonGroupFieldArea;\nvar _c;\n$RefreshReg$(_c, \"ButtonGroupFieldArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/form/components/element/buttonGroup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/header.tsx":
/*!******************************************************************************!*\
  !*** ./src/components/workspace/main/views/table/renderer/common/header.tsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderRenderer: function() { return /* binding */ HeaderRenderer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisHorizontalIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisHorizontalIcon.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _components_workspace_main_database_databaseFieldTypeIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/database/databaseFieldTypeIcon */ \"(app-pages-browser)/./src/components/workspace/main/database/databaseFieldTypeIcon.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_inputWithEnter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/inputWithEnter */ \"(app-pages-browser)/./src/components/custom-ui/inputWithEnter.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _custom_ui_taggableInput_css__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./../../../../../../custom-ui/taggableInput.css */ \"(app-pages-browser)/./src/components/custom-ui/taggableInput.css\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_derived__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/derived */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/derived.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/onboarding */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/onboarding.js\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _components_workspace_main_common_databaseColumnSelect__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/workspace/main/common/databaseColumnSelect */ \"(app-pages-browser)/./src/components/workspace/main/common/databaseColumnSelect.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _components_custom_ui_taggableInput__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/custom-ui/taggableInput */ \"(app-pages-browser)/./src/components/custom-ui/taggableInput.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(app-pages-browser)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_string__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/string */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/string.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/custom-ui/dndSortable */ \"(app-pages-browser)/./src/components/custom-ui/dndSortable.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import { TagOptionsMap} from \"@/components/custom-ui/tagInput\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst companyDef = (0,opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__.getCompanyDbDefinition)();\nconst contactsDef = (0,opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__.getCustomerDbDefinition)(\"\");\nconst contactableDef = (0,opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__.getContactDbDefinition)();\nconst companiesColIds = companyDef.definition.columnIds;\nconst contactsColIds = [\n    ...contactsDef.definition.columnIds,\n    ...contactableDef.definition.columnIds\n];\nconst companySrcPackageName = (0,opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__.getDatabasePackageName)(companyDef);\nconst contactsSrcPackageName = (0,opendb_app_db_utils_lib_utils_onboarding__WEBPACK_IMPORTED_MODULE_20__.getDatabasePackageName)(contactsDef);\nconst HeaderRenderer = (param)=>{\n    let { column, sortDirection, priority } = param;\n    _s();\n    const { databaseStore, databaseErrorStore } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)();\n    const { updateDatabaseColumn, cache, deleteDatabaseColumn, updateDatabaseTitleColumn, makeDatabaseColumnUnique } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_17__.useViews)();\n    // @ts-ignore\n    const meta = column[\"__meta__\"];\n    const dbColumn = meta.column;\n    // console.log(\"Header\", {column, meta})\n    // const fieldType = column.fieldType as DatabaseFieldDataType\n    // const [isUpdating, setIsUpdating] = useState(false)\n    const shared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_22__.useMaybeShared)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(meta.triggerEdit || false);\n    const [aiPromptOpen, setAIPromptOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [scanContentOpen, setScanContentOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [formulaEditorOpen, setFormulaEditorOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [buttonGroupEditorOpen, setButtonGroupEditorOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const onUpdate = async (column)=>{\n        // setOpen(false)\n        await updateDatabaseColumn(meta.databaseId, dbColumn.id, dbColumn.type, column);\n    };\n    const headerLocked = !!meta.headerLocked;\n    let isNameLocked = false;\n    let isDeleteLocked = false;\n    const currDb = databaseStore[meta.databaseId];\n    if (currDb.database.srcPackageName === companySrcPackageName && companiesColIds.includes(dbColumn.id)) {\n        isDeleteLocked = isNameLocked = true;\n    } else if ((currDb.database.isMessagingEnabled || currDb.database.srcPackageName === contactsSrcPackageName) && contactsColIds.includes(dbColumn.id)) {\n        isDeleteLocked = isNameLocked = true;\n    }\n    const isUnique = currDb.database.definition.uniqueColumnId === dbColumn.id;\n    const onDelete = ()=>{\n        deleteDatabaseColumn(meta.databaseId, dbColumn);\n    };\n    let error = \"\";\n    if (dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked) {\n        if (!dbColumn.databaseId) error = \"Linked database not defined\";\n        else if (databaseErrorStore[dbColumn.databaseId]) error = databaseErrorStore[dbColumn.databaseId].error || \"\";\n    }\n    const setAsTitle = (setTitle)=>{\n        if (setTitle) updateDatabaseTitleColumn(currDb.database.id, dbColumn.id).then();\n        else updateDatabaseTitleColumn(currDb.database.id, \"\").then();\n    };\n    const isTitleCol = currDb.database.definition.titleColumnId === dbColumn.id;\n    const firstRenderTriggerEditRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(false);\n    const triggerEdit = meta.triggerEdit || false;\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_26__[\"default\"])();\n    const openModal = !firstRenderTriggerEditRef.current && triggerEdit || open;\n    // console.log({triggerEdit, open, openModal})\n    const onOpenChange = (o)=>{\n        firstRenderTriggerEditRef.current = true;\n        setOpen(o);\n        if (o === open) forceRender();\n    };\n    // useEffect(() => {\n    //     if (triggerEdit) {\n    //         setTimeout(() => {\n    //             firstRenderTriggerEditRef.current = true\n    //         }, 500)\n    //     }\n    // }, [triggerEdit])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"r-header text-xs h-full flex items-center font-semibold gap-2 relative group\",\n        onContextMenu: (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (!headerLocked) {\n                onOpenChange(true);\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_database_databaseFieldTypeIcon__WEBPACK_IMPORTED_MODULE_5__.DatabaseFieldTypeIcon, {\n                type: dbColumn.type\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 145,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 truncate\",\n                children: dbColumn.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 146,\n                columnNumber: 13\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative text-red-600 -right-8 group-hover:right-0\",\n                title: error,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_19__.CircleExclamationIcon, {\n                    className: \"size-3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 147,\n                columnNumber: 23\n            }, undefined),\n            !shared && !headerLocked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n                    // defaultOpen={open || meta.triggerEdit || false}\n                    open: openModal,\n                    onOpenChange: onOpenChange,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                className: \"rounded-full h-auto p-1 -mr-1.5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                    className: \"size-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                            className: \"w-56  rounded-none text-neutral-800 font-semibold\",\n                            align: \"end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                    className: \"p-1 flex flex-col gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_inputWithEnter__WEBPACK_IMPORTED_MODULE_8__.InputWithEnter, {\n                                            value: dbColumn.title,\n                                            placeHolder: \"Field name\",\n                                            disabled: isNameLocked,\n                                            onChange: (v)=>{\n                                                onUpdate({\n                                                    title: v.trim()\n                                                }).then();\n                                            },\n                                            wrapperClassname: \"h-8 p-1\",\n                                            shortEnter: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.TextHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 77\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Number ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_6__.NumberHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 79\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_18__.LinkedHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 30\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Summarize ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_24__.SummarizeHeaderDropDownMore, {\n                                            database: currDb.database,\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 30\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Select || dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked || dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Person ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_9__.MultiSelectHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 30\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Files ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_23__.FileHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 78\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.AI ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__.AIHeaderDropDownMore, {\n                                                editPrompt: ()=>setAIPromptOpen(true),\n                                                onUpdate: onUpdate,\n                                                column: dbColumn\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_29__.ScannableCodeHeaderDropDownMore, {\n                                                editContent: ()=>setScanContentOpen(true),\n                                                onUpdate: onUpdate,\n                                                column: dbColumn\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_derived__WEBPACK_IMPORTED_MODULE_16__.DerivedDropDownMore, {\n                                                edit: ()=>setFormulaEditorOpen(true),\n                                                onUpdate: onUpdate,\n                                                column: dbColumn\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ButtonGroup ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonGroupHeaderDropDownMore, {\n                                                editButtons: ()=>setButtonGroupEditorOpen(true),\n                                                onUpdate: onUpdate,\n                                                column: dbColumn\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 5\n                                            }, undefined)\n                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                                        dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Date || dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedAt || dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedAt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_10__.DateHeaderDropDownMore, {\n                                            onUpdate: onUpdate,\n                                            column: dbColumn\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 89\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 25\n                                }, undefined),\n                                [\n                                    opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text,\n                                    opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UUID\n                                ].includes(dbColumn.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                className: \"p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_21__.Switch, {\n                                                        className: \"h-4 w-8\",\n                                                        thumbClassName: \"!size-3\",\n                                                        onCheckedChange: setAsTitle,\n                                                        checked: isTitleCol\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Set as title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                className: \"p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_21__.Switch, {\n                                                        className: \"h-4 w-8\",\n                                                        thumbClassName: \"!size-3\",\n                                                        onCheckedChange: (unique)=>makeDatabaseColumnUnique(currDb.database.id, dbColumn.id, unique),\n                                                        checked: isUnique\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Enforce Uniqueness\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                [\n                                    opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text\n                                ].includes(dbColumn.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                className: \"p-2 pl-1 text-xs w-full text-left justify-start items-center font-semibold rounded-none gap-2 hover:border-neutral-200 flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_21__.Switch, {\n                                                        className: \"h-4 w-8\",\n                                                        thumbClassName: \"!size-3\",\n                                                        onCheckedChange: (c)=>onUpdate({\n                                                                isLong: c\n                                                            }),\n                                                        checked: dbColumn.isLong\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Enable long text\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 37\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                !isDeleteLocked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 29\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuGroup, {\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                                                className: \"text-xs rounded-none p-2\",\n                                                onClick: onDelete,\n                                                children: \"Delete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.AI && aiPromptOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIPromptEditor, {\n                    column: dbColumn,\n                    databaseId: meta.databaseId,\n                    close: ()=>setAIPromptOpen(false),\n                    onUpdate: onUpdate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode && scanContentOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScannableContentEditor, {\n                    column: dbColumn,\n                    databaseId: meta.databaseId,\n                    close: ()=>setScanContentOpen(false),\n                    onUpdate: onUpdate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            dbColumn.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived && formulaEditorOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_derived__WEBPACK_IMPORTED_MODULE_16__.DerivedFormulaEditor, {\n                    database: currDb,\n                    column: dbColumn,\n                    doUpdate: onUpdate,\n                    close: ()=>setFormulaEditorOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            buttonGroupEditorOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonGroupConfigEditor, {\n                column: dbColumn,\n                databaseId: meta.databaseId,\n                close: ()=>setButtonGroupEditorOpen(false),\n                onUpdate: onUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 322,\n                columnNumber: 39\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n        lineNumber: 137,\n        columnNumber: 9\n    }, undefined);\n};\n_s(HeaderRenderer, \"cQ+8HJfrKvS856zRDZi2qjy2j/E=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_17__.useViews,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_22__.useMaybeShared,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n    ];\n});\n_c = HeaderRenderer;\nconst AIPromptEditor = (props)=>{\n    var _databaseStore_props_databaseId_database_definition, _databaseStore_props_databaseId_database, _databaseStore_props_databaseId;\n    _s1();\n    const dbColumn = props.column;\n    const { databaseStore } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dbColumn.title);\n    const [maxWordOutput, setMaxWordOutput] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dbColumn.maxWordOutput || 1000);\n    const [attachmentColumnIds, setAttachmentColumnIds] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(dbColumn.attachmentColumnIds || []);\n    // const [prompt, setPrompt] = useState(dbColumn.prompt || '')\n    const promptRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(dbColumn.prompt || \"\");\n    const columnsMap = (_databaseStore_props_databaseId = databaseStore[props.databaseId]) === null || _databaseStore_props_databaseId === void 0 ? void 0 : (_databaseStore_props_databaseId_database = _databaseStore_props_databaseId.database) === null || _databaseStore_props_databaseId_database === void 0 ? void 0 : (_databaseStore_props_databaseId_database_definition = _databaseStore_props_databaseId_database.definition) === null || _databaseStore_props_databaseId_database_definition === void 0 ? void 0 : _databaseStore_props_databaseId_database_definition.columnsMap;\n    const tagOptions = ()=>{\n        if (!columnsMap) return {};\n        const tagOptions = {};\n        for (const col of Object.values(columnsMap)){\n            if (col.id === dbColumn.id) continue;\n            // if ([DatabaseFieldDataType.Files].includes(dbColumn.type)) continue\n            tagOptions[col.id] = {\n                tag: \"{{\".concat(col.id, \"}}\"),\n                description: \"\",\n                label: col.title\n            };\n        }\n        return tagOptions;\n    };\n    const tagOpts = tagOptions();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                hideCloseBtn: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                            className: \"font-bold text-sm\",\n                            children: \"AI Prompt\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col flex-1 gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                        children: \"Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                        placeholder: \"Field name\",\n                                        value: title,\n                                        onChange: (e)=>setTitle(e.target.value),\n                                        autoCapitalize: \"none\",\n                                        autoComplete: \"off\",\n                                        spellCheck: \"false\",\n                                        autoCorrect: \"off\",\n                                        className: \"rounded-none text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col flex-1 gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                        children: \"Write your prompt, (mention @columns as needed)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_taggableInput__WEBPACK_IMPORTED_MODULE_27__.TaggableInput, {\n                                        id: \"aiPromptTagInput\",\n                                        tagOptionsMap: tagOpts,\n                                        value: dbColumn.prompt || \"\",\n                                        onChange: (v)=>{\n                                            console.log(\"On change:\", v);\n                                            promptRef.current = v;\n                                        },\n                                        stripTagsInOutput: true,\n                                        placeholder: \"Write your prompt, mention @columns as needed\",\n                                        showVariables: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col flex-1 gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                        children: \"Max Output Words (in 1000s, rounded up)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                        placeholder: \"Max output words\",\n                                        value: maxWordOutput,\n                                        onChange: (e)=>{\n                                            const num = Number(e.target.value);\n                                            setMaxWordOutput(num);\n                                        },\n                                        autoCapitalize: \"none\",\n                                        autoComplete: \"off\",\n                                        spellCheck: \"false\",\n                                        autoCorrect: \"off\",\n                                        type: \"number\",\n                                        step: 1000,\n                                        min: 1000,\n                                        className: \"rounded-none text-xs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-muted-foreground text-xs text-[11px]\",\n                                        children: [\n                                            \"Max tokens sent to the AI, rounded up to the next 1000.\\xa0\",\n                                            Math.max(maxWordOutput, 1000),\n                                            \" words = \",\n                                            Math.ceil(Math.max(maxWordOutput, 1000) / 1000),\n                                            \" content unit.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col flex-1 gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                        children: \"Attachments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_databaseColumnSelect__WEBPACK_IMPORTED_MODULE_25__.DatabaseColumnSelect, {\n                                        databaseId: props.databaseId,\n                                        isMultiple: true,\n                                        selected: attachmentColumnIds,\n                                        filterFor: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Files,\n                                        onChange: setAttachmentColumnIds\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-muted-foreground text-xs text-[11px]\",\n                                        children: \"Text from attachments is extracted and sent to the AI. PDFs and images use OCR. Large files may increase AI content unit usage.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    props.onUpdate({\n                                        prompt: promptRef.current,\n                                        title: title.trim() || dbColumn.title,\n                                        attachmentColumnIds,\n                                        maxWordOutput: Math.max(maxWordOutput, 1000)\n                                    });\n                                    props.close();\n                                },\n                                className: \"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: props.close,\n                                variant: \"ghost\",\n                                className: \"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 368,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n            lineNumber: 367,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n_s1(AIPromptEditor, \"hhHefpFTm9DPTujSYKcZhl6/ONU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace\n    ];\n});\n_c1 = AIPromptEditor;\nconst ScannableContentEditor = (props)=>{\n    var _databaseStore_props_databaseId_database_definition, _databaseStore_props_databaseId_database, _databaseStore_props_databaseId;\n    _s2();\n    const dbColumn = props.column;\n    const { databaseStore } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)();\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(dbColumn.derivation || \"\");\n    const columnsMap = (_databaseStore_props_databaseId = databaseStore[props.databaseId]) === null || _databaseStore_props_databaseId === void 0 ? void 0 : (_databaseStore_props_databaseId_database = _databaseStore_props_databaseId.database) === null || _databaseStore_props_databaseId_database === void 0 ? void 0 : (_databaseStore_props_databaseId_database_definition = _databaseStore_props_databaseId_database.definition) === null || _databaseStore_props_databaseId_database_definition === void 0 ? void 0 : _databaseStore_props_databaseId_database_definition.columnsMap;\n    const tagOptions = ()=>{\n        if (!columnsMap) return {};\n        const tagOptions = {};\n        for (const col of Object.values(columnsMap)){\n            if (col.id === dbColumn.id || col.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode) continue;\n            tagOptions[col.id] = {\n                tag: \"{{\".concat(col.id, \"}}\"),\n                description: \"\",\n                label: col.title\n            };\n        }\n        return tagOptions;\n    };\n    const tagOpts = tagOptions();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                hideCloseBtn: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                            className: \"font-bold text-sm\",\n                            children: \"Scannable Code Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col flex-1 gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                    className: \"block text-xs font-medium leading-6 text-gray-900\",\n                                    children: \"Write your prompt, (mention @columns as needed)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_taggableInput__WEBPACK_IMPORTED_MODULE_27__.TaggableInput, {\n                                    id: \"scanCodePromptTag\",\n                                    tagOptionsMap: tagOpts,\n                                    value: dbColumn.derivation || \"\",\n                                    onChange: (v)=>{\n                                        console.log(\"On change:\", v);\n                                        contentRef.current = v;\n                                    },\n                                    stripTagsInOutput: true,\n                                    placeholder: \"Enter the content of the code, mention @columns as needed\",\n                                    showVariables: true,\n                                    debounceTimeoutMS: 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: ()=>{\n                                    props.onUpdate({\n                                        derivation: contentRef.current\n                                    });\n                                    props.close();\n                                },\n                                className: \"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                onClick: props.close,\n                                variant: \"ghost\",\n                                className: \"mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 491,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n            lineNumber: 490,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n_s2(ScannableContentEditor, \"QznpxfvsY1IaXBvnbxLyA+ZrVrc=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace\n    ];\n});\n_c2 = ScannableContentEditor;\nconst ButtonGroupHeaderDropDownMore = (props)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n            onClick: props.editButtons,\n            className: \"text-xs rounded-none p-2\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-1 rounded-full capitalize\",\n                children: \"Edit Buttons\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 547,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n            lineNumber: 543,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\n_c3 = ButtonGroupHeaderDropDownMore;\nconst ButtonGroupConfigEditor = (props)=>{\n    _s3();\n    const dbColumn = props.column;\n    const buttonEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const [buttons, setButtons] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(Array.isArray(dbColumn.buttons) ? dbColumn.buttons.map((button, index)=>{\n        if (\"actions\" in button && Array.isArray(button.actions)) {\n            return button;\n        } else if (\"actionType\" in button) {\n            const newButton = {\n                id: button.id,\n                label: button.label,\n                isReady: button.isReady,\n                actions: [\n                    button\n                ]\n            };\n            return newButton;\n        } else {\n            return {\n                id: button.id,\n                label: button.label,\n                isReady: button.isReady,\n                actions: []\n            };\n        }\n    }) : []);\n    const [selectedButtonIndex, setSelectedButtonIndex] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const { confirm } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_33__.useAlert)();\n    const addButton = ()=>{\n        const newButton = {\n            id: (0,opendb_app_db_utils_lib_methods_string__WEBPACK_IMPORTED_MODULE_30__.generateUUID)(),\n            label: \"New Button\",\n            isReady: true,\n            actions: []\n        };\n        setButtons([\n            ...buttons,\n            newButton\n        ]);\n        setSelectedButtonIndex(buttons.length);\n    };\n    const updateButton = (index, updatedButton)=>{\n        const newButtons = [\n            ...buttons\n        ];\n        newButtons[index] = updatedButton;\n        setButtons(newButtons);\n    };\n    const deleteButton = (index)=>{\n        confirm(\"Delete Button\", \"Are you sure you want to delete this button?\", ()=>{\n            const newButtons = [\n                ...buttons\n            ];\n            newButtons.splice(index, 1);\n            setButtons(newButtons);\n        }, undefined, undefined, true);\n    };\n    const reorderButtons = (items)=>{\n        setButtons(items.map((item)=>item.data));\n    };\n    const saveChanges = ()=>{\n        props.onUpdate({\n            buttons: buttons\n        });\n        props.close();\n    };\n    const handleSaveButtonEditor = ()=>{\n        var _buttonEditorRef_current;\n        (_buttonEditorRef_current = buttonEditorRef.current) === null || _buttonEditorRef_current === void 0 ? void 0 : _buttonEditorRef_current.save();\n    };\n    if (selectedButtonIndex !== null) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n            open: true,\n            onOpenChange: ()=>{},\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n                className: \"max-w-2xl max-h-[90vh] !rounded-none p-0\",\n                hideCloseBtn: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                        className: \"p-4 border-b flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedButtonIndex(null),\n                                        className: \"p-1 h-6 w-6 hover:bg-gray-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_35__.Cross2Icon, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 650,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                        className: \"text-sm font-semibold\",\n                                        children: \"Button\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"text-xs rounded-full\",\n                                        onClick: ()=>setSelectedButtonIndex(null),\n                                        children: \"Revert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        size: \"sm\",\n                                        className: \"text-xs rounded-full bg-black text-white hover:bg-gray-800\",\n                                        onClick: handleSaveButtonEditor,\n                                        children: \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 642,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-[calc(90vh-120px)] overflow-auto mention-input-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_28__.ButtonEditor, {\n                            ref: buttonEditorRef,\n                            button: buttons[selectedButtonIndex],\n                            databaseId: props.databaseId,\n                            contextIsRecord: true,\n                            onSave: (updatedButton)=>{\n                                updateButton(selectedButtonIndex, updatedButton);\n                                setSelectedButtonIndex(null);\n                            },\n                            onCancel: ()=>setSelectedButtonIndex(null)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 672,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                lineNumber: 641,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n            lineNumber: 640,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.Dialog, {\n        open: true,\n        onOpenChange: ()=>{},\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogContent, {\n            className: \"max-w-2xl max-h-[90vh] !rounded-none p-0\",\n            hideCloseBtn: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogHeader, {\n                    className: \"p-4 border-b flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: props.close,\n                                    className: \"p-1 h-6 w-6 hover:bg-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_35__.Cross2Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 29\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_11__.DialogTitle, {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"Edit buttons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full\",\n                                    onClick: props.close,\n                                    children: \"Revert\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    onClick: saveChanges,\n                                    size: \"sm\",\n                                    className: \"text-xs rounded-full bg-black text-white hover:bg-gray-800\",\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 sm:p-4 max-h-[calc(90vh-120px)] overflow-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Buttons\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-md overflow-hidden\",\n                                    children: buttons.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-400 text-center py-4\",\n                                        children: \"No buttons configured\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 37\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_32__.DNDSortable, {\n                                        items: buttons.map((button)=>({\n                                                id: button.id,\n                                                data: button\n                                            })),\n                                        itemRenderer: (index, item, isDragging)=>{\n                                            const button = item.data;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"flex items-center justify-between p-3 hover:bg-gray-50 bg-white\", index !== buttons.length - 1 && \"border-b\", isDragging && \"shadow-lg\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            button.actions.length > 0 && (0,_components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_28__.getActionIcon)(button.actions[0].actionType),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: button.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 57\n                                                            }, void 0),\n                                                            button.actions.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"(\",\n                                                                    button.actions.length,\n                                                                    \" actions)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 61\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 745,\n                                                        columnNumber: 53\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"ghost\",\n                                                                className: \"p-1 h-6 w-6 text-black\",\n                                                                onClick: ()=>{\n                                                                    const buttonIndex = buttons.findIndex((b)=>b.id === button.id);\n                                                                    setSelectedButtonIndex(buttonIndex);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_19__.PencilIcon, {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 61\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 57\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"ghost\",\n                                                                className: \"p-1 h-6 w-6 text-black\",\n                                                                onClick: ()=>{\n                                                                    const buttonIndex = buttons.findIndex((b)=>b.id === button.id);\n                                                                    deleteButton(buttonIndex);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_19__.TrashIcon, {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 61\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 57\n                                                            }, void 0)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 53\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 49\n                                            }, void 0);\n                                        },\n                                        onChange: reorderButtons,\n                                        useDragHandle: true,\n                                        handlePosition: \"center\",\n                                        useDragOverlay: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    className: \"text-xs mt-2 w-full text-gray-600 hover:text-gray-800\",\n                                    onClick: addButton,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_19__.PlusIcon, {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Add Button\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                            lineNumber: 793,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                                    lineNumber: 786,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n                    lineNumber: 723,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n            lineNumber: 692,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\common\\\\header.tsx\",\n        lineNumber: 691,\n        columnNumber: 9\n    }, undefined);\n};\n_s3(ButtonGroupConfigEditor, \"UVbfdV/ERI4IWQEAB1paPrai33A=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_33__.useAlert\n    ];\n});\n_c4 = ButtonGroupConfigEditor;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeaderRenderer\");\n$RefreshReg$(_c1, \"AIPromptEditor\");\n$RefreshReg$(_c2, \"ScannableContentEditor\");\n$RefreshReg$(_c3, \"ButtonGroupHeaderDropDownMore\");\n$RefreshReg$(_c4, \"ButtonGroupConfigEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx":
/*!***********************************************************************************!*\
  !*** ./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx ***!
  \***********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionConfigEditor: function() { return /* reexport safe */ _components_workspace_main_views_table_renderer_fields_buttonGroup_Editor__WEBPACK_IMPORTED_MODULE_19__.ActionConfigEditor; },\n/* harmony export */   ButtonEditor: function() { return /* reexport safe */ _components_workspace_main_views_table_renderer_fields_buttonGroup_Editor__WEBPACK_IMPORTED_MODULE_19__.ButtonEditor; },\n/* harmony export */   ButtonGroupRenderer: function() { return /* binding */ ButtonGroupRenderer; },\n/* harmony export */   getActionIcon: function() { return /* binding */ getActionIcon; },\n/* harmony export */   getButtonIcon: function() { return /* binding */ getButtonIcon; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_gridRender__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/gridRender */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/gridRender.tsx\");\n/* harmony import */ var _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/utils/buttonActionHelpers */ \"(app-pages-browser)/./src/utils/buttonActionHelpers.ts\");\n/* harmony import */ var _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/buttonAction */ \"(app-pages-browser)/./src/utils/buttonAction.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup_Editor__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup/Editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ *,getActionIcon,getButtonIcon,ButtonGroupRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getActionIcon = (actionType)=>{\n    switch(actionType){\n        case \"sendEmail\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.EnvelopeIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 31,\n                columnNumber: 20\n            }, undefined);\n        case \"openUrl\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.LinkIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 33,\n                columnNumber: 20\n            }, undefined);\n        case \"updateRecord\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.PenToSquareIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 35,\n                columnNumber: 20\n            }, undefined);\n        case \"deleteRecord\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TrashIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 37,\n                columnNumber: 20\n            }, undefined);\n        case \"showConfirmation\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CircleInfoIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 39,\n                columnNumber: 20\n            }, undefined);\n        case \"showToast\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CircleCheckIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 41,\n                columnNumber: 20\n            }, undefined);\n        case \"sendNotification\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.BellIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 43,\n                columnNumber: 20\n            }, undefined);\n        case \"callWorkflow\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CodeMergeIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 45,\n                columnNumber: 20\n            }, undefined);\n        case \"expandRecord\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.ArrowUpRightAndArrowDownLeftFromCenterIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 47,\n                columnNumber: 20\n            }, undefined);\n        case \"peekRecord\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.EyeIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 49,\n                columnNumber: 20\n            }, undefined);\n        case \"executeIntegrationAction\":\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.BoltIcon, {\n                className: \"size-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 51,\n                columnNumber: 20\n            }, undefined);\n        default:\n            return null;\n    }\n};\n// Helper function to get the appropriate icon for a button based on its actions\nconst getButtonIcon = (button)=>{\n    var _button_actions, _button_actions1;\n    if (!button) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CircleExclamationIcon, {\n        className: \"size-3\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n        lineNumber: 59,\n        columnNumber: 25\n    }, undefined);\n    if (((_button_actions = button.actions) === null || _button_actions === void 0 ? void 0 : _button_actions.length) > 1) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.ListIcon, {\n            className: \"size-3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n            lineNumber: 62,\n            columnNumber: 16\n        }, undefined);\n    } else if (((_button_actions1 = button.actions) === null || _button_actions1 === void 0 ? void 0 : _button_actions1.length) === 1) {\n        return getActionIcon(button.actions[0].actionType);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.CircleExclamationIcon, {\n            className: \"size-3\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n            lineNumber: 66,\n            columnNumber: 16\n        }, undefined);\n    }\n};\nconst ButtonGroupRenderer = (props)=>{\n    _s();\n    const { updateRecordValues, deleteRecords, directDeleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    const { confirm, toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const { token, user } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { sendMessage } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord)();\n    const inputDialog = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__.useInputDialog)();\n    const [, setNotificationSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { column } = props;\n    const rowData = props.row;\n    const row = rowData.record;\n    // The __meta__ property is added by the table component at runtime\n    const meta = column[\"__meta__\"];\n    const dbColumn = meta.column;\n    const buttons = dbColumn.buttons || [];\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek)();\n    const database = databaseStore === null || databaseStore === void 0 ? void 0 : databaseStore[meta.databaseId];\n    const context = {\n        record: row,\n        database: database,\n        workspace: workspace,\n        token: token,\n        user: user,\n        meta: meta,\n        databaseId: meta.databaseId,\n        parentRecord: maybeRecord ? {\n            id: maybeRecord.recordInfo.record.id,\n            databaseId: maybeRecord.database.id\n        } : undefined\n    };\n    const handleButtonClick = async (button)=>{\n        var _button_actions;\n        if (!(button === null || button === void 0 ? void 0 : (_button_actions = button.actions) === null || _button_actions === void 0 ? void 0 : _button_actions.length)) {\n            toast.info(\"This button has no actions configured\");\n            return;\n        }\n        const services = (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__.createActionServices)({\n            updateRecordValues: updateRecordValues,\n            deleteRecords,\n            directDeleteRecords,\n            setPeekRecord: (recordId, databaseId)=>openRecord(recordId, databaseId),\n            confirm,\n            toast,\n            router,\n            forceRender,\n            sendMessage: (message)=>sendMessage(\"info\", \"action\", {\n                    message\n                })\n        }, inputDialog);\n        const urlsToOpen = [];\n        let actionSucceeded = true;\n        for (const action of button.actions){\n            const { success, result } = await (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__.executeDeclarativeAction)(action, context, services, databaseStore);\n            if (!success) {\n                actionSucceeded = false;\n                break;\n            }\n            if (action.actionType === \"openUrl\" && (result === null || result === void 0 ? void 0 : result.url)) {\n                urlsToOpen.push(result.url);\n            }\n        }\n        if (urlsToOpen.length > 0) {\n            services.toast.success(\"Opening \".concat(urlsToOpen.length, \" URL(s)...\"));\n            urlsToOpen.forEach((url)=>{\n                window.open(url, \"_blank\", \"noopener,noreferrer\");\n            });\n        }\n        if (actionSucceeded) {} else {}\n        forceRender();\n    };\n    const buttonStates = buttons.map((button)=>({\n            button,\n            state: (0,_utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__.evaluateButtonState)(button, row.recordValues || {})\n        }));\n    const visibleButtons = buttons.filter((button)=>{\n        var _buttonStates_find;\n        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n        return (buttonState === null || buttonState === void 0 ? void 0 : buttonState.visible) !== false;\n    });\n    if (!visibleButtons.length) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_common_gridRender__WEBPACK_IMPORTED_MODULE_12__.GridRender, {\n            rowId: rowData.id,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"r-text r-button-group text-xs h-full flex items-center group overflow-hidden button-group-container\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_table_renderer_common_gridRender__WEBPACK_IMPORTED_MODULE_12__.GridRender, {\n                rowId: rowData.id,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"r-text r-button-group text-xs h-full flex items-center group overflow-hidden button-group-container\",\n                    children: visibleButtons.length === 1 ? (()=>{\n                        var _buttonStates_find;\n                        const button = visibleButtons[0];\n                        const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                        const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.ERROR;\n                        const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.DISABLED;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                            onClick: ()=>!hasError && !isDisabled && handleButtonClick(button),\n                            disabled: hasError || isDisabled,\n                            variant: \"outline\",\n                            children: [\n                                hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                    className: \"size-3 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 21\n                                }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                    className: \"size-3 text-red-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 21\n                                }, undefined) : getButtonIcon(button),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"truncate\",\n                                    children: button.label || \"Action\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 17\n                        }, undefined);\n                    })() : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1 flex-wrap sm:flex-nowrap w-full\",\n                        children: [\n                            (()=>{\n                                var _buttonStates_find;\n                                const button = visibleButtons[0];\n                                const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                                const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.ERROR;\n                                const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.DISABLED;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    className: \"text-xs rounded-full p-1.5 h-auto font-semibold gap-1 flex items-center \".concat(hasError ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed\" : isDisabled ? \"bg-gray-50 hover:bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed\" : \"\"),\n                                    onClick: ()=>!hasError && !isDisabled && handleButtonClick(button),\n                                    disabled: hasError || isDisabled,\n                                    variant: \"outline\",\n                                    children: [\n                                        hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                            className: \"size-3 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 23\n                                        }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                            className: \"size-3 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 23\n                                        }, undefined) : getButtonIcon(button),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: button.label || \"Action\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 19\n                                }, undefined);\n                            })(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            className: \"text-xs rounded-full p-1.5 h-auto font-semibold flex items-center\",\n                                            variant: \"outline\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.ChevronDownIcon, {\n                                                className: \"size-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                        children: visibleButtons.slice(1).map((button, index)=>{\n                                            var _buttonStates_find;\n                                            const buttonState = (_buttonStates_find = buttonStates.find((bs)=>bs.button.id === button.id)) === null || _buttonStates_find === void 0 ? void 0 : _buttonStates_find.state;\n                                            const hasError = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.ERROR;\n                                            const isDisabled = (buttonState === null || buttonState === void 0 ? void 0 : buttonState.state) === _utils_buttonAction__WEBPACK_IMPORTED_MODULE_14__.ButtonState.DISABLED;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                                className: \"text-xs font-semibold gap-1 flex items-center \".concat(hasError ? \"text-gray-600 cursor-not-allowed\" : isDisabled ? \"text-gray-400 cursor-not-allowed\" : \"\"),\n                                                onClick: ()=>!hasError && !isDisabled && handleButtonClick(button),\n                                                disabled: hasError || isDisabled,\n                                                children: [\n                                                    hasError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                        className: \"size-3 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 27\n                                                    }, undefined) : isDisabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_4__.TriangleExclamationIcon, {\n                                                        className: \"size-3 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 27\n                                                    }, undefined) : getButtonIcon(button),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate\",\n                                                        children: button.label || \"Action\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.Dialog, {\n                open: inputDialog.inputDialogOpen,\n                onOpenChange: inputDialog.setInputDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogContent, {\n                    className: \"max-w-[95vw] sm:max-w-[600px] !rounded-none p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogTitle, {\n                                children: inputDialog.inputDialogTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 mb-4\",\n                                    children: inputDialog.inputDialogMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_16__.Input, {\n                                    value: inputDialog.inputValue,\n                                    onChange: (e)=>inputDialog.setInputValue(e.target.value),\n                                    placeholder: \"Enter your input here\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_15__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: ()=>inputDialog.setInputDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"text-xs\",\n                                    onClick: inputDialog.handleInputSubmit,\n                                    children: \"Submit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\renderer\\\\fields\\\\buttonGroup.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(ButtonGroupRenderer, \"+JbGtJZWb6h7cMvAObxyKI0Eik0=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_6__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _providers_user__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.useBroadcast,\n        _providers_record__WEBPACK_IMPORTED_MODULE_18__.useMaybeRecord,\n        _utils_buttonActionHelpers__WEBPACK_IMPORTED_MODULE_13__.useInputDialog,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek\n    ];\n});\n_c = ButtonGroupRenderer;\nvar _c;\n$RefreshReg$(_c, \"ButtonGroupRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\n"));

/***/ })

});