"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/views/layout",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Compute caret position relative to viewport.\n                let rect = currentRange.getBoundingClientRect();\n                // If the rectangle is all zeros, create a temporary marker to compute correct coordinates.\n                if (rect.width === 0 && rect.height === 0) {\n                    var _marker_parentNode;\n                    const marker = document.createElement(\"span\");\n                    marker.textContent = \"​\"; // zero width space\n                    currentRange.insertNode(marker);\n                    rect = marker.getBoundingClientRect();\n                    (_marker_parentNode = marker.parentNode) === null || _marker_parentNode === void 0 ? void 0 : _marker_parentNode.removeChild(marker);\n                    sel.removeAllRanges();\n                    sel.addRange(currentRange);\n                }\n            // Position will be calculated in useEffect to ensure proper positioning\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Calculate absolute position for portal-based dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                // Determine if we should show above or below\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                // Calculate absolute position for portal\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 580,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"absolute z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: showAbove ? {\n                    bottom: \"100%\",\n                    left: \"0\",\n                    marginBottom: \"2px\"\n                } : {\n                    top: \"100%\",\n                    left: \"0\",\n                    marginTop: \"2px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 596,\n                columnNumber: 17\n            }, this),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 645,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 579,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"C4RIgA4damfizFcXrLqcY529xaE=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});