"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/views/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/ViewsRootLayout.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/workspace/main/views/ViewsRootLayout.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ViewContext: function() { return /* binding */ ViewContext; },\n/* harmony export */   ViewsRootLayout: function() { return /* binding */ ViewsRootLayout; },\n/* harmony export */   useViewContext: function() { return /* binding */ useViewContext; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,MagnifyingGlassCircleIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassCircleIcon.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _typings_page__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/typings/page */ \"(app-pages-browser)/./src/typings/page.ts\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/view */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/view.js\");\n/* harmony import */ var _api_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/api/page */ \"(app-pages-browser)/./src/api/page.ts\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _utils_clipboard__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/clipboard */ \"(app-pages-browser)/./src/utils/clipboard.ts\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_columnsReorder__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/common/columnsReorder */ \"(app-pages-browser)/./src/components/workspace/main/views/common/columnsReorder.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewCreator */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewCreator.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSwitcher__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSwitcher */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSwitcher.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewFilter */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewFilter.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewSort */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewSort.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewMoreOptions */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewMoreOptions.tsx\");\n/* harmony import */ var _components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/summaryTable/renderers/header */ \"(app-pages-browser)/./src/components/workspace/main/views/summaryTable/renderers/header.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/common */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/common.js\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/workspace/main/emails/sendEmailWrapper */ \"(app-pages-browser)/./src/components/workspace/main/emails/sendEmailWrapper.tsx\");\n/* harmony import */ var _components_workspace_main_common_updateRecords__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/workspace/main/common/updateRecords */ \"(app-pages-browser)/./src/components/workspace/main/common/updateRecords.tsx\");\n/* harmony import */ var _api_account__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/api/account */ \"(app-pages-browser)/./src/api/account.ts\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_shareView__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/workspace/main/views/common/shareView */ \"(app-pages-browser)/./src/components/workspace/main/views/common/shareView.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _components_workspace_main_record_components_recordExtras__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @/components/workspace/main/record/components/recordExtras */ \"(app-pages-browser)/./src/components/workspace/main/record/components/recordExtras.tsx\");\n/* harmony import */ var _yudiel_react_qr_scanner__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @yudiel/react-qr-scanner */ \"(app-pages-browser)/./node_modules/@yudiel/react-qr-scanner/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _providers_recordTabViews__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! @/providers/recordTabViews */ \"(app-pages-browser)/./src/providers/recordTabViews.tsx\");\n/* harmony import */ var _components_workspace_main_views_addrecordmodal__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! @/components/workspace/main/views/addrecordmodal */ \"(app-pages-browser)/./src/components/workspace/main/views/addrecordmodal.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* __next_internal_client_entry_do_not_use__ ViewContext,useViewContext,ViewsRootLayout auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// import {ScrollArea} from \"@/components/ui/scroll-area\";\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ViewContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)({\n    context: \"page\"\n});\nconst useViewContext = ()=>{\n    _s();\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ViewContext);\n};\n_s(useViewContext, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nconst ViewsRootLayout = (props)=>{\n    var _maybeRecord_recordInfo_record, _maybeRecord_recordInfo, _maybeRecord_recordInfo_record1, _maybeRecord_recordInfo1;\n    _s1();\n    const { url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_30__.useAuth)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const documentId = searchParams.get(\"documentId\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const { viewsMap, accessLevel, page } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_6__.usePage)();\n    const [newView, setNewView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { createRecords, deleteRecords, smartUpdateViewDefinition, peekRecordId, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViews)();\n    const { filter, sorts, search, setFilter, setSorts, setSearch } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewFiltering)();\n    const { selectedIds, setSelectedIds } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewSelection)();\n    const { databaseStore, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_34__.useMaybeRecord)();\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__.useStackedPeek)();\n    // State for AddRecordModal\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // For record_tab context, get view from database metadata\n    let view = null;\n    if (props.context === \"record_tab\") {\n        var _databaseStore_props_parentId;\n        const database = (_databaseStore_props_parentId = databaseStore[props.parentId]) === null || _databaseStore_props_parentId === void 0 ? void 0 : _databaseStore_props_parentId.database;\n        if (database && database.meta && database.meta.recordViewsMap) {\n            view = database.meta.recordViewsMap[props.viewId] || null;\n        }\n    }\n    if (!view) {\n        view = viewsMap[props.viewId] || null;\n    }\n    let viewType = view === null || view === void 0 ? void 0 : view.type;\n    // Context-aware update function that automatically detects record tab vs regular views\n    const contextAwareUpdateViewDefinition = (update)=>{\n        const isRecordTab = props.context === \"record_tab\";\n        const databaseId = isRecordTab ? props.parentId : undefined;\n        return smartUpdateViewDefinition((view === null || view === void 0 ? void 0 : view.id) || \"\", (view === null || view === void 0 ? void 0 : view.pageId) || \"\", update, {\n            databaseId,\n            isRecordTab\n        });\n    };\n    const canContact = false;\n    const canEdit = accessLevel && [\n        _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Full,\n        _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Edit\n    ].includes(accessLevel);\n    const hasFullAccess = accessLevel && accessLevel === _typings_page__WEBPACK_IMPORTED_MODULE_7__.AccessLevel.Full;\n    let viewFilter = {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.Match.All\n    };\n    if (viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board || viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) {\n        const definition = view.definition;\n        viewFilter = definition.filter || viewFilter;\n    }\n    const handleAddRecord = ()=>{\n        if (!view) return;\n        if (view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board && view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) return;\n        // Different behavior based on context\n        if (props.context === \"record_tab\") {\n            // In record tabs: Use smart modal (especially for cross-database)\n            setShowAddModal(true);\n        } else {\n            // In main views: Direct record creation (original behavior)\n            handleDirectAddRecord();\n        }\n    };\n    const handleDirectAddRecord = async ()=>{\n        var _filter_conditions, _viewFilter_conditions;\n        if (!view) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        // Create record directly without modal (original behavior)\n        const rS = await createRecords(definition.databaseId, [\n            {}\n        ]);\n        // Only peek if there are filters/search (original logic)\n        const shouldPeek = search && search.trim() || (filter === null || filter === void 0 ? void 0 : (_filter_conditions = filter.conditions) === null || _filter_conditions === void 0 ? void 0 : _filter_conditions.length) > 0 || viewFilter && viewFilter.conditions && (viewFilter === null || viewFilter === void 0 ? void 0 : (_viewFilter_conditions = viewFilter.conditions) === null || _viewFilter_conditions === void 0 ? void 0 : _viewFilter_conditions.length) > 0;\n        if (rS && rS.records && rS.records.length > 0 && shouldPeek) {\n            setPeekRecordId(rS.records[0].id);\n        }\n    };\n    const handleRecordCreated = (recordId)=>{\n        if (!view) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        // In record tabs: Stack the newly created record\n        openRecord(recordId, definition.databaseId);\n        setShowAddModal(false);\n    };\n    const deleteSelected = async ()=>{\n        if (!view) return;\n        if (view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board && view.type !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table) return;\n        const definition = view.definition;\n        if (!definition.databaseId) return;\n        const { databaseId } = definition;\n        await deleteRecords(databaseId, selectedIds);\n    };\n    const copySharedUrl = ()=>{\n        if (!view) return;\n        const { id, name } = view;\n        const url = (0,_api_page__WEBPACK_IMPORTED_MODULE_12__.getViewPublicUrl)(id, name);\n        (0,_utils_clipboard__WEBPACK_IMPORTED_MODULE_14__.copyToClipboard)(url);\n        toast.success(\"Link copied to clipboard\");\n    };\n    let database = null;\n    let dbId = \"\";\n    if (view && [\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Form,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Calendar,\n        opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.ListView\n    ].includes(viewType)) {\n        dbId = view.definition.databaseId;\n        database = databaseStore[dbId] ? databaseStore[dbId].database : null;\n    }\n    const currentViewIdRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(props.viewId || \"\");\n    const viewId = props.viewId;\n    const pageId = (view === null || view === void 0 ? void 0 : view.pageId) || \"\";\n    const workspaceId = workspace.workspace.id;\n    const viewExists = !!view;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!viewExists) return;\n        const timeout = setTimeout(async ()=>{\n            const databaseId = dbId ? dbId : undefined;\n            await (0,_api_account__WEBPACK_IMPORTED_MODULE_29__.pushEvent)((token === null || token === void 0 ? void 0 : token.token) || \"\", {\n                workspaceId,\n                pageId,\n                event: _api_account__WEBPACK_IMPORTED_MODULE_29__.EventType.View,\n                databaseId,\n                viewId\n            });\n        }, 3000);\n        return ()=>{\n            if (timeout) clearTimeout(timeout);\n        };\n    }, [\n        dbId,\n        token === null || token === void 0 ? void 0 : token.token,\n        workspaceId,\n        pageId,\n        viewId,\n        viewExists\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (props.viewId === currentViewIdRef.current) return;\n        currentViewIdRef.current = props.viewId;\n        setSorts([]);\n        setFilter({\n            conditions: [],\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.Match.All\n        });\n        setSearch(\"\");\n        setSelectedIds([]);\n        setPeekRecordId(\"\");\n    }, [\n        props.viewId\n    ]);\n    // , [props.viewId, setFilter, setSorts, setSearch, setSelectedIds, setPeekRecordId])\n    // const [peekRecordId, setPeekRecordId] = useState(\"\")\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewContext.Provider, {\n        value: {\n            context: props.context\n        },\n        children: [\n            !view && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_8__.PageLoader, {\n                    size: \"full\",\n                    error: \"The requested content does not exists\",\n                    cta: {\n                        label: \"Go Home\",\n                        onClick: ()=>router.replace(url())\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false),\n            view && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full overflow-hidden flex flex-col\",\n                children: [\n                    viewType !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Calendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 h-12 flex items-center border-b border-neutral-300 gap-0.5\",\n                        children: [\n                            props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSwitcher__WEBPACK_IMPORTED_MODULE_18__.ViewSwitcher, {\n                                        context: props.context,\n                                        viewId: props.viewId,\n                                        creatable: canEdit,\n                                        editable: canEdit,\n                                        deletable: hasFullAccess,\n                                        cloneable: hasFullAccess,\n                                        requestNewView: ()=>setNewView(true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_17__.ViewCreator, {\n                                        context: props.context,\n                                        open: newView,\n                                        setOpen: setNewView\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    !maybeTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_shareView__WEBPACK_IMPORTED_MODULE_31__.ShareView, {\n                                        view: view,\n                                        page: page,\n                                        documentId: documentId || \"\",\n                                        domain: workspace.workspace.domain,\n                                        triggerAlign: \"start\",\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_24__.cn)(\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\", view.isPublished && \"text-blue-600 font-semibold\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpFromArcIcon, {\n                                                    className: \"size-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 33\n                                                }, void 0),\n                                                \" Share View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 38\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 44\n                                    }, undefined),\n                                    props.context === \"page\" && database && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageContextSourceDatabase, {\n                                            database: database\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            props.context === \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 56\n                            }, undefined),\n                            database && [\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board\n                            ].includes(viewType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: !view.definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: handleAddRecord,\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.CirclePlusIcon, {\n                                                        className: \"size-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 37\n                                                    }, undefined),\n                                                    \"Add\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false),\n                                        selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold text-xs text-blue-600 select-none\",\n                                                    children: [\n                                                        selectedIds.length,\n                                                        \" \\xa0selected\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_updateRecords__WEBPACK_IMPORTED_MODULE_28__.UpdateRecords, {\n                                                    trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        variant: \"ghost\",\n                                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.SquarePenIcon, {\n                                                                className: \"size-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 41\n                                                            }, void 0),\n                                                            \"Update\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 46\n                                                    }, void 0),\n                                                    database: database,\n                                                    ids: selectedIds,\n                                                    onUpdate: ()=>setSelectedIds([])\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    onClick: deleteSelected,\n                                                    className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.TrashListIcon, {\n                                                            className: \"size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 37\n                                                        }, undefined),\n                                                        \"Delete\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false),\n                            database && [\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Board,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.ListView,\n                                opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Calendar\n                            ].includes(viewType) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    canContact && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_47__[\"default\"], {\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    \"Enrich\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_48__[\"default\"], {\n                                                        className: \"size-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    \"Message\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    canEdit && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_27__.SendEmailWrapperForView, {\n                                        database: database,\n                                        view: view\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 71\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewFilter__WEBPACK_IMPORTED_MODULE_19__.ViewFilter, {\n                                        database: database,\n                                        trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.FilterListIcon, {\n                                                    className: \"size-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 37\n                                                }, void 0),\n                                                filter.conditions.length > 0 ? \"\".concat(filter.conditions.length, \" filters\") : \"Filter\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 33\n                                        }, void 0),\n                                        filter: filter,\n                                        onChange: setFilter,\n                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.id,\n                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : maybeRecord.recordInfo.record.databaseId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    (viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Table || viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewSort__WEBPACK_IMPORTED_MODULE_20__.ViewSort, {\n                                            database: database,\n                                            sorts: sorts,\n                                            onChange: setSorts,\n                                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpWideShortIcon, {\n                                                        className: \"size-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 41\n                                                    }, void 0),\n                                                    sorts.length > 0 ? \"\".concat(sorts.length, \" sorts\") : \"Sort\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start flex hover:bg-accent focus:bg-accent active:bg-accent items-center whitespace-nowrap font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                form: \"search-input\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_MagnifyingGlassCircleIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_49__[\"default\"], {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                placeholder: \"Search\",\n                                                value: search,\n                                                onChange: (e)=>setSearch(e.target.value),\n                                                className: \"text-xs transition-all outline-none h-auto !p-0 !ring-0 w-12 focus:w-48 !bg-transparent border-0 shadow-none drop-shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScannableCodeScanner, {\n                                        onRecordScan: setPeekRecordId,\n                                        database: database,\n                                        viewFilter: viewFilter,\n                                        filter: filter\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    canEdit && viewType !== opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_columnsReorder__WEBPACK_IMPORTED_MODULE_16__.ColumnsReorder, {\n                                            onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                            view: view\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false),\n                                    canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.SummaryTable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__.SummaryColumnGroupBy, {\n                                                onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                                view: view\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_summaryTable_renderers_header__WEBPACK_IMPORTED_MODULE_22__.SummaryColumnsReorder, {\n                                                onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                                view: view\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 29\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    canEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.ViewMoreOptions, {\n                                        // definition={view.definition as TableViewDefinition}\n                                        disabled: !canEdit,\n                                        view: view,\n                                        database: database,\n                                        selectedIds: selectedIds,\n                                        filter: filter,\n                                        sorts: sorts,\n                                        search: search,\n                                        onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u),\n                                        currentRecordId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo === void 0 ? void 0 : (_maybeRecord_recordInfo_record = _maybeRecord_recordInfo.record) === null || _maybeRecord_recordInfo_record === void 0 ? void 0 : _maybeRecord_recordInfo_record.id,\n                                        currentRecordDatabaseId: maybeRecord === null || maybeRecord === void 0 ? void 0 : (_maybeRecord_recordInfo1 = maybeRecord.recordInfo) === null || _maybeRecord_recordInfo1 === void 0 ? void 0 : (_maybeRecord_recordInfo_record1 = _maybeRecord_recordInfo1.record) === null || _maybeRecord_recordInfo_record1 === void 0 ? void 0 : _maybeRecord_recordInfo_record1.databaseId\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false),\n                            canEdit && database && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Form && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.FormViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    database: database,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false),\n                            canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Dashboard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.DashboardViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false),\n                            canEdit && viewType === opendb_app_db_utils_lib_typings_view__WEBPACK_IMPORTED_MODULE_11__.ViewType.Document && props.context !== \"record_tab\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewMoreOptions__WEBPACK_IMPORTED_MODULE_21__.DocViewMoreOptions, {\n                                    disabled: !canEdit,\n                                    view: view,\n                                    onDefinitionUpdate: (u)=>contextAwareUpdateViewDefinition(u)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 52\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden pr=1\",\n                        children: props.children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 233,\n                columnNumber: 22\n            }, undefined),\n            database && peekRecordId && !maybeRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PeekRecord, {\n                canEdit: canEdit,\n                onClose: ()=>setPeekRecordId(\"\"),\n                recordId: peekRecordId,\n                databaseId: database.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 475,\n                columnNumber: 58\n            }, undefined),\n            database && showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_addrecordmodal__WEBPACK_IMPORTED_MODULE_45__.AddRecordModal, {\n                open: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                databaseId: database.id,\n                viewFilter: viewFilter || undefined,\n                contextualFilter: filter,\n                onRecordCreated: handleRecordCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                lineNumber: 482,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n        lineNumber: 224,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(ViewsRootLayout, \"jhp8THB4JYOh4feSj9B/pDpog7g=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_user__WEBPACK_IMPORTED_MODULE_30__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _providers_page__WEBPACK_IMPORTED_MODULE_6__.usePage,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewFiltering,\n        _providers_views__WEBPACK_IMPORTED_MODULE_15__.useViewSelection,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert,\n        _providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate,\n        _providers_record__WEBPACK_IMPORTED_MODULE_34__.useMaybeRecord,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_46__.useStackedPeek\n    ];\n});\n_c = ViewsRootLayout;\nconst PageContextSourceDatabase = (param)=>{\n    let { database } = param;\n    _s2();\n    const { databasePageStore, databasePagesId } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const db = databasePageStore[database.id];\n    const databaseId = database.id;\n    const dbItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const items = [];\n        const databaseIds = [\n            ...databasePagesId\n        ];\n        if (!databaseIds.includes(databaseId)) databaseIds.push(databaseId);\n        for (const id of databaseIds){\n            const db = databasePageStore[id];\n            if (!db) continue;\n            const { page } = db;\n            const emoji = page.icon && page.icon.type === opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__.ObjectType.Emoji ? page.icon.emoji : \"\\uD83D\\uDCD5\";\n            const item = {\n                color: undefined,\n                data: undefined,\n                id,\n                title: \"\".concat(emoji, \" \").concat(db.page.name),\n                value: id\n            };\n            items.push(item);\n        }\n        return items;\n    }, [\n        databasePageStore,\n        databasePagesId,\n        databaseId\n    ]);\n    if (!db) return null;\n    const { page } = db;\n    const emoji = page.icon && page.icon.type === opendb_app_db_utils_lib_typings_common__WEBPACK_IMPORTED_MODULE_25__.ObjectType.Emoji ? page.icon.emoji : \"\\uD83D\\uDCD5\";\n    const title = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            emoji,\n            \" \\xa0 \",\n            db.page.name\n        ]\n    }, void 0, true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenu, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenuTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"outline\",\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_24__.cn)(\"text-xs rounded-full p-1.5 !px-3 h-auto gap-2 justify-start max-w-48 items-center\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.DatabaseIcon, {\n                                className: \"size-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"truncate text-black font-medium\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 530,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_23__.DropdownMenuContent, {\n                    className: \"p-0 rounded-none min-w-80\",\n                    align: \"start\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"db-select\",\n                                className: \"text-xs text-neutral-500\",\n                                children: \"Source database\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_26__.CustomSelect, {\n                                onChange: (v)=>{},\n                                disabled: true,\n                                selectedIds: [\n                                    databaseId\n                                ],\n                                placeholder: \"Choose a database\",\n                                options: dbItems\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 529,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s2(PageContextSourceDatabase, \"HF+HEFapgkk21Bf612NPlXB3zzU=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace\n    ];\n});\n_c1 = PageContextSourceDatabase;\nconst PeekRecord = (props)=>{\n    _s3();\n    const { databaseStore, members, url } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const template = (0,_providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate)();\n    const shared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_39__.useMaybeShared)();\n    const canExpand = !shared && !template;\n    const database = databaseStore[props.databaseId];\n    const record = databaseStore[props.databaseId].recordsIdMap[props.recordId];\n    const onOpenChange = (o)=>{\n        if (!o) {\n            var _props_onClose;\n            (_props_onClose = props.onClose) === null || _props_onClose === void 0 ? void 0 : _props_onClose.call(props);\n        }\n    };\n    if (!database || !record) return null;\n    const recordInfo = record;\n    let processedRecord = null;\n    const persons = (0,_components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_35__.membersToPersons)(members);\n    const linkedDatabaseId = Object.values(database.database.definition.columnsMap).filter((c)=>c.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.DatabaseFieldDataType.Linked && c.databaseId).map((c)=>c.databaseId);\n    const linkedDatabases = {};\n    for (const id of linkedDatabaseId){\n        const db = databaseStore[id];\n        if (db) {\n            linkedDatabases[id] = {\n                id,\n                definition: db.database.definition,\n                recordsMap: {},\n                srcPackageName: db.database.srcPackageName\n            };\n            for (let r of Object.values(db.recordsIdMap)){\n                linkedDatabases[id].recordsMap[r.record.id] = r.record;\n            }\n        }\n    }\n    const records = [\n        recordInfo.record\n    ];\n    const processedRecords = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_36__.transformRawRecords)(database.database.definition, records, persons, linkedDatabases);\n    processedRecord = processedRecords[0];\n    let href = url(\"/databases/\".concat(record.record.databaseId, \"/records/\").concat(record.record.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.Sheet, {\n            defaultOpen: true,\n            onOpenChange: onOpenChange,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetTrigger, {\n                    asChild: true,\n                    children: props.trigger\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetContent, {\n                    className: \"!w-[50vw] !min-w-[400px] !max-w-full bg-white p-0 pt-8\",\n                    children: [\n                        canExpand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            asChild: true,\n                            className: \"absolute right-12 top-2.5 !size-6 !p-1.5 rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                href: href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.ArrowUpRightAndArrowDownLeftFromCenterIcon, {\n                                    className: \"size-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"size-full flex flex-col overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetHeader, {\n                                    className: \"hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetTitle, {\n                                            className: \"font-bold text-base\",\n                                            children: \"Peek Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 633,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetDescription, {\n                                            className: \"hidden\",\n                                            children: \"Make changes to your record here. Click save when you're done.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 634,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 632,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_recordTabViews__WEBPACK_IMPORTED_MODULE_44__.RecordTabViewsProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_record__WEBPACK_IMPORTED_MODULE_34__.RecordProvider, {\n                                            recordInfo: {\n                                                ...record,\n                                                processedRecord\n                                            },\n                                            database: database.database,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_recordExtras__WEBPACK_IMPORTED_MODULE_40__.RecordExtras, {\n                                                showOverview: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 25\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 21\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetFooter, {\n                                    className: \"hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_32__.SheetClose, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            type: \"submit\",\n                                            className: \"rounded-full\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 25\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 621,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s3(PeekRecord, \"AsUdGlLRyHFbDg8CUaGjnjZ3LoM=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace,\n        _providers_template__WEBPACK_IMPORTED_MODULE_37__.useMaybeTemplate,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_39__.useMaybeShared\n    ];\n});\n_c2 = PeekRecord;\nconst ScannableCodeScanner = (param)=>{\n    let { database, filter, viewFilter, onRecordScan } = param;\n    _s4();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert)();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace)();\n    const databaseStoreItem = databaseStore[database.id];\n    let hasScannableField = false;\n    for (let value of Object.values(database.definition.columnsMap)){\n        if (value.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_33__.DatabaseFieldDataType.ScannableCode) {\n            hasScannableField = true;\n            break;\n        }\n    }\n    if (!hasScannableField) return null;\n    const onScan = (results)=>{\n        const detected = results[0];\n        if (!detected) {\n            toast.error(\"Nothing to search\");\n            return;\n        }\n        const rawValue = detected.rawValue;\n        if (!rawValue) {\n            toast.error(\"Nothing to search\");\n            return;\n        }\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__.filterAndSortRecords)(databaseStoreItem, members, databaseStore, viewFilter, filter, [], workspace.workspaceMember.userId);\n        console.log(\"Scanned value: \", {\n            rawValue,\n            rows\n        });\n        const searched = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_43__.searchFilteredRecords)(rawValue, rows);\n        if (!searched || searched.length === 0) {\n            toast.error(\"No records found\");\n            return;\n        }\n        onRecordScan(searched[0].record.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.Popover, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.PopoverTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"ghost\",\n                        className: \"text-xs rounded-full p-1 size-7 gap-2 justify-center items-center font-medium\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_5__.BarcodeReadIcon, {\n                            className: \"size-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 733,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_42__.PopoverContent, {\n                    className: \"w-80 p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"mb-4 text-xs font-medium\",\n                                children: \"Find record by scanning barcode/QR code\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 740,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_yudiel_react_qr_scanner__WEBPACK_IMPORTED_MODULE_41__.Scanner, {\n                                allowMultiple: true,\n                                onScan: onScan\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                            lineNumber: 743,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n                    lineNumber: 739,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\ViewsRootLayout.tsx\",\n            lineNumber: 732,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s4(ScannableCodeScanner, \"ovSne5WK1YXbIK6ADZ04IdwnxrQ=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_13__.useAlert,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_9__.useWorkspace\n    ];\n});\n_c3 = ScannableCodeScanner;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ViewsRootLayout\");\n$RefreshReg$(_c1, \"PageContextSourceDatabase\");\n$RefreshReg$(_c2, \"PeekRecord\");\n$RefreshReg$(_c3, \"ScannableCodeScanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL1ZpZXdzUm9vdExheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEM7QUFDa0U7QUFDZDtBQUN0RDtBQUNBO0FBQzRMO0FBQy9MO0FBQ1E7QUFDUTtBQUNOO0FBQ1E7QUFDd0I7QUFDdkM7QUFDRDtBQUNPO0FBQzhCO0FBR087QUFDTjtBQUNFO0FBQ0o7QUFFSjtBQUNpRjtBQUNoQztBQUN2QjtBQUN0RTtBQUNtQztBQUVEO0FBQzJCO0FBQ2I7QUFDNUI7QUFDVjtBQUNvQztBQUMrRDtBQUNYO0FBQ2pJLDBEQUEwRDtBQUNSO0FBQzhDO0FBQ1Y7QUFDaEM7QUFDekI7QUFDcUI7QUFDc0M7QUFDckI7QUFDYTtBQUNvQjtBQUNsRDtBQUNnQjtBQUNjO0FBQ3pCO0FBU2hELE1BQU1xRiw0QkFBY25GLG9EQUFhQSxDQUFtRDtJQUFDb0YsU0FBUztBQUFNLEdBQUc7QUFDdkcsTUFBTUMsaUJBQWlCOztJQUFNcEYsT0FBQUEsaURBQVVBLENBQUNrRjtBQUFXLEVBQUU7R0FBL0NFO0FBRU4sTUFBTUMsa0JBQWtCLENBQUNDO1FBdVdhQyxnQ0FBQUEseUJBQ1FBLGlDQUFBQTs7SUF2V2pELE1BQU0sRUFBQ0MsR0FBRyxFQUFDLEdBQUdsRSxrRUFBWUE7SUFDMUIsTUFBTSxFQUFDbUUsS0FBSyxFQUFDLEdBQUdwQyx5REFBT0E7SUFFdkIsTUFBTXFDLGVBQWVsRSxpRUFBZUE7SUFDcEMsTUFBTW1FLGFBQWFELGFBQWFFLEdBQUcsQ0FBQztJQUVwQyxNQUFNQyxTQUFTdEUsMkRBQVNBO0lBQ3hCLE1BQU0sRUFBQ3VFLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxJQUFJLEVBQUMsR0FBRzdFLHdEQUFPQTtJQUM3QyxNQUFNLENBQUM4RSxTQUFTQyxXQUFXLEdBQUc5RiwrQ0FBUUEsQ0FBQztJQUV2QyxNQUFNLEVBQUMrRixhQUFhLEVBQUVDLGFBQWEsRUFBRUMseUJBQXlCLEVBQUVDLFlBQVksRUFBRUMsZUFBZSxFQUFDLEdBQUcxRSwyREFBUUE7SUFDekcsTUFBTSxFQUFDMkUsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBQyxHQUFHL0UsbUVBQWdCQTtJQUNoRixNQUFNLEVBQUNnRixXQUFXLEVBQUVDLGNBQWMsRUFBQyxHQUFHaEYsbUVBQWdCQTtJQUN0RCxNQUFNLEVBQUNpRixhQUFhLEVBQUVDLFNBQVMsRUFBQyxHQUFHM0Ysa0VBQVlBO0lBQy9DLE1BQU0sRUFBQzRGLEtBQUssRUFBQyxHQUFHdkYsMkRBQVFBO0lBQ3hCLE1BQU13RixnQkFBZ0IvQyxzRUFBZ0JBO0lBQ3RDLE1BQU1tQixjQUFjVCxrRUFBY0E7SUFDbEMsTUFBTSxFQUFDc0MsVUFBVSxFQUFDLEdBQUduQyx1RUFBY0E7SUFFbkMsMkJBQTJCO0lBQzNCLE1BQU0sQ0FBQ29DLGNBQWNDLGdCQUFnQixHQUFHbEgsK0NBQVFBLENBQUM7SUFFakQsMERBQTBEO0lBQzFELElBQUltSCxPQUFvQjtJQUN4QixJQUFJakMsTUFBTUgsT0FBTyxLQUFLLGNBQWM7WUFDZjZCO1FBQWpCLE1BQU1RLFlBQVdSLGdDQUFBQSxhQUFhLENBQUMxQixNQUFNbUMsUUFBUSxDQUFDLGNBQTdCVCxvREFBQUEsOEJBQStCUSxRQUFRO1FBQ3hELElBQUlBLFlBQVlBLFNBQVNFLElBQUksSUFBSUYsU0FBU0UsSUFBSSxDQUFDQyxjQUFjLEVBQUU7WUFDM0RKLE9BQU9DLFNBQVNFLElBQUksQ0FBQ0MsY0FBYyxDQUFDckMsTUFBTXNDLE1BQU0sQ0FBQyxJQUFJO1FBQ3pEO0lBQ0o7SUFFQSxJQUFJLENBQUNMLE1BQU07UUFDUEEsT0FBT3pCLFFBQVEsQ0FBQ1IsTUFBTXNDLE1BQU0sQ0FBQyxJQUFJO0lBQ3JDO0lBRUEsSUFBSUMsV0FBaUNOLGlCQUFBQSwyQkFBQUEsS0FBTU8sSUFBSTtJQUUvQyx1RkFBdUY7SUFDdkYsTUFBTUMsbUNBQW1DLENBQUNDO1FBQ3RDLE1BQU1DLGNBQWMzQyxNQUFNSCxPQUFPLEtBQUs7UUFDdEMsTUFBTStDLGFBQWFELGNBQWMzQyxNQUFNbUMsUUFBUSxHQUFHVTtRQUVsRCxPQUFPOUIsMEJBQ0hrQixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1hLEVBQUUsS0FBSSxJQUNaYixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1jLE1BQU0sS0FBSSxJQUNoQkwsUUFDQTtZQUFFRTtZQUFZRDtRQUFZO0lBRWxDO0lBRUEsTUFBTUssYUFBYTtJQUVuQixNQUFNQyxVQUFVeEMsZUFBZTtRQUFDM0Usc0RBQVdBLENBQUNvSCxJQUFJO1FBQUVwSCxzREFBV0EsQ0FBQ3FILElBQUk7S0FBQyxDQUFDQyxRQUFRLENBQUMzQztJQUM3RSxNQUFNNEMsZ0JBQWdCNUMsZUFBZUEsZ0JBQWdCM0Usc0RBQVdBLENBQUNvSCxJQUFJO0lBRXJFLElBQUlJLGFBQW9DO1FBQUNDLFlBQVksRUFBRTtRQUFFQyxPQUFPOUUsc0VBQUtBLENBQUMrRSxHQUFHO0lBQUE7SUFDekUsSUFBSWxCLGFBQWFwRywyRUFBUUEsQ0FBQ3VILEtBQUssSUFBSW5CLGFBQWFwRywyRUFBUUEsQ0FBQ3dILEtBQUssRUFBRTtRQUM1RCxNQUFNQyxhQUFhM0IsS0FBSzJCLFVBQVU7UUFDbENOLGFBQWFNLFdBQVcxQyxNQUFNLElBQUlvQztJQUN0QztJQUVBLE1BQU1PLGtCQUFrQjtRQUNwQixJQUFJLENBQUM1QixNQUFNO1FBQ1gsSUFBSUEsS0FBS08sSUFBSSxLQUFLckcsMkVBQVFBLENBQUN1SCxLQUFLLElBQUl6QixLQUFLTyxJQUFJLEtBQUtyRywyRUFBUUEsQ0FBQ3dILEtBQUssRUFBRTtRQUVsRSxzQ0FBc0M7UUFDdEMsSUFBSTNELE1BQU1ILE9BQU8sS0FBSyxjQUFjO1lBQ2hDLGtFQUFrRTtZQUNsRW1DLGdCQUFnQjtRQUNwQixPQUFPO1lBQ0gsNERBQTREO1lBQzVEOEI7UUFDSjtJQUNKO0lBRUEsTUFBTUEsd0JBQXdCO1lBU3NCNUMsb0JBQTBFb0M7UUFSMUgsSUFBSSxDQUFDckIsTUFBTTtRQUNYLE1BQU0yQixhQUFhM0IsS0FBSzJCLFVBQVU7UUFDbEMsSUFBSSxDQUFDQSxXQUFXaEIsVUFBVSxFQUFFO1FBRTVCLDJEQUEyRDtRQUMzRCxNQUFNbUIsS0FBSyxNQUFNbEQsY0FBYytDLFdBQVdoQixVQUFVLEVBQUU7WUFBQyxDQUFDO1NBQUU7UUFFMUQseURBQXlEO1FBQ3pELE1BQU1vQixhQUFhLFVBQVc1QyxPQUFPNkMsSUFBSSxNQUFPL0MsQ0FBQUEsbUJBQUFBLDhCQUFBQSxxQkFBQUEsT0FBUXFDLFVBQVUsY0FBbEJyQyx5Q0FBQUEsbUJBQW9CZ0QsTUFBTSxJQUFHLEtBQU1aLGNBQWNBLFdBQVdDLFVBQVUsSUFBSUQsQ0FBQUEsdUJBQUFBLGtDQUFBQSx5QkFBQUEsV0FBWUMsVUFBVSxjQUF0QkQsNkNBQUFBLHVCQUF3QlksTUFBTSxJQUFHO1FBQzNKLElBQUlILE1BQU1BLEdBQUdJLE9BQU8sSUFBSUosR0FBR0ksT0FBTyxDQUFDRCxNQUFNLEdBQUcsS0FBS0YsWUFBWTtZQUN6RC9DLGdCQUFnQjhDLEdBQUdJLE9BQU8sQ0FBQyxFQUFFLENBQUNyQixFQUFFO1FBQ3BDO0lBQ0o7SUFFQSxNQUFNc0Isc0JBQXNCLENBQUNDO1FBQ3pCLElBQUksQ0FBQ3BDLE1BQU07UUFDWCxNQUFNMkIsYUFBYTNCLEtBQUsyQixVQUFVO1FBQ2xDLElBQUksQ0FBQ0EsV0FBV2hCLFVBQVUsRUFBRTtRQUU1QixpREFBaUQ7UUFDakRkLFdBQVd1QyxVQUFVVCxXQUFXaEIsVUFBVTtRQUMxQ1osZ0JBQWdCO0lBQ3BCO0lBRUEsTUFBTXNDLGlCQUFpQjtRQUNuQixJQUFJLENBQUNyQyxNQUFNO1FBQ1gsSUFBSUEsS0FBS08sSUFBSSxLQUFLckcsMkVBQVFBLENBQUN1SCxLQUFLLElBQUl6QixLQUFLTyxJQUFJLEtBQUtyRywyRUFBUUEsQ0FBQ3dILEtBQUssRUFBRTtRQUNsRSxNQUFNQyxhQUFhM0IsS0FBSzJCLFVBQVU7UUFFbEMsSUFBSSxDQUFDQSxXQUFXaEIsVUFBVSxFQUFFO1FBQzVCLE1BQU0sRUFBQ0EsVUFBVSxFQUFDLEdBQUdnQjtRQUVyQixNQUFNOUMsY0FBYzhCLFlBQVlwQjtJQUNwQztJQUNBLE1BQU0rQyxnQkFBZ0I7UUFDbEIsSUFBSSxDQUFDdEMsTUFBTTtRQUNYLE1BQU0sRUFBQ2EsRUFBRSxFQUFFMEIsSUFBSSxFQUFDLEdBQUd2QztRQUNuQixNQUFNL0IsTUFBTTlELDREQUFnQkEsQ0FBQzBHLElBQUkwQjtRQUNqQ2xJLGtFQUFlQSxDQUFDNEQ7UUFDaEIwQixNQUFNNkMsT0FBTyxDQUFDO0lBQ2xCO0lBRUEsSUFBSXZDLFdBQTRCO0lBQ2hDLElBQUl3QyxPQUFPO0lBQ1gsSUFBSXpDLFFBQVE7UUFBQzlGLDJFQUFRQSxDQUFDd0gsS0FBSztRQUFFeEgsMkVBQVFBLENBQUN1SCxLQUFLO1FBQUV2SCwyRUFBUUEsQ0FBQ3dJLFlBQVk7UUFBRXhJLDJFQUFRQSxDQUFDeUksSUFBSTtRQUFFekksMkVBQVFBLENBQUMwSSxRQUFRO1FBQUUxSSwyRUFBUUEsQ0FBQzJJLFFBQVE7S0FBQyxDQUFDMUIsUUFBUSxDQUFDYixXQUFXO1FBQ3pJbUMsT0FBTyxLQUFNZCxVQUFVLENBQXlCaEIsVUFBVTtRQUMxRFYsV0FBV1IsYUFBYSxDQUFDZ0QsS0FBSyxHQUFHaEQsYUFBYSxDQUFDZ0QsS0FBSyxDQUFDeEMsUUFBUSxHQUFHO0lBQ3BFO0lBRUEsTUFBTTZDLG1CQUFtQmxLLDZDQUFNQSxDQUFDbUYsTUFBTXNDLE1BQU0sSUFBSTtJQUVoRCxNQUFNQSxTQUFTdEMsTUFBTXNDLE1BQU07SUFDM0IsTUFBTVMsU0FBU2QsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNYyxNQUFNLEtBQUk7SUFDL0IsTUFBTWlDLGNBQWNyRCxVQUFVQSxTQUFTLENBQUNtQixFQUFFO0lBQzFDLE1BQU1tQyxhQUFhLENBQUMsQ0FBQ2hEO0lBRXJCdEgsZ0RBQVNBLENBQUM7UUFDTixJQUFJLENBQUNzSyxZQUFZO1FBQ2pCLE1BQU1DLFVBQVVDLFdBQVc7WUFDdkIsTUFBTXZDLGFBQWE4QixPQUFPQSxPQUFPN0I7WUFDakMsTUFBTS9FLHdEQUFTQSxDQUFDcUMsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPQSxLQUFLLEtBQUksSUFBSTtnQkFBQzZFO2dCQUFhakM7Z0JBQVFxQyxPQUFPdkgsb0RBQVNBLENBQUN3SCxJQUFJO2dCQUFFekM7Z0JBQVlOO1lBQU07UUFDdkcsR0FBRztRQUNILE9BQU87WUFDSCxJQUFJNEMsU0FBU0ksYUFBYUo7UUFDOUI7SUFDSixHQUFHO1FBQUNSO1FBQU12RSxrQkFBQUEsNEJBQUFBLE1BQU9BLEtBQUs7UUFBRTZFO1FBQWFqQztRQUFRVDtRQUFRMkM7S0FBVztJQUVoRXRLLGdEQUFTQSxDQUFDO1FBQ04sSUFBSXFGLE1BQU1zQyxNQUFNLEtBQUt5QyxpQkFBaUJRLE9BQU8sRUFBRTtRQUMvQ1IsaUJBQWlCUSxPQUFPLEdBQUd2RixNQUFNc0MsTUFBTTtRQUN2Q2hCLFNBQVMsRUFBRTtRQUNYRCxVQUFVO1lBQUNrQyxZQUFZLEVBQUU7WUFBRUMsT0FBTzlFLHNFQUFLQSxDQUFDK0UsR0FBRztRQUFBO1FBQzNDbEMsVUFBVTtRQUNWRSxlQUFlLEVBQUU7UUFDakJSLGdCQUFnQjtJQUNwQixHQUFHO1FBQUNqQixNQUFNc0MsTUFBTTtLQUFDO0lBQ2pCLHFGQUFxRjtJQUNyRix1REFBdUQ7SUFFdkQscUJBQ0ksOERBQUMxQyxZQUFZNEYsUUFBUTtRQUFDQyxPQUFPO1lBQUM1RixTQUFTRyxNQUFNSCxPQUFPO1FBQUE7O1lBQy9DLENBQUNvQyxzQkFBUTswQkFDTiw0RUFBQ2xHLG9FQUFVQTtvQkFBQzJKLE1BQUs7b0JBQU9DLE9BQU07b0JBQXdDQyxLQUFLO3dCQUN2RUMsT0FBTzt3QkFDUEMsU0FBUyxJQUFNdkYsT0FBT3dGLE9BQU8sQ0FBQzdGO29CQUNsQzs7Ozs7OztZQUlIK0Isc0JBQVEsOERBQUMrRDtnQkFBSUMsV0FBVTs7b0JBSW5CMUQsYUFBYXBHLDJFQUFRQSxDQUFDMEksUUFBUSxrQkFBSSw4REFBQ21CO3dCQUFJQyxXQUFVOzs0QkFDN0NqRyxNQUFNSCxPQUFPLEtBQUssOEJBQWdCOztrREFDL0IsOERBQUNqRCwrRkFBWUE7d0NBQUNpRCxTQUFTRyxNQUFNSCxPQUFPO3dDQUFFeUMsUUFBUXRDLE1BQU1zQyxNQUFNO3dDQUM1QzRELFdBQVdqRDt3Q0FDWGtELFVBQVVsRDt3Q0FDVm1ELFdBQVcvQzt3Q0FDWGdELFdBQVdoRDt3Q0FDWGlELGdCQUFnQixJQUFNMUYsV0FBVzs7Ozs7O29DQUM5Q3FDLHlCQUFXLDhEQUFDdEcsNkZBQVdBO3dDQUFDa0QsU0FBU0csTUFBTUgsT0FBTzt3Q0FBRTBHLE1BQU01Rjt3Q0FBUzZGLFNBQVM1Rjs7Ozs7O29DQUN4RSxDQUFDaUIsK0JBQWlCLDhEQUFDN0QseUZBQVNBO3dDQUN6QmlFLE1BQU1BO3dDQUNOdkIsTUFBTUE7d0NBQ05MLFlBQVlBLGNBQWM7d0NBQzFCb0csUUFBUTlFLFVBQVVBLFNBQVMsQ0FBQzhFLE1BQU07d0NBQ2xDQyxjQUFjO3dDQUNkQyx1QkFBUyw4REFBQ3BNLHlEQUFNQTs0Q0FBQ3FNLFNBQVE7NENBQ1JYLFdBQVd6SSwrQ0FBRUEsQ0FBQywyRUFBMkV5RSxLQUFLNEUsV0FBVyxJQUFJOzs4REFDMUgsOERBQUN6TCxvRkFBa0JBO29EQUFDNkssV0FBVTs7Ozs7O2dEQUFVOzs7Ozs7Ozs7Ozs7b0NBRy9DakcsTUFBTUgsT0FBTyxLQUFLLFVBQVVxQywwQkFBWTtrREFDckMsNEVBQUM0RTs0Q0FBMEI1RSxVQUFVQTs7Ozs7OztrREFHekMsOERBQUM4RDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7NEJBR2xCakcsTUFBTUgsT0FBTyxLQUFLLDhCQUFnQiw4REFBQ21HO2dDQUFJQyxXQUFVOzs7Ozs7NEJBRWpEL0QsWUFBWTtnQ0FBQy9GLDJFQUFRQSxDQUFDd0gsS0FBSztnQ0FBRXhILDJFQUFRQSxDQUFDdUgsS0FBSzs2QkFBQyxDQUFDTixRQUFRLENBQUNiLDJCQUF5QjswQ0FDM0UsQ0FBQyxLQUFNcUIsVUFBVSxDQUF5Qm1ELFdBQVcsa0JBQUk7O3dDQW1DckQ5RCxXQUFXVixhQUFhcEcsMkVBQVFBLENBQUN3SCxLQUFLLGtCQUFJO3NEQUN2Qyw0RUFBQ3BKLHlEQUFNQTtnREFBQ3FNLFNBQVE7Z0RBQ1JkLFNBQVNqQztnREFDVG9DLFdBQVU7O2tFQUNkLDhEQUFDekssZ0ZBQWNBO3dEQUFDeUssV0FBVTs7Ozs7O29EQUFVOzs7Ozs7Ozt3Q0FJM0N6RSxZQUFZMEMsTUFBTSxHQUFHLG1CQUFLOzs4REFDdkIsOERBQUM4QztvREFBS2YsV0FBVTs7d0RBQW1EekUsWUFBWTBDLE1BQU07d0RBQUM7Ozs7Ozs7OERBQ3RGLDhEQUFDdEcsMkZBQWFBO29EQUNWK0ksdUJBQVMsOERBQUNwTSx5REFBTUE7d0RBQUNxTSxTQUFRO3dEQUNSWCxXQUFVOzswRUFDdkIsOERBQUN0SywrRUFBYUE7Z0VBQUNzSyxXQUFVOzs7Ozs7NERBQVU7Ozs7Ozs7b0RBR3ZDL0QsVUFBVUE7b0RBQ1YrRSxLQUFLekY7b0RBQ0wwRixVQUFVLElBQU16RixlQUFlLEVBQUU7Ozs7Ozs4REFFckMsOERBQUNsSCx5REFBTUE7b0RBQUNxTSxTQUFRO29EQUNSZCxTQUFTeEI7b0RBQ1QyQixXQUFVOztzRUFDZCw4REFBQ3JLLCtFQUFhQTs0REFBQ3FLLFdBQVU7Ozs7Ozt3REFBVTs7Ozs7Ozs7Ozs7OzRCQVNsRC9ELFlBQVk7Z0NBQUMvRiwyRUFBUUEsQ0FBQ3dILEtBQUs7Z0NBQUV4SCwyRUFBUUEsQ0FBQ3VILEtBQUs7Z0NBQUV2SCwyRUFBUUEsQ0FBQ3dJLFlBQVk7Z0NBQUV4SSwyRUFBUUEsQ0FBQzJJLFFBQVE7Z0NBQUUzSSwyRUFBUUEsQ0FBQzBJLFFBQVE7NkJBQUMsQ0FBQ3pCLFFBQVEsQ0FBQ2IsMEJBQXdCOztvQ0FDdklTLGNBQWNoRCxNQUFNSCxPQUFPLEtBQUssOEJBQWdCOzswREFDN0MsOERBQUN0Rix5REFBTUE7Z0RBQUNxTSxTQUFRO2dEQUNSWCxXQUFVOztrRUFDZCw4REFBQ2hMLDhJQUFZQTt3REFBQ2dMLFdBQVU7Ozs7OztvREFBVTs7Ozs7OzswREFHdEMsOERBQUMxTCx5REFBTUE7Z0RBQUNxTSxTQUFRO2dEQUNSWCxXQUFVOztrRUFDZCw4REFBQ2xMLDhJQUFZQTt3REFBQ2tMLFdBQVU7Ozs7OztvREFBVTs7Ozs7Ozs7O29DQUl6Q2hELFdBQVdqRCxNQUFNSCxPQUFPLEtBQUssOEJBQWdCLDhEQUFDbEMsd0dBQXVCQTt3Q0FDbEV1RSxVQUFVQTt3Q0FDVkQsTUFBTUE7Ozs7OztrREFFViw4REFBQ3BGLDJGQUFVQTt3Q0FDUHFGLFVBQVVBO3dDQUNWeUUsdUJBQ0ksOERBQUNwTSx5REFBTUE7NENBQUNxTSxTQUFROzRDQUNSWCxXQUFVOzs4REFDZCw4REFBQ3ZLLGdGQUFjQTtvREFBQ3VLLFdBQVU7Ozs7OztnREFDekIvRSxPQUFPcUMsVUFBVSxDQUFDVyxNQUFNLEdBQUcsSUFDM0IsR0FBNEIsT0FBekJoRCxPQUFPcUMsVUFBVSxDQUFDVyxNQUFNLEVBQUMsY0FDNUI7Ozs7Ozs7d0NBR1RoRCxRQUFRQTt3Q0FDUmlHLFVBQVU5Rjt3Q0FDVitGLGVBQWUsRUFBRW5ILHdCQUFBQSxrQ0FBQUEsWUFBYW9ILFVBQVUsQ0FBQ0MsTUFBTSxDQUFDeEUsRUFBRTt3Q0FDbER5RSx1QkFBdUIsRUFBRXRILHdCQUFBQSxrQ0FBQUEsWUFBYW9ILFVBQVUsQ0FBQ0MsTUFBTSxDQUFDMUUsVUFBVTs7Ozs7O29DQUdwRUwsQ0FBQUEsYUFBYXBHLDJFQUFRQSxDQUFDd0gsS0FBSyxJQUFJcEIsYUFBYXBHLDJFQUFRQSxDQUFDd0ksWUFBWSxtQkFBSztrREFDcEUsNEVBQUM3SCx1RkFBUUE7NENBQ0xvRixVQUFVQTs0Q0FDVmYsT0FBT0E7NENBQ1BnRyxVQUFVN0Y7NENBQ1ZxRix1QkFDSSw4REFBQ3BNLHlEQUFNQTtnREFBQ3FNLFNBQVE7Z0RBQ1JYLFdBQVU7O2tFQUNkLDhEQUFDM0ssc0ZBQW9CQTt3REFBQzJLLFdBQVU7Ozs7OztvREFDL0I5RSxNQUFNK0MsTUFBTSxHQUFHLElBQ2YsR0FBZ0IsT0FBYi9DLE1BQU0rQyxNQUFNLEVBQUMsWUFDaEI7Ozs7Ozs7Ozs7Ozs7a0RBTWpCLDhEQUFDOEI7d0NBQUlDLFdBQVU7OzBEQUNYLDhEQUFDOUssdURBQUtBO2dEQUFDcU0sTUFBSzswREFFUiw0RUFBQ3hNLDhJQUF5QkE7b0RBQUNpTCxXQUFVOzs7Ozs7Ozs7OzswREFFekMsOERBQUMvSyx1REFBS0E7Z0RBQUN1TSxhQUFZO2dEQUNaaEMsT0FBT3JFO2dEQUNQK0YsVUFBVU8sQ0FBQUEsSUFBS25HLFVBQVVtRyxFQUFFQyxNQUFNLENBQUNsQyxLQUFLO2dEQUN2Q1EsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdyQiw4REFBQzJCO3dDQUNHQyxjQUFjNUc7d0NBQ2RpQixVQUFVQTt3Q0FDVm9CLFlBQVlBO3dDQUNacEMsUUFBUUE7Ozs7OztvQ0FFWCtCLFdBQVdWLGFBQWFwRywyRUFBUUEsQ0FBQ3dJLFlBQVksa0JBQUk7a0RBQzlDLDRFQUFDakksbUdBQWNBOzRDQUNYb0wsb0JBQW9CQyxDQUFBQSxJQUFLdEYsaUNBQWlDc0Y7NENBQzFEOUYsTUFBTUE7Ozs7Ozs7b0NBRWJnQixXQUFXVixhQUFhcEcsMkVBQVFBLENBQUN3SSxZQUFZLGtCQUFJOzswREFDOUMsOERBQUN4SCxpSEFBb0JBO2dEQUNqQjJLLG9CQUFvQkMsQ0FBQUEsSUFBS3RGLGlDQUFpQ3NGO2dEQUMxRDlGLE1BQU1BOzs7Ozs7MERBRVYsOERBQUM3RSxrSEFBcUJBO2dEQUNsQjBLLG9CQUFvQkMsQ0FBQUEsSUFBS3RGLGlDQUFpQ3NGO2dEQUMxRDlGLE1BQU1BOzs7Ozs7OztvQ0FFYmdCLHlCQUFXLDhEQUFDL0YscUdBQWVBO3dDQUN4QixzREFBc0Q7d0NBQ3REOEssVUFBVSxDQUFDL0U7d0NBQ1hoQixNQUFNQTt3Q0FDTkMsVUFBVUE7d0NBQ1ZWLGFBQWFBO3dDQUNiTixRQUFRQTt3Q0FDUkMsT0FBT0E7d0NBQ1BDLFFBQVFBO3dDQUNSMEcsb0JBQW9CQyxDQUFBQSxJQUFLdEYsaUNBQWlDc0Y7d0NBQzFEWCxlQUFlLEVBQUVuSCx3QkFBQUEsbUNBQUFBLDBCQUFBQSxZQUFhb0gsVUFBVSxjQUF2QnBILCtDQUFBQSxpQ0FBQUEsd0JBQXlCcUgsTUFBTSxjQUEvQnJILHFEQUFBQSwrQkFBaUM2QyxFQUFFO3dDQUNwRHlFLHVCQUF1QixFQUFFdEgsd0JBQUFBLG1DQUFBQSwyQkFBQUEsWUFBYW9ILFVBQVUsY0FBdkJwSCxnREFBQUEsa0NBQUFBLHlCQUF5QnFILE1BQU0sY0FBL0JySCxzREFBQUEsZ0NBQWlDMkMsVUFBVTs7Ozs7Ozs2REFXdEU7NEJBSUxLLFdBQVdmLFlBQVlLLGFBQWFwRywyRUFBUUEsQ0FBQ3lJLElBQUksSUFBSTVFLE1BQU1ILE9BQU8sS0FBSyw4QkFBZ0I7MENBQ3BGLDRFQUFDNUMseUdBQW1CQTtvQ0FDaEIrSyxVQUFVLENBQUMvRTtvQ0FDWGhCLE1BQU1BO29DQUNOQyxVQUFVQTtvQ0FDVjRGLG9CQUFvQkMsQ0FBQUEsSUFBS3RGLGlDQUFpQ3NGOzs7Ozs7OzRCQUlqRTlFLFdBQVdWLGFBQWFwRywyRUFBUUEsQ0FBQzhMLFNBQVMsa0JBQUk7MENBRTNDLDRFQUFDbEwsOEdBQXdCQTtvQ0FDckJpTCxVQUFVLENBQUMvRTtvQ0FDWGhCLE1BQU1BO29DQUNONkYsb0JBQW9CQyxDQUFBQSxJQUFLdEYsaUNBQWlDc0Y7Ozs7Ozs7NEJBR2pFOUUsV0FBV1YsYUFBYXBHLDJFQUFRQSxDQUFDK0wsUUFBUSxJQUFJbEksTUFBTUgsT0FBTyxLQUFLLDhCQUFnQjswQ0FDNUUsNEVBQUM3Qyx3R0FBa0JBO29DQUNmZ0wsVUFBVSxDQUFDL0U7b0NBQ1hoQixNQUFNQTtvQ0FDTjZGLG9CQUFvQkMsQ0FBQUEsSUFBS3RGLGlDQUFpQ3NGOzs7Ozs7Ozs7Ozs7O2tDQU90RSw4REFBQy9CO3dCQUFJQyxXQUFVO2tDQUNWakcsTUFBTW1JLFFBQVE7Ozs7Ozs7Ozs7OztZQUl0QmpHLFlBQVlsQixnQkFBZ0IsQ0FBQ2YsNkJBQWUsOERBQUNtSTtnQkFDMUNuRixTQUFTQTtnQkFDVG9GLFNBQVMsSUFBTXBILGdCQUFnQjtnQkFDL0JvRCxVQUFVckQ7Z0JBQWM0QixZQUFZVixTQUFTWSxFQUFFOzs7Ozs7WUFHbERaLFlBQVlILDhCQUNULDhEQUFDckMsNEZBQWNBO2dCQUNYNkcsTUFBTXhFO2dCQUNOc0csU0FBUyxJQUFNckcsZ0JBQWdCO2dCQUMvQlksWUFBWVYsU0FBU1ksRUFBRTtnQkFDdkJRLFlBQVlBLGNBQWNUO2dCQUMxQnlGLGtCQUFrQnBIO2dCQUNsQnFILGlCQUFpQm5FOzs7Ozs7Ozs7Ozs7QUFNckMsRUFBQztJQTNhWXJFOztRQUNLL0QsOERBQVlBO1FBQ1YrQixxREFBT0E7UUFFRjdCLDZEQUFlQTtRQUdyQkQsdURBQVNBO1FBQ2NKLG9EQUFPQTtRQUdvRFUsdURBQVFBO1FBQ3pDQywrREFBZ0JBO1FBQzFDQywrREFBZ0JBO1FBQ25CVCw4REFBWUE7UUFDL0JLLHVEQUFRQTtRQUNGeUMsa0VBQWdCQTtRQUNsQlUsOERBQWNBO1FBQ2JHLG1FQUFjQTs7O0tBbEIxQkk7QUE2YWIsTUFBTStHLDRCQUE0QjtRQUFDLEVBQUM1RSxRQUFRLEVBQXlCOztJQUVqRSxNQUFNLEVBQUNzRyxpQkFBaUIsRUFBRUMsZUFBZSxFQUFDLEdBQUd6TSxrRUFBWUE7SUFDekQsTUFBTTBNLEtBQUtGLGlCQUFpQixDQUFDdEcsU0FBU1ksRUFBRSxDQUFDO0lBRXpDLE1BQU1GLGFBQWFWLFNBQVNZLEVBQUU7SUFFOUIsTUFBTTZGLFVBQVUvTiw4Q0FBT0EsQ0FBQztRQUNwQixNQUFNZ08sUUFBOEIsRUFBRTtRQUV0QyxNQUFNQyxjQUFjO2VBQUlKO1NBQWdCO1FBQ3hDLElBQUksQ0FBQ0ksWUFBWXpGLFFBQVEsQ0FBQ1IsYUFBYWlHLFlBQVlDLElBQUksQ0FBQ2xHO1FBRXhELEtBQUssTUFBTUUsTUFBTStGLFlBQWE7WUFDMUIsTUFBTUgsS0FBS0YsaUJBQWlCLENBQUMxRixHQUFHO1lBQ2hDLElBQUksQ0FBQzRGLElBQUk7WUFDVCxNQUFNLEVBQUNoSSxJQUFJLEVBQUMsR0FBR2dJO1lBQ2YsTUFBTUssUUFBUXJJLEtBQUtzSSxJQUFJLElBQUl0SSxLQUFLc0ksSUFBSSxDQUFDeEcsSUFBSSxLQUFLL0UsK0VBQVVBLENBQUN3TCxLQUFLLEdBQUd2SSxLQUFLc0ksSUFBSSxDQUFDRCxLQUFLLEdBQUc7WUFFbkYsTUFBTUcsT0FBMkI7Z0JBQzdCQyxPQUFPdEc7Z0JBQVd1RyxNQUFNdkc7Z0JBQVdDO2dCQUFJdUcsT0FBTyxHQUFZWCxPQUFUSyxPQUFNLEtBQWdCLE9BQWJMLEdBQUdoSSxJQUFJLENBQUM4RCxJQUFJO2dCQUFJaUIsT0FBTzNDO1lBQ3JGO1lBQ0E4RixNQUFNRSxJQUFJLENBQUNJO1FBQ2Y7UUFDQSxPQUFPTjtJQUNYLEdBQUc7UUFBQ0o7UUFBbUJDO1FBQWlCN0Y7S0FBVztJQUVuRCxJQUFJLENBQUM4RixJQUFJLE9BQU87SUFFaEIsTUFBTSxFQUFDaEksSUFBSSxFQUFDLEdBQUdnSTtJQUNmLE1BQU1LLFFBQVFySSxLQUFLc0ksSUFBSSxJQUFJdEksS0FBS3NJLElBQUksQ0FBQ3hHLElBQUksS0FBSy9FLCtFQUFVQSxDQUFDd0wsS0FBSyxHQUFHdkksS0FBS3NJLElBQUksQ0FBQ0QsS0FBSyxHQUFHO0lBQ25GLE1BQU1NLHNCQUFROztZQUFHTjtZQUFNO1lBQVNMLEdBQUdoSSxJQUFJLENBQUM4RCxJQUFJOzs7SUFDNUMscUJBQU87a0JBQ0gsNEVBQUNuSCx1RUFBWUE7OzhCQUNULDhEQUFDRSw4RUFBbUJBO29CQUFDK0wsT0FBTzs4QkFDeEIsNEVBQUMvTyx5REFBTUE7d0JBQUNxTSxTQUFRO3dCQUFVWCxXQUFXekksK0NBQUVBLENBQUM7OzBDQUNwQyw4REFBQy9CLDhFQUFZQTtnQ0FBQ3dLLFdBQVU7Ozs7OzswQ0FFeEIsOERBQUNlO2dDQUFLZixXQUFVOzBDQUFtQ29EOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFHM0QsOERBQUMvTCw4RUFBbUJBO29CQUFDMkksV0FBVTtvQkFBNEJzRCxPQUFNOzhCQUM3RCw0RUFBQ3ZEO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQzlLLHVEQUFLQTtnQ0FBQ3FPLFNBQVE7Z0NBQVl2RCxXQUFVOzBDQUEyQjs7Ozs7OzBDQUNoRSw4REFBQ3ZJLDZFQUFZQTtnQ0FDVHlKLFVBQVVzQyxDQUFBQSxLQUVWO2dDQUNBekIsUUFBUTtnQ0FDUnhHLGFBQWE7b0NBQUNvQjtpQ0FBVztnQ0FDekI2RSxhQUFZO2dDQUNaaUMsU0FBU2Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9qQztJQTFETTdCOztRQUUyQzlLLDhEQUFZQTs7O01BRnZEOEs7QUFxRU4sTUFBTXNCLGFBQWEsQ0FBQ3BJOztJQUNoQixNQUFNLEVBQUMwQixhQUFhLEVBQUVpSSxPQUFPLEVBQUV6SixHQUFHLEVBQUMsR0FBR2xFLGtFQUFZQTtJQUNsRCxNQUFNNE4sV0FBVzlLLHNFQUFnQkE7SUFDakMsTUFBTStLLFNBQVM3SyxrRUFBY0E7SUFFN0IsTUFBTThLLFlBQVksQ0FBQ0QsVUFBVSxDQUFDRDtJQUU5QixNQUFNMUgsV0FBV1IsYUFBYSxDQUFDMUIsTUFBTTRDLFVBQVUsQ0FBQztJQUNoRCxNQUFNMEUsU0FBUzVGLGFBQWEsQ0FBQzFCLE1BQU00QyxVQUFVLENBQUMsQ0FBQ21ILFlBQVksQ0FBQy9KLE1BQU1xRSxRQUFRLENBQUM7SUFDM0UsTUFBTTJGLGVBQWUsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDQSxHQUFHO2dCQUNKaks7YUFBQUEsaUJBQUFBLE1BQU1xSSxPQUFPLGNBQWJySSxxQ0FBQUEsb0JBQUFBO1FBQ0o7SUFDSjtJQUVBLElBQUksQ0FBQ2tDLFlBQVksQ0FBQ29GLFFBQVEsT0FBTztJQUVqQyxNQUFNRCxhQUFhQztJQUVuQixJQUFJNEMsa0JBQTRDO0lBRWhELE1BQU1DLFVBQVV2TCxnSEFBZ0JBLENBQUMrSztJQUVqQyxNQUFNUyxtQkFBbUJDLE9BQU9DLE1BQU0sQ0FBQ3BJLFNBQVNBLFFBQVEsQ0FBQzBCLFVBQVUsQ0FBQzJHLFVBQVUsRUFDekVySixNQUFNLENBQUNzSixDQUFBQSxJQUFLQSxFQUFFaEksSUFBSSxLQUFLL0Qsc0ZBQXFCQSxDQUFDZ00sTUFBTSxJQUFJRCxFQUFFNUgsVUFBVSxFQUNuRThILEdBQUcsQ0FBQ0YsQ0FBQUEsSUFBSyxFQUFvQjVILFVBQVU7SUFFNUMsTUFBTStILGtCQUFtQyxDQUFDO0lBRTFDLEtBQUssTUFBTTdILE1BQU1zSCxpQkFBa0I7UUFDL0IsTUFBTTFCLEtBQUtoSCxhQUFhLENBQUNvQixHQUFHO1FBQzVCLElBQUk0RixJQUFJO1lBQ0ppQyxlQUFlLENBQUM3SCxHQUFHLEdBQUc7Z0JBQ2xCQTtnQkFDQWMsWUFBWThFLEdBQUd4RyxRQUFRLENBQUMwQixVQUFVO2dCQUNsQ2dILFlBQVksQ0FBQztnQkFDYkMsZ0JBQWdCbkMsR0FBR3hHLFFBQVEsQ0FBQzJJLGNBQWM7WUFDOUM7WUFDQSxLQUFLLElBQUlDLEtBQUtULE9BQU9DLE1BQU0sQ0FBQzVCLEdBQUdxQixZQUFZLEVBQUc7Z0JBQzFDWSxlQUFlLENBQUM3SCxHQUFHLENBQUM4SCxVQUFVLENBQUNFLEVBQUV4RCxNQUFNLENBQUN4RSxFQUFFLENBQUMsR0FBR2dJLEVBQUV4RCxNQUFNO1lBQzFEO1FBQ0o7SUFDSjtJQUNBLE1BQU1uRCxVQUFVO1FBQUNrRCxXQUFXQyxNQUFNO0tBQUM7SUFDbkMsTUFBTXlELG1CQUFtQmxNLHNGQUFtQkEsQ0FDeENxRCxTQUFTQSxRQUFRLENBQUMwQixVQUFVLEVBQzVCTyxTQUNBZ0csU0FDQVE7SUFFSlQsa0JBQWtCYSxnQkFBZ0IsQ0FBQyxFQUFFO0lBRXJDLElBQUlDLE9BQU85SyxJQUFJLGNBQWtEb0gsT0FBcENBLE9BQU9BLE1BQU0sQ0FBQzFFLFVBQVUsRUFBQyxhQUE0QixPQUFqQjBFLE9BQU9BLE1BQU0sQ0FBQ3hFLEVBQUU7SUFHakYscUJBQU87a0JBQ0gsNEVBQUM3RSx3REFBS0E7WUFBQ2dOLGFBQWE7WUFBTWpCLGNBQWNBOzs4QkFDcEMsOERBQUN4TCwrREFBWUE7b0JBQUM4SyxPQUFPOzhCQUNoQnRKLE1BQU0yRyxPQUFPOzs7Ozs7OEJBRWxCLDhEQUFDeEksK0RBQVlBO29CQUFDOEgsV0FBVTs7d0JBQ25CNkQsMkJBQWEsOERBQUN2UCx5REFBTUE7NEJBQUNxTSxTQUFROzRCQUFRMEMsT0FBTzs0QkFBQ3JELFdBQVU7c0NBQ3BELDRFQUFDbEgsa0RBQUlBO2dDQUFDaU0sTUFBTUE7MENBQ1IsNEVBQUMzUCw0R0FBMENBO29DQUFDNEssV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHOUQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQzNILDhEQUFXQTtvQ0FBQzJILFdBQVU7O3NEQUNuQiw4REFBQzFILDZEQUFVQTs0Q0FBQzBILFdBQVU7c0RBQXNCOzs7Ozs7c0RBQzVDLDhEQUFDN0gsbUVBQWdCQTs0Q0FBQzZILFdBQVU7c0RBQVM7Ozs7Ozs7Ozs7Ozs4Q0FJekMsOERBQUNEO29DQUFJQyxXQUFVOzhDQWtCWCw0RUFBQ3hHLDhFQUFzQkE7a0RBQ25CLDRFQUFDZCw4REFBY0E7NENBQ1gwSSxZQUFZO2dEQUFDLEdBQUdDLE1BQU07Z0RBQUU0Qzs0Q0FBZTs0Q0FDdkNoSSxVQUFVQSxTQUFTQSxRQUFRO3NEQUUzQiw0RUFBQ2pELG9HQUFZQTtnREFBQ2lNLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLdEMsOERBQUM3TSw4REFBV0E7b0NBQUM0SCxXQUFVOzhDQUNuQiw0RUFBQy9ILDZEQUFVQTt3Q0FBQ29MLE9BQU87a0RBQ2YsNEVBQUMvTyx5REFBTUE7NENBQUNpSSxNQUFLOzRDQUFTeUQsV0FBVTtzREFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0U7SUEvR01tQzs7UUFDb0NwTSw4REFBWUE7UUFDakM4QyxrRUFBZ0JBO1FBQ2xCRSw4REFBY0E7OztNQUgzQm9KO0FBd0hOLE1BQU1SLHVCQUF1QjtRQUFDLEVBQUMxRixRQUFRLEVBQUVoQixNQUFNLEVBQUVvQyxVQUFVLEVBQUV1RSxZQUFZLEVBQTRCOztJQUNqRyxNQUFNLEVBQUNqRyxLQUFLLEVBQUMsR0FBR3ZGLDJEQUFRQTtJQUN4QixNQUFNLEVBQUNxRixhQUFhLEVBQUVpSSxPQUFPLEVBQUVoSSxTQUFTLEVBQUMsR0FBRzNGLGtFQUFZQTtJQUN4RCxNQUFNbVAsb0JBQW9CekosYUFBYSxDQUFDUSxTQUFTWSxFQUFFLENBQUM7SUFHcEQsSUFBSXNJLG9CQUFvQjtJQUN4QixLQUFLLElBQUkzRixTQUFTNEUsT0FBT0MsTUFBTSxDQUFDcEksU0FBUzBCLFVBQVUsQ0FBQzJHLFVBQVUsRUFBRztRQUM3RCxJQUFJOUUsTUFBTWpELElBQUksS0FBSy9ELHNGQUFxQkEsQ0FBQzRNLGFBQWEsRUFBRTtZQUNwREQsb0JBQW9CO1lBQ3BCO1FBQ0o7SUFDSjtJQUVBLElBQUksQ0FBQ0EsbUJBQW1CLE9BQU87SUFDL0IsTUFBTUUsU0FBUyxDQUFDQztRQUNaLE1BQU1DLFdBQVdELE9BQU8sQ0FBQyxFQUFFO1FBQzNCLElBQUksQ0FBQ0MsVUFBVTtZQUNYNUosTUFBTStELEtBQUssQ0FBQztZQUNaO1FBQ0o7UUFFQSxNQUFNOEYsV0FBV0QsU0FBU0MsUUFBUTtRQUNsQyxJQUFJLENBQUNBLFVBQVU7WUFDWDdKLE1BQU0rRCxLQUFLLENBQUM7WUFDWjtRQUNKO1FBRUEsTUFBTSxFQUFDK0YsSUFBSSxFQUFDLEdBQUdwTSw2RkFBb0JBLENBQy9CNkwsbUJBQ0F4QixTQUNBakksZUFDQTRCLFlBQ0FwQyxRQUNBLEVBQUUsRUFDRlMsVUFBVWdLLGVBQWUsQ0FBQ0MsTUFBTTtRQUVwQ0MsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQjtZQUFDTDtZQUFVQztRQUFJO1FBQzlDLE1BQU1LLFdBQVd4TSw4RkFBcUJBLENBQUNrTSxVQUFVQztRQUNqRCxJQUFJLENBQUNLLFlBQVlBLFNBQVM3SCxNQUFNLEtBQUssR0FBRztZQUNwQ3RDLE1BQU0rRCxLQUFLLENBQUM7WUFDWjtRQUNKO1FBQ0FrQyxhQUFha0UsUUFBUSxDQUFDLEVBQUUsQ0FBQ3pFLE1BQU0sQ0FBQ3hFLEVBQUU7SUFDdEM7SUFFQSxxQkFBTztrQkFDSCw0RUFBQzNELDREQUFPQTs7OEJBQ0osOERBQUNFLG1FQUFjQTtvQkFBQ2lLLE9BQU87OEJBQ25CLDRFQUFDL08seURBQU1BO3dCQUFDcU0sU0FBUTt3QkFDUlgsV0FBVTtrQ0FDZCw0RUFBQzFLLGlGQUFlQTs0QkFBQzBLLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR25DLDhEQUFDN0csbUVBQWNBO29CQUFDNkcsV0FBVTs7c0NBQ3RCLDhEQUFDRDtzQ0FDRyw0RUFBQ2dHO2dDQUFHL0YsV0FBVTswQ0FBMkI7Ozs7Ozs7Ozs7O3NDQUU3Qyw4REFBQ0Q7c0NBQ0csNEVBQUM5Ryw4REFBT0E7Z0NBQUMrTSxhQUFhO2dDQUFDWCxRQUFRQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS25EO0lBaEVNMUQ7O1FBQ2N2TCx1REFBUUE7UUFDb0JMLDhEQUFZQTs7O01BRnRENEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvVmlld3NSb290TGF5b3V0LnRzeD9hNjZlIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQge0J1dHRvbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCBSZWFjdCwge2NyZWF0ZUNvbnRleHQsIFByb3BzV2l0aENoaWxkcmVuLCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZVJlZiwgdXNlU3RhdGV9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHtFbnZlbG9wZUljb24sIE1hZ25pZnlpbmdHbGFzc0NpcmNsZUljb24sIFNwYXJrbGVzSWNvbn0gZnJvbSBcIkBoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZVwiO1xuaW1wb3J0IHtJbnB1dH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xuaW1wb3J0IHtMYWJlbH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9sYWJlbFwiO1xuaW1wb3J0IHtBcnJvd1VwRnJvbUFyY0ljb24sIEFycm93VXBSaWdodEFuZEFycm93RG93bkxlZnRGcm9tQ2VudGVySWNvbiwgQXJyb3dVcFdpZGVTaG9ydEljb24sIEJhcmNvZGVSZWFkSWNvbiwgQ2lyY2xlUGx1c0ljb24sIERhdGFiYXNlSWNvbiwgRmlsdGVyTGlzdEljb24sIFNxdWFyZVBlbkljb24sIFRyYXNoTGlzdEljb259IGZyb20gXCJAL2NvbXBvbmVudHMvaWNvbnMvRm9udEF3ZXNvbWVSZWd1bGFyXCI7XG5pbXBvcnQge3VzZVBhZ2V9IGZyb20gXCJAL3Byb3ZpZGVycy9wYWdlXCI7XG5pbXBvcnQge0FjY2Vzc0xldmVsLCBWaWV3fSBmcm9tIFwiQC90eXBpbmdzL3BhZ2VcIjtcbmltcG9ydCB7UGFnZUxvYWRlcn0gZnJvbSBcIkAvY29tcG9uZW50cy9jdXN0b20tdWkvbG9hZGVyXCI7XG5pbXBvcnQge3VzZVdvcmtzcGFjZX0gZnJvbSBcIkAvcHJvdmlkZXJzL3dvcmtzcGFjZVwiO1xuaW1wb3J0IHt1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtc30gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHtUYWJsZVZpZXdEZWZpbml0aW9uLCBWaWV3VHlwZX0gZnJvbSBcIm9wZW5kYi1hcHAtZGItdXRpbHMvbGliL3R5cGluZ3Mvdmlld1wiO1xuaW1wb3J0IHtnZXRWaWV3UHVibGljVXJsfSBmcm9tIFwiQC9hcGkvcGFnZVwiO1xuaW1wb3J0IHt1c2VBbGVydH0gZnJvbSBcIkAvcHJvdmlkZXJzL2FsZXJ0XCI7XG5pbXBvcnQge2NvcHlUb0NsaXBib2FyZH0gZnJvbSBcIkAvdXRpbHMvY2xpcGJvYXJkXCI7XG5pbXBvcnQge3VzZVZpZXdzLCB1c2VWaWV3RmlsdGVyaW5nLCB1c2VWaWV3U2VsZWN0aW9uIH0gZnJvbSBcIkAvcHJvdmlkZXJzL3ZpZXdzXCI7XG5cbmltcG9ydCB7Vmlld0RlZmluaXRpb259IGZyb20gXCJvcGVuZGItYXBwLWRiLXV0aWxzL2xpYi90eXBpbmdzL3ZpZXdcIjtcbmltcG9ydCB7Q29sdW1uc1Jlb3JkZXJ9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY29tbW9uL2NvbHVtbnNSZW9yZGVyXCI7XG5pbXBvcnQge1ZpZXdDcmVhdG9yfSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NvbW1vbi92aWV3Q3JlYXRvclwiO1xuaW1wb3J0IHtWaWV3U3dpdGNoZXJ9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvY29tbW9uL3ZpZXdTd2l0Y2hlclwiO1xuaW1wb3J0IHtWaWV3RmlsdGVyfSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NvbW1vbi92aWV3RmlsdGVyXCI7XG5pbXBvcnQge0RhdGFiYXNlfSBmcm9tIFwiQC90eXBpbmdzL2RhdGFiYXNlXCI7XG5pbXBvcnQge1ZpZXdTb3J0fSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NvbW1vbi92aWV3U29ydFwiO1xuaW1wb3J0IHtEYXNoYm9hcmRWaWV3TW9yZU9wdGlvbnMsIERvY1ZpZXdNb3JlT3B0aW9ucywgRm9ybVZpZXdNb3JlT3B0aW9ucywgVmlld01vcmVPcHRpb25zfSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NvbW1vbi92aWV3TW9yZU9wdGlvbnNcIjtcbmltcG9ydCB7U3VtbWFyeUNvbHVtbkdyb3VwQnksIFN1bW1hcnlDb2x1bW5zUmVvcmRlcn0gZnJvbSBcIkAvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy9zdW1tYXJ5VGFibGUvcmVuZGVyZXJzL2hlYWRlclwiO1xuaW1wb3J0IHtEcm9wZG93bk1lbnUsIERyb3Bkb3duTWVudUNvbnRlbnQsIERyb3Bkb3duTWVudVRyaWdnZXJ9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudVwiO1xuaW1wb3J0IHtjbn0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQge09iamVjdFR5cGV9IGZyb20gXCJvcGVuZGItYXBwLWRiLXV0aWxzL2xpYi90eXBpbmdzL2NvbW1vblwiO1xuaW1wb3J0IHtUYWdJdGVtfSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL3RhYmxlL3JlbmRlcmVyL2NvbW1vbi90YWdcIjtcbmltcG9ydCB7Q3VzdG9tU2VsZWN0fSBmcm9tIFwiQC9jb21wb25lbnRzL2N1c3RvbS11aS9jdXN0b21TZWxlY3RcIjtcbmltcG9ydCB7U2VuZEVtYWlsV3JhcHBlckZvclZpZXd9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vZW1haWxzL3NlbmRFbWFpbFdyYXBwZXJcIjtcbmltcG9ydCB7VXBkYXRlUmVjb3Jkc30gZnJvbSBcIkAvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi9jb21tb24vdXBkYXRlUmVjb3Jkc1wiO1xuaW1wb3J0IHtFdmVudFR5cGUsIHB1c2hFdmVudH0gZnJvbSBcIkAvYXBpL2FjY291bnRcIjtcbmltcG9ydCB7dXNlQXV0aH0gZnJvbSBcIkAvcHJvdmlkZXJzL3VzZXJcIjtcbmltcG9ydCB7U2hhcmVWaWV3fSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NvbW1vbi9zaGFyZVZpZXdcIjtcbmltcG9ydCB7U2hlZXQsIFNoZWV0Q2xvc2UsIFNoZWV0Q29udGVudCwgU2hlZXREZXNjcmlwdGlvbiwgU2hlZXRGb290ZXIsIFNoZWV0SGVhZGVyLCBTaGVldFRpdGxlLCBTaGVldFRyaWdnZXIsfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NoZWV0XCJcbmltcG9ydCB7RGF0YWJhc2VGaWVsZERhdGFUeXBlLCBEYlJlY29yZEZpbHRlciwgTGlua2VkQ29sdW1uLCBNYXRjaCwgUHJvY2Vzc2VkRGJSZWNvcmR9IGZyb20gXCJvcGVuZGItYXBwLWRiLXV0aWxzL2xpYi90eXBpbmdzL2RiXCI7XG4vLyBpbXBvcnQge1Njcm9sbEFyZWF9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2Nyb2xsLWFyZWFcIjtcbmltcG9ydCB7UmVjb3JkUHJvdmlkZXJ9IGZyb20gXCJAL3Byb3ZpZGVycy9yZWNvcmRcIjtcbmltcG9ydCB7bWVtYmVyc1RvUGVyc29uc30gZnJvbSBcIkAvY29tcG9uZW50cy93b3Jrc3BhY2UvbWFpbi92aWV3cy90YWJsZS9yZW5kZXJlci9maWVsZHMvcGVyc29uXCI7XG5pbXBvcnQge0xpbmtlZERhdGFiYXNlcywgdHJhbnNmb3JtUmF3UmVjb3Jkc30gZnJvbSBcIm9wZW5kYi1hcHAtZGItdXRpbHMvbGliL3V0aWxzL2RiXCI7XG5pbXBvcnQge3VzZU1heWJlVGVtcGxhdGV9IGZyb20gXCJAL3Byb3ZpZGVycy90ZW1wbGF0ZVwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IHt1c2VNYXliZVNoYXJlZH0gZnJvbSBcIkAvcHJvdmlkZXJzL3NoYXJlZFwiO1xuaW1wb3J0IHtSZWNvcmRFeHRyYXN9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vcmVjb3JkL2NvbXBvbmVudHMvcmVjb3JkRXh0cmFzXCI7XG5pbXBvcnQge0lEZXRlY3RlZEJhcmNvZGUsIFNjYW5uZXJ9IGZyb20gJ0B5dWRpZWwvcmVhY3QtcXItc2Nhbm5lcic7XG5pbXBvcnQge1BvcG92ZXIsIFBvcG92ZXJDb250ZW50LCBQb3BvdmVyVHJpZ2dlcix9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvcG9wb3ZlclwiXG5pbXBvcnQge2ZpbHRlckFuZFNvcnRSZWNvcmRzLCBzZWFyY2hGaWx0ZXJlZFJlY29yZHN9IGZyb20gXCJAL2NvbXBvbmVudHMvd29ya3NwYWNlL21haW4vdmlld3MvdGFibGVcIjtcbmltcG9ydCB7dXNlTWF5YmVSZWNvcmR9IGZyb20gXCJAL3Byb3ZpZGVycy9yZWNvcmRcIjtcbmltcG9ydCB7UmVjb3JkVGFiVmlld3NQcm92aWRlcn0gZnJvbSBcIkAvcHJvdmlkZXJzL3JlY29yZFRhYlZpZXdzXCI7XG5pbXBvcnQge0FkZFJlY29yZE1vZGFsfSBmcm9tIFwiQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2FkZHJlY29yZG1vZGFsXCI7XG5pbXBvcnQge3VzZVN0YWNrZWRQZWVrfSBmcm9tIFwiQC9wcm92aWRlcnMvc3RhY2tlZHBlZWtcIjtcblxuXG5leHBvcnQgaW50ZXJmYWNlIFZpZXdHcm91cFByb3BzIHtcbiAgICBjb250ZXh0PzogXCJkYXRhYmFzZVwiIHwgXCJwYWdlXCJ8J3JlY29yZF90YWInXG4gICAgcGFyZW50SWQ6IHN0cmluZ1xuICAgIHZpZXdJZDogc3RyaW5nXG59XG5cbmV4cG9ydCBjb25zdCBWaWV3Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8eyBjb250ZXh0PzogXCJkYXRhYmFzZVwiIHwgXCJwYWdlXCIgfCBcInJlY29yZF90YWJcIiB9Pih7Y29udGV4dDogJ3BhZ2UnfSk7XG5leHBvcnQgY29uc3QgdXNlVmlld0NvbnRleHQgPSAoKSA9PiB1c2VDb250ZXh0KFZpZXdDb250ZXh0KTtcblxuZXhwb3J0IGNvbnN0IFZpZXdzUm9vdExheW91dCA9IChwcm9wczogUHJvcHNXaXRoQ2hpbGRyZW48Vmlld0dyb3VwUHJvcHM+KSA9PiB7XG4gICAgY29uc3Qge3VybH0gPSB1c2VXb3Jrc3BhY2UoKVxuICAgIGNvbnN0IHt0b2tlbn0gPSB1c2VBdXRoKClcblxuICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXG4gICAgY29uc3QgZG9jdW1lbnRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2RvY3VtZW50SWQnKVxuXG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcbiAgICBjb25zdCB7dmlld3NNYXAsIGFjY2Vzc0xldmVsLCBwYWdlfSA9IHVzZVBhZ2UoKVxuICAgIGNvbnN0IFtuZXdWaWV3LCBzZXROZXdWaWV3XSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgICAgICAgIFxuICAgIGNvbnN0IHtjcmVhdGVSZWNvcmRzLCBkZWxldGVSZWNvcmRzLCBzbWFydFVwZGF0ZVZpZXdEZWZpbml0aW9uLCBwZWVrUmVjb3JkSWQsIHNldFBlZWtSZWNvcmRJZH0gPSB1c2VWaWV3cygpXG4gICAgY29uc3Qge2ZpbHRlciwgc29ydHMsIHNlYXJjaCwgc2V0RmlsdGVyLCBzZXRTb3J0cywgc2V0U2VhcmNofSA9IHVzZVZpZXdGaWx0ZXJpbmcoKVxuICAgIGNvbnN0IHtzZWxlY3RlZElkcywgc2V0U2VsZWN0ZWRJZHN9ID0gdXNlVmlld1NlbGVjdGlvbigpXG4gICAgY29uc3Qge2RhdGFiYXNlU3RvcmUsIHdvcmtzcGFjZX0gPSB1c2VXb3Jrc3BhY2UoKVxuICAgIGNvbnN0IHt0b2FzdH0gPSB1c2VBbGVydCgpXG4gICAgY29uc3QgbWF5YmVUZW1wbGF0ZSA9IHVzZU1heWJlVGVtcGxhdGUoKVxuICAgIGNvbnN0IG1heWJlUmVjb3JkID0gdXNlTWF5YmVSZWNvcmQoKVxuICAgIGNvbnN0IHtvcGVuUmVjb3JkfSA9IHVzZVN0YWNrZWRQZWVrKClcbiAgICBcbiAgICAvLyBTdGF0ZSBmb3IgQWRkUmVjb3JkTW9kYWxcbiAgICBjb25zdCBbc2hvd0FkZE1vZGFsLCBzZXRTaG93QWRkTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgICAvLyBGb3IgcmVjb3JkX3RhYiBjb250ZXh0LCBnZXQgdmlldyBmcm9tIGRhdGFiYXNlIG1ldGFkYXRhXG4gICAgbGV0IHZpZXc6IFZpZXcgfCBudWxsID0gbnVsbFxuICAgIGlmIChwcm9wcy5jb250ZXh0ID09PSAncmVjb3JkX3RhYicpIHtcbiAgICAgICAgY29uc3QgZGF0YWJhc2UgPSBkYXRhYmFzZVN0b3JlW3Byb3BzLnBhcmVudElkXT8uZGF0YWJhc2VcbiAgICAgICAgaWYgKGRhdGFiYXNlICYmIGRhdGFiYXNlLm1ldGEgJiYgZGF0YWJhc2UubWV0YS5yZWNvcmRWaWV3c01hcCkge1xuICAgICAgICAgICAgdmlldyA9IGRhdGFiYXNlLm1ldGEucmVjb3JkVmlld3NNYXBbcHJvcHMudmlld0lkXSB8fCBudWxsXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIXZpZXcpIHtcbiAgICAgICAgdmlldyA9IHZpZXdzTWFwW3Byb3BzLnZpZXdJZF0gfHwgbnVsbFxuICAgIH1cbiAgICBcbiAgICBsZXQgdmlld1R5cGU6IFZpZXdUeXBlIHwgdW5kZWZpbmVkID0gdmlldz8udHlwZVxuXG4gICAgLy8gQ29udGV4dC1hd2FyZSB1cGRhdGUgZnVuY3Rpb24gdGhhdCBhdXRvbWF0aWNhbGx5IGRldGVjdHMgcmVjb3JkIHRhYiB2cyByZWd1bGFyIHZpZXdzXG4gICAgY29uc3QgY29udGV4dEF3YXJlVXBkYXRlVmlld0RlZmluaXRpb24gPSAodXBkYXRlOiBQYXJ0aWFsPFZpZXdEZWZpbml0aW9uPikgPT4ge1xuICAgICAgICBjb25zdCBpc1JlY29yZFRhYiA9IHByb3BzLmNvbnRleHQgPT09ICdyZWNvcmRfdGFiJ1xuICAgICAgICBjb25zdCBkYXRhYmFzZUlkID0gaXNSZWNvcmRUYWIgPyBwcm9wcy5wYXJlbnRJZCA6IHVuZGVmaW5lZFxuICAgICAgICBcbiAgICAgICAgcmV0dXJuIHNtYXJ0VXBkYXRlVmlld0RlZmluaXRpb24oXG4gICAgICAgICAgICB2aWV3Py5pZCB8fCAnJywgXG4gICAgICAgICAgICB2aWV3Py5wYWdlSWQgfHwgJycsIFxuICAgICAgICAgICAgdXBkYXRlLCBcbiAgICAgICAgICAgIHsgZGF0YWJhc2VJZCwgaXNSZWNvcmRUYWIgfVxuICAgICAgICApXG4gICAgfVxuXG4gICAgY29uc3QgY2FuQ29udGFjdCA9IGZhbHNlO1xuXG4gICAgY29uc3QgY2FuRWRpdCA9IGFjY2Vzc0xldmVsICYmIFtBY2Nlc3NMZXZlbC5GdWxsLCBBY2Nlc3NMZXZlbC5FZGl0XS5pbmNsdWRlcyhhY2Nlc3NMZXZlbClcbiAgICBjb25zdCBoYXNGdWxsQWNjZXNzID0gYWNjZXNzTGV2ZWwgJiYgYWNjZXNzTGV2ZWwgPT09IEFjY2Vzc0xldmVsLkZ1bGxcblxuICAgIGxldCB2aWV3RmlsdGVyOiBEYlJlY29yZEZpbHRlciB8IG51bGwgPSB7Y29uZGl0aW9uczogW10sIG1hdGNoOiBNYXRjaC5BbGx9XG4gICAgaWYgKHZpZXdUeXBlID09PSBWaWV3VHlwZS5Cb2FyZCB8fCB2aWV3VHlwZSA9PT0gVmlld1R5cGUuVGFibGUpIHtcbiAgICAgICAgY29uc3QgZGVmaW5pdGlvbiA9IHZpZXcuZGVmaW5pdGlvbiBhcyBUYWJsZVZpZXdEZWZpbml0aW9uXG4gICAgICAgIHZpZXdGaWx0ZXIgPSBkZWZpbml0aW9uLmZpbHRlciB8fCB2aWV3RmlsdGVyXG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlQWRkUmVjb3JkID0gKCkgPT4ge1xuICAgICAgICBpZiAoIXZpZXcpIHJldHVyblxuICAgICAgICBpZiAodmlldy50eXBlICE9PSBWaWV3VHlwZS5Cb2FyZCAmJiB2aWV3LnR5cGUgIT09IFZpZXdUeXBlLlRhYmxlKSByZXR1cm5cbiAgICAgICAgXG4gICAgICAgIC8vIERpZmZlcmVudCBiZWhhdmlvciBiYXNlZCBvbiBjb250ZXh0XG4gICAgICAgIGlmIChwcm9wcy5jb250ZXh0ID09PSAncmVjb3JkX3RhYicpIHtcbiAgICAgICAgICAgIC8vIEluIHJlY29yZCB0YWJzOiBVc2Ugc21hcnQgbW9kYWwgKGVzcGVjaWFsbHkgZm9yIGNyb3NzLWRhdGFiYXNlKVxuICAgICAgICAgICAgc2V0U2hvd0FkZE1vZGFsKHRydWUpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyBJbiBtYWluIHZpZXdzOiBEaXJlY3QgcmVjb3JkIGNyZWF0aW9uIChvcmlnaW5hbCBiZWhhdmlvcilcbiAgICAgICAgICAgIGhhbmRsZURpcmVjdEFkZFJlY29yZCgpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBoYW5kbGVEaXJlY3RBZGRSZWNvcmQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIGlmICghdmlldykgcmV0dXJuXG4gICAgICAgIGNvbnN0IGRlZmluaXRpb24gPSB2aWV3LmRlZmluaXRpb24gYXMgVGFibGVWaWV3RGVmaW5pdGlvblxuICAgICAgICBpZiAoIWRlZmluaXRpb24uZGF0YWJhc2VJZCkgcmV0dXJuXG4gICAgICAgIFxuICAgICAgICAvLyBDcmVhdGUgcmVjb3JkIGRpcmVjdGx5IHdpdGhvdXQgbW9kYWwgKG9yaWdpbmFsIGJlaGF2aW9yKVxuICAgICAgICBjb25zdCByUyA9IGF3YWl0IGNyZWF0ZVJlY29yZHMoZGVmaW5pdGlvbi5kYXRhYmFzZUlkLCBbe31dKVxuICAgICAgICBcbiAgICAgICAgLy8gT25seSBwZWVrIGlmIHRoZXJlIGFyZSBmaWx0ZXJzL3NlYXJjaCAob3JpZ2luYWwgbG9naWMpXG4gICAgICAgIGNvbnN0IHNob3VsZFBlZWsgPSAoc2VhcmNoICYmIHNlYXJjaC50cmltKCkpIHx8IGZpbHRlcj8uY29uZGl0aW9ucz8ubGVuZ3RoID4gMCB8fCAodmlld0ZpbHRlciAmJiB2aWV3RmlsdGVyLmNvbmRpdGlvbnMgJiYgdmlld0ZpbHRlcj8uY29uZGl0aW9ucz8ubGVuZ3RoID4gMClcbiAgICAgICAgaWYgKHJTICYmIHJTLnJlY29yZHMgJiYgclMucmVjb3Jkcy5sZW5ndGggPiAwICYmIHNob3VsZFBlZWspIHtcbiAgICAgICAgICAgIHNldFBlZWtSZWNvcmRJZChyUy5yZWNvcmRzWzBdLmlkKVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaGFuZGxlUmVjb3JkQ3JlYXRlZCA9IChyZWNvcmRJZDogc3RyaW5nKSA9PiB7XG4gICAgICAgIGlmICghdmlldykgcmV0dXJuXG4gICAgICAgIGNvbnN0IGRlZmluaXRpb24gPSB2aWV3LmRlZmluaXRpb24gYXMgVGFibGVWaWV3RGVmaW5pdGlvblxuICAgICAgICBpZiAoIWRlZmluaXRpb24uZGF0YWJhc2VJZCkgcmV0dXJuXG4gICAgICAgIFxuICAgICAgICAvLyBJbiByZWNvcmQgdGFiczogU3RhY2sgdGhlIG5ld2x5IGNyZWF0ZWQgcmVjb3JkXG4gICAgICAgIG9wZW5SZWNvcmQocmVjb3JkSWQsIGRlZmluaXRpb24uZGF0YWJhc2VJZClcbiAgICAgICAgc2V0U2hvd0FkZE1vZGFsKGZhbHNlKVxuICAgIH1cblxuICAgIGNvbnN0IGRlbGV0ZVNlbGVjdGVkID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICBpZiAoIXZpZXcpIHJldHVyblxuICAgICAgICBpZiAodmlldy50eXBlICE9PSBWaWV3VHlwZS5Cb2FyZCAmJiB2aWV3LnR5cGUgIT09IFZpZXdUeXBlLlRhYmxlKSByZXR1cm5cbiAgICAgICAgY29uc3QgZGVmaW5pdGlvbiA9IHZpZXcuZGVmaW5pdGlvbiBhcyBUYWJsZVZpZXdEZWZpbml0aW9uXG5cbiAgICAgICAgaWYgKCFkZWZpbml0aW9uLmRhdGFiYXNlSWQpIHJldHVyblxuICAgICAgICBjb25zdCB7ZGF0YWJhc2VJZH0gPSBkZWZpbml0aW9uXG5cbiAgICAgICAgYXdhaXQgZGVsZXRlUmVjb3JkcyhkYXRhYmFzZUlkLCBzZWxlY3RlZElkcylcbiAgICB9XG4gICAgY29uc3QgY29weVNoYXJlZFVybCA9ICgpID0+IHtcbiAgICAgICAgaWYgKCF2aWV3KSByZXR1cm5cbiAgICAgICAgY29uc3Qge2lkLCBuYW1lfSA9IHZpZXdcbiAgICAgICAgY29uc3QgdXJsID0gZ2V0Vmlld1B1YmxpY1VybChpZCwgbmFtZSlcbiAgICAgICAgY29weVRvQ2xpcGJvYXJkKHVybClcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkxpbmsgY29waWVkIHRvIGNsaXBib2FyZFwiKVxuICAgIH1cblxuICAgIGxldCBkYXRhYmFzZTogRGF0YWJhc2UgfCBudWxsID0gbnVsbFxuICAgIGxldCBkYklkID0gJydcbiAgICBpZiAodmlldyAmJiBbVmlld1R5cGUuVGFibGUsIFZpZXdUeXBlLkJvYXJkLCBWaWV3VHlwZS5TdW1tYXJ5VGFibGUsIFZpZXdUeXBlLkZvcm0sIFZpZXdUeXBlLkNhbGVuZGFyLCBWaWV3VHlwZS5MaXN0Vmlld10uaW5jbHVkZXModmlld1R5cGUpKSB7XG4gICAgICAgIGRiSWQgPSAodmlldy5kZWZpbml0aW9uIGFzIFRhYmxlVmlld0RlZmluaXRpb24pLmRhdGFiYXNlSWRcbiAgICAgICAgZGF0YWJhc2UgPSBkYXRhYmFzZVN0b3JlW2RiSWRdID8gZGF0YWJhc2VTdG9yZVtkYklkXS5kYXRhYmFzZSA6IG51bGxcbiAgICB9XG4gIFxuICAgIGNvbnN0IGN1cnJlbnRWaWV3SWRSZWYgPSB1c2VSZWYocHJvcHMudmlld0lkIHx8ICcnKVxuXG4gICAgY29uc3Qgdmlld0lkID0gcHJvcHMudmlld0lkXG4gICAgY29uc3QgcGFnZUlkID0gdmlldz8ucGFnZUlkIHx8ICcnXG4gICAgY29uc3Qgd29ya3NwYWNlSWQgPSB3b3Jrc3BhY2Uud29ya3NwYWNlLmlkXG4gICAgY29uc3Qgdmlld0V4aXN0cyA9ICEhdmlld1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCF2aWV3RXhpc3RzKSByZXR1cm5cbiAgICAgICAgY29uc3QgdGltZW91dCA9IHNldFRpbWVvdXQoYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZGF0YWJhc2VJZCA9IGRiSWQgPyBkYklkIDogdW5kZWZpbmVkXG4gICAgICAgICAgICBhd2FpdCBwdXNoRXZlbnQodG9rZW4/LnRva2VuIHx8ICcnLCB7d29ya3NwYWNlSWQsIHBhZ2VJZCwgZXZlbnQ6IEV2ZW50VHlwZS5WaWV3LCBkYXRhYmFzZUlkLCB2aWV3SWR9KVxuICAgICAgICB9LCAzMDAwKVxuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgaWYgKHRpbWVvdXQpIGNsZWFyVGltZW91dCh0aW1lb3V0KVxuICAgICAgICB9XG4gICAgfSwgW2RiSWQsIHRva2VuPy50b2tlbiwgd29ya3NwYWNlSWQsIHBhZ2VJZCwgdmlld0lkLCB2aWV3RXhpc3RzXSlcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy52aWV3SWQgPT09IGN1cnJlbnRWaWV3SWRSZWYuY3VycmVudCkgcmV0dXJuXG4gICAgICAgIGN1cnJlbnRWaWV3SWRSZWYuY3VycmVudCA9IHByb3BzLnZpZXdJZFxuICAgICAgICBzZXRTb3J0cyhbXSlcbiAgICAgICAgc2V0RmlsdGVyKHtjb25kaXRpb25zOiBbXSwgbWF0Y2g6IE1hdGNoLkFsbH0pXG4gICAgICAgIHNldFNlYXJjaCgnJylcbiAgICAgICAgc2V0U2VsZWN0ZWRJZHMoW10pXG4gICAgICAgIHNldFBlZWtSZWNvcmRJZCgnJylcbiAgICB9LCBbcHJvcHMudmlld0lkXSkgXG4gICAgLy8gLCBbcHJvcHMudmlld0lkLCBzZXRGaWx0ZXIsIHNldFNvcnRzLCBzZXRTZWFyY2gsIHNldFNlbGVjdGVkSWRzLCBzZXRQZWVrUmVjb3JkSWRdKVxuICAgIC8vIGNvbnN0IFtwZWVrUmVjb3JkSWQsIHNldFBlZWtSZWNvcmRJZF0gPSB1c2VTdGF0ZShcIlwiKVxuXG4gICAgcmV0dXJuIChcbiAgICAgICAgPFZpZXdDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7Y29udGV4dDogcHJvcHMuY29udGV4dH19PlxuICAgICAgICAgICAgeyF2aWV3ICYmIDw+XG4gICAgICAgICAgICAgICAgPFBhZ2VMb2FkZXIgc2l6ZT1cImZ1bGxcIiBlcnJvcj1cIlRoZSByZXF1ZXN0ZWQgY29udGVudCBkb2VzIG5vdCBleGlzdHNcIiBjdGE9e3tcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiR28gSG9tZVwiLFxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrOiAoKSA9PiByb3V0ZXIucmVwbGFjZSh1cmwoKSlcbiAgICAgICAgICAgICAgICB9fS8+XG4gICAgICAgICAgICA8Lz59XG5cblxuICAgICAgICAgICAge3ZpZXcgJiYgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG92ZXJmbG93LWhpZGRlbiBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgey8qVmlldyBHcm91cCBIZWFkZXIqL31cbiAgICAgICAgICAgICAgey8qPGRpdiBjbGFzc05hbWU9XCJwLTIgcHgtNCBoLTEyIGZsZXggaXRlbXMtY2VudGVyIGJvcmRlci1iIGJvcmRlci1uZXV0cmFsLTMwMFwiPiovfVxuICAgICAgICAgICAgICAgIHsvKiBIaWRlIGhlYWRlciBmb3IgY2FsZW5kYXIgdmlldyB3aGVuIGl0IGhhcyBubyBjb250cm9scyAqL31cbiAgICAgICAgICAgICAgICB7dmlld1R5cGUgIT09IFZpZXdUeXBlLkNhbGVuZGFyICYmIDxkaXYgY2xhc3NOYW1lPVwicC0yIGgtMTIgZmxleCBpdGVtcy1jZW50ZXIgYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIGdhcC0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAge3Byb3BzLmNvbnRleHQgIT09ICdyZWNvcmRfdGFiJyAmJiA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFZpZXdTd2l0Y2hlciBjb250ZXh0PXtwcm9wcy5jb250ZXh0fSB2aWV3SWQ9e3Byb3BzLnZpZXdJZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3JlYXRhYmxlPXtjYW5FZGl0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlZGl0YWJsZT17Y2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVsZXRhYmxlPXtoYXNGdWxsQWNjZXNzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbG9uZWFibGU9e2hhc0Z1bGxBY2Nlc3N9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3ROZXdWaWV3PXsoKSA9PiBzZXROZXdWaWV3KHRydWUpfS8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2FuRWRpdCAmJiA8Vmlld0NyZWF0b3IgY29udGV4dD17cHJvcHMuY29udGV4dH0gb3Blbj17bmV3Vmlld30gc2V0T3Blbj17c2V0TmV3Vmlld30vPn1cbiAgICAgICAgICAgICAgICAgICAgICAgIHshbWF5YmVUZW1wbGF0ZSAmJiA8U2hhcmVWaWV3XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlldz17dmlld31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWdlPXtwYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50SWQ9e2RvY3VtZW50SWQgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9tYWluPXt3b3Jrc3BhY2Uud29ya3NwYWNlLmRvbWFpbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyQWxpZ249e1wic3RhcnRcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyPXs8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgIXB4LTMgaC1hdXRvIGdhcC0yIGp1c3RpZnktc3RhcnQgZm9udC1tZWRpdW1cIiwgdmlldy5pc1B1Ymxpc2hlZCAmJiAndGV4dC1ibHVlLTYwMCBmb250LXNlbWlib2xkJyl9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dVcEZyb21BcmNJY29uIGNsYXNzTmFtZT0nc2l6ZS0zJy8+IFNoYXJlIFZpZXdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj59Lz59XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9wcy5jb250ZXh0ID09PSAncGFnZScgJiYgZGF0YWJhc2UgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGFnZUNvbnRleHRTb3VyY2VEYXRhYmFzZSBkYXRhYmFzZT17ZGF0YWJhc2V9Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPn1cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG4gICAgICAgICAgICAgICAgICAgIHtwcm9wcy5jb250ZXh0ID09PSAncmVjb3JkX3RhYicgJiYgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj48L2Rpdj59XG5cbiAgICAgICAgICAgICAgICAgICAge2RhdGFiYXNlICYmIFtWaWV3VHlwZS5UYWJsZSwgVmlld1R5cGUuQm9hcmRdLmluY2x1ZGVzKHZpZXdUeXBlIGFzIFZpZXdUeXBlKSAmJiA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgeyEodmlldy5kZWZpbml0aW9uIGFzIFRhYmxlVmlld0RlZmluaXRpb24pLmxvY2tDb250ZW50ICYmIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qeyhzZWFyY2ggJiYgc2VhcmNoLnRyaW0oKSkgfHwgZmlsdGVyPy5jb25kaXRpb25zPy5sZW5ndGggPiAwIHx8ICh2aWV3RmlsdGVyICYmIHZpZXdGaWx0ZXIuY29uZGl0aW9ucyAmJiB2aWV3RmlsdGVyPy5jb25kaXRpb25zPy5sZW5ndGggPiAwKSA/IDw+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKjwvPiA6IDw+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgICAgICAgICBvbkNsaWNrPXthZGRSZWNvcmR9Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgIXB4LTMgaC1hdXRvIGdhcC0yIGp1c3RpZnktc3RhcnQgZm9udC1tZWRpdW1cIj4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgICAgIDxDaXJjbGVQbHVzSWNvbiBjbGFzc05hbWU9XCJzaXplLTNcIi8+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICAgICBBZGQqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgPC9CdXR0b24+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIDwvPn0qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyp7KHNlYXJjaCAmJiBzZWFyY2gudHJpbSgpKSB8fCBmaWx0ZXI/LmNvbmRpdGlvbnM/Lmxlbmd0aCA+IDAgfHwgKHZpZXdGaWx0ZXIgJiYgdmlld0ZpbHRlci5jb25kaXRpb25zICYmIHZpZXdGaWx0ZXI/LmNvbmRpdGlvbnM/Lmxlbmd0aCA+IDApID8gPD4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgPFBlZWtSZWNvcmQgdHJpZ2dlcj17PEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZvbnQtbWVkaXVtXCI+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICAgICA8Q2lyY2xlUGx1c0ljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIvPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgICAgQWRkKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIDwvQnV0dG9uPn0+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIDwvUGVla1JlY29yZD4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyo8Lz4gOiA8PiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgICAgICAgICBvbkNsaWNrPXthZGRSZWNvcmR9Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZvbnQtbWVkaXVtXCI+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgICAgICAgPENpcmNsZVBsdXNJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgICAgICBBZGQqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgIDwvQnV0dG9uPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiA8Lz59Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qeyEoc2VhcmNoIHx8IHNlYXJjaC50cmltKCkpICYmIHNlbGVjdGVkSWRzLmxlbmd0aCA9PT0gMCAmJiB2aWV3VHlwZSA9PT0gVmlld1R5cGUuVGFibGUgJiYgPD4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgICAgICAgIG9uQ2xpY2s9e2FkZFJlY29yZH0qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bVwiPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAgICAgPENpcmNsZVBsdXNJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgICAgIEFkZCovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICA8L0J1dHRvbj4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyo8Lz59Ki99XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2FuRWRpdCAmJiB2aWV3VHlwZSA9PT0gVmlld1R5cGUuVGFibGUgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZFJlY29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENpcmNsZVBsdXNJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFkZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkSWRzLmxlbmd0aCA+IDAgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPSdmb250LXNlbWlib2xkIHRleHQteHMgdGV4dC1ibHVlLTYwMCBzZWxlY3Qtbm9uZSc+e3NlbGVjdGVkSWRzLmxlbmd0aH0gJm5ic3A7c2VsZWN0ZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxVcGRhdGVSZWNvcmRzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyPXs8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNxdWFyZVBlbkljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVwZGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YWJhc2U9e2RhdGFiYXNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRzPXtzZWxlY3RlZElkc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uVXBkYXRlPXsoKSA9PiBzZXRTZWxlY3RlZElkcyhbXSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtkZWxldGVTZWxlY3RlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoTGlzdEljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGVsZXRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz59XG5cbiAgICAgICAgICAgICAgICAgICAgPC8+fVxuICAgICAgICAgICAgICAgICAgICB7ZGF0YWJhc2UgJiYgW1ZpZXdUeXBlLlRhYmxlLCBWaWV3VHlwZS5Cb2FyZCwgVmlld1R5cGUuU3VtbWFyeVRhYmxlLCBWaWV3VHlwZS5MaXN0VmlldywgVmlld1R5cGUuQ2FsZW5kYXJdLmluY2x1ZGVzKHZpZXdUeXBlIGFzIFZpZXdUeXBlKSA/IDw+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2FuQ29udGFjdCAmJiBwcm9wcy5jb250ZXh0ICE9PSAncmVjb3JkX3RhYicgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3BhcmtsZXNJY29uIGNsYXNzTmFtZT1cInNpemUtNFwiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRW5yaWNoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgIXB4LTMgaC1hdXRvIGdhcC0yIGp1c3RpZnktc3RhcnQgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJzaXplLTRcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1lc3NhZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjYW5FZGl0ICYmIHByb3BzLmNvbnRleHQgIT09ICdyZWNvcmRfdGFiJyAmJiA8U2VuZEVtYWlsV3JhcHBlckZvclZpZXdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhYmFzZT17ZGF0YWJhc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlldz17dmlld30vPn1cblxuICAgICAgICAgICAgICAgICAgICAgICAgPFZpZXdGaWx0ZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhYmFzZT17ZGF0YWJhc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIHJvdW5kZWQtZnVsbCBwLTEuNSAhcHgtMyBoLWF1dG8gZ2FwLTIganVzdGlmeS1zdGFydCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEZpbHRlckxpc3RJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXIuY29uZGl0aW9ucy5sZW5ndGggPiAwID9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBgJHtmaWx0ZXIuY29uZGl0aW9ucy5sZW5ndGh9IGZpbHRlcnNgIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnRmlsdGVyJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbHRlcj17ZmlsdGVyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtzZXRGaWx0ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFJlY29yZElkPXttYXliZVJlY29yZD8ucmVjb3JkSW5mby5yZWNvcmQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudFJlY29yZERhdGFiYXNlSWQ9e21heWJlUmVjb3JkPy5yZWNvcmRJbmZvLnJlY29yZC5kYXRhYmFzZUlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgICAgICAgICAgeyh2aWV3VHlwZSA9PT0gVmlld1R5cGUuVGFibGUgfHwgdmlld1R5cGUgPT09IFZpZXdUeXBlLlN1bW1hcnlUYWJsZSkgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Vmlld1NvcnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YWJhc2U9e2RhdGFiYXNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0cz17c29ydHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtzZXRTb3J0c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJpZ2dlcj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93VXBXaWRlU2hvcnRJY29uIGNsYXNzTmFtZT1cInNpemUtM1wiLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c29ydHMubGVuZ3RoID4gMCA/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGAke3NvcnRzLmxlbmd0aH0gc29ydHNgIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ1NvcnQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IGZsZXggaG92ZXI6YmctYWNjZW50IGZvY3VzOmJnLWFjY2VudCBhY3RpdmU6YmctYWNjZW50IGl0ZW1zLWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBmb3JtPVwic2VhcmNoLWlucHV0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKjxNYWduaWZ5aW5nR2xhc3NJY29uIGNsYXNzTmFtZT1cInNpemUtNFwiLz4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0NpcmNsZUljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0IHBsYWNlaG9sZGVyPVwiU2VhcmNoXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0U2VhcmNoKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0cmFuc2l0aW9uLWFsbCBvdXRsaW5lLW5vbmUgaC1hdXRvICFwLTAgIXJpbmctMCB3LTEyIGZvY3VzOnctNDggIWJnLXRyYW5zcGFyZW50IGJvcmRlci0wIHNoYWRvdy1ub25lIGRyb3Atc2hhZG93LW5vbmVcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPFNjYW5uYWJsZUNvZGVTY2FubmVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25SZWNvcmRTY2FuPXtzZXRQZWVrUmVjb3JkSWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YWJhc2U9e2RhdGFiYXNlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXdGaWx0ZXI9e3ZpZXdGaWx0ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyPXtmaWx0ZXJ9Lz5cblxuICAgICAgICAgICAgICAgICAgICAgICAge2NhbkVkaXQgJiYgdmlld1R5cGUgIT09IFZpZXdUeXBlLlN1bW1hcnlUYWJsZSAmJiA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb2x1bW5zUmVvcmRlclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRlZmluaXRpb25VcGRhdGU9e3UgPT4gY29udGV4dEF3YXJlVXBkYXRlVmlld0RlZmluaXRpb24odSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZpZXc9e3ZpZXd9Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPn1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjYW5FZGl0ICYmIHZpZXdUeXBlID09PSBWaWV3VHlwZS5TdW1tYXJ5VGFibGUgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3VtbWFyeUNvbHVtbkdyb3VwQnlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWZpbml0aW9uVXBkYXRlPXt1ID0+IGNvbnRleHRBd2FyZVVwZGF0ZVZpZXdEZWZpbml0aW9uKHUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3PXt2aWV3fS8+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3VtbWFyeUNvbHVtbnNSZW9yZGVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVmaW5pdGlvblVwZGF0ZT17dSA9PiBjb250ZXh0QXdhcmVVcGRhdGVWaWV3RGVmaW5pdGlvbih1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlldz17dmlld30vPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAge2NhbkVkaXQgJiYgPFZpZXdNb3JlT3B0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGRlZmluaXRpb249e3ZpZXcuZGVmaW5pdGlvbiBhcyBUYWJsZVZpZXdEZWZpbml0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3PXt2aWV3fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFiYXNlPXtkYXRhYmFzZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZElkcz17c2VsZWN0ZWRJZHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyPXtmaWx0ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydHM9e3NvcnRzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlYXJjaD17c2VhcmNofVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVmaW5pdGlvblVwZGF0ZT17dSA9PiBjb250ZXh0QXdhcmVVcGRhdGVWaWV3RGVmaW5pdGlvbih1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50UmVjb3JkSWQ9e21heWJlUmVjb3JkPy5yZWNvcmRJbmZvPy5yZWNvcmQ/LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRSZWNvcmREYXRhYmFzZUlkPXttYXliZVJlY29yZD8ucmVjb3JkSW5mbz8ucmVjb3JkPy5kYXRhYmFzZUlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz59XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKjxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgIXB4LTMgaC1hdXRvIGdhcC0yIGp1c3RpZnktc3RhcnRcIj4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICA8QXJyb3dVcE9uU3F1YXJlSWNvbiBjbGFzc05hbWU9XCJzaXplLTRcIi8+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgRXhwb3J0Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICB7Lyo8L0J1dHRvbj4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKjxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xLjUgIXB4LTMgaC1hdXRvIGdhcC0yIGp1c3RpZnktc3RhcnRcIj4qL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICA8UHJpbnRlckljb24gY2xhc3NOYW1lPVwic2l6ZS00XCIvPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIFByaW50Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICB7Lyo8L0J1dHRvbj4qL31cbiAgICAgICAgICAgICAgICAgICAgPC8+IDogPD5cblxuICAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG4gICAgICAgICAgICAgICAgICAgIHtjYW5FZGl0ICYmIGRhdGFiYXNlICYmIHZpZXdUeXBlID09PSBWaWV3VHlwZS5Gb3JtICYmIHByb3BzLmNvbnRleHQgIT09ICdyZWNvcmRfdGFiJyAmJiA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZvcm1WaWV3TW9yZU9wdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWNhbkVkaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlldz17dmlld31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhYmFzZT17ZGF0YWJhc2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWZpbml0aW9uVXBkYXRlPXt1ID0+IGNvbnRleHRBd2FyZVVwZGF0ZVZpZXdEZWZpbml0aW9uKHUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG4gICAgICAgICAgICAgICAgICAgIHtjYW5FZGl0ICYmIHZpZXdUeXBlID09PSBWaWV3VHlwZS5EYXNoYm9hcmQgJiYgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgPERhc2hib2FyZFZpZXdNb3JlT3B0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3PXt2aWV3fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVmaW5pdGlvblVwZGF0ZT17dSA9PiBjb250ZXh0QXdhcmVVcGRhdGVWaWV3RGVmaW5pdGlvbih1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvPn1cbiAgICAgICAgICAgICAgICAgICAge2NhbkVkaXQgJiYgdmlld1R5cGUgPT09IFZpZXdUeXBlLkRvY3VtZW50ICYmIHByb3BzLmNvbnRleHQgIT09ICdyZWNvcmRfdGFiJyAmJiA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPERvY1ZpZXdNb3JlT3B0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshY2FuRWRpdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3PXt2aWV3fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRGVmaW5pdGlvblVwZGF0ZT17dSA9PiBjb250ZXh0QXdhcmVVcGRhdGVWaWV3RGVmaW5pdGlvbih1KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICAgICAgPC8+fVxuXG5cbiAgICAgICAgICAgICAgICA8L2Rpdj59XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuIHByPTFcIj5cbiAgICAgICAgICAgICAgICAgICAge3Byb3BzLmNoaWxkcmVufVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+fVxuXG4gICAgICAgICAgICB7ZGF0YWJhc2UgJiYgcGVla1JlY29yZElkICYmICFtYXliZVJlY29yZCAmJiA8UGVla1JlY29yZFxuICAgICAgICAgICAgICAgIGNhbkVkaXQ9e2NhbkVkaXR9XG4gICAgICAgICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0UGVla1JlY29yZElkKCcnKX1cbiAgICAgICAgICAgICAgICByZWNvcmRJZD17cGVla1JlY29yZElkfSBkYXRhYmFzZUlkPXtkYXRhYmFzZS5pZH0vPn1cblxuICAgICAgICAgICAgey8qIEFkZFJlY29yZE1vZGFsIGZvciBpbnRlbGxpZ2VudCByZWNvcmQgY3JlYXRpb24gKi99XG4gICAgICAgICAgICB7ZGF0YWJhc2UgJiYgc2hvd0FkZE1vZGFsICYmIChcbiAgICAgICAgICAgICAgICA8QWRkUmVjb3JkTW9kYWxcbiAgICAgICAgICAgICAgICAgICAgb3Blbj17c2hvd0FkZE1vZGFsfVxuICAgICAgICAgICAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRTaG93QWRkTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgICBkYXRhYmFzZUlkPXtkYXRhYmFzZS5pZH1cbiAgICAgICAgICAgICAgICAgICAgdmlld0ZpbHRlcj17dmlld0ZpbHRlciB8fCB1bmRlZmluZWR9XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHR1YWxGaWx0ZXI9e2ZpbHRlcn1cbiAgICAgICAgICAgICAgICAgICAgb25SZWNvcmRDcmVhdGVkPXtoYW5kbGVSZWNvcmRDcmVhdGVkfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgIDwvVmlld0NvbnRleHQuUHJvdmlkZXI+XG4gICAgKVxufVxuXG5jb25zdCBQYWdlQ29udGV4dFNvdXJjZURhdGFiYXNlID0gKHtkYXRhYmFzZX06IHsgZGF0YWJhc2U6IERhdGFiYXNlIH0pID0+IHtcblxuICAgIGNvbnN0IHtkYXRhYmFzZVBhZ2VTdG9yZSwgZGF0YWJhc2VQYWdlc0lkfSA9IHVzZVdvcmtzcGFjZSgpXG4gICAgY29uc3QgZGIgPSBkYXRhYmFzZVBhZ2VTdG9yZVtkYXRhYmFzZS5pZF1cblxuICAgIGNvbnN0IGRhdGFiYXNlSWQgPSBkYXRhYmFzZS5pZFxuXG4gICAgY29uc3QgZGJJdGVtcyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgICAgICBjb25zdCBpdGVtczogVGFnSXRlbTx1bmRlZmluZWQ+W10gPSBbXVxuXG4gICAgICAgIGNvbnN0IGRhdGFiYXNlSWRzID0gWy4uLmRhdGFiYXNlUGFnZXNJZF1cbiAgICAgICAgaWYgKCFkYXRhYmFzZUlkcy5pbmNsdWRlcyhkYXRhYmFzZUlkKSkgZGF0YWJhc2VJZHMucHVzaChkYXRhYmFzZUlkKVxuXG4gICAgICAgIGZvciAoY29uc3QgaWQgb2YgZGF0YWJhc2VJZHMpIHtcbiAgICAgICAgICAgIGNvbnN0IGRiID0gZGF0YWJhc2VQYWdlU3RvcmVbaWRdXG4gICAgICAgICAgICBpZiAoIWRiKSBjb250aW51ZVxuICAgICAgICAgICAgY29uc3Qge3BhZ2V9ID0gZGJcbiAgICAgICAgICAgIGNvbnN0IGVtb2ppID0gcGFnZS5pY29uICYmIHBhZ2UuaWNvbi50eXBlID09PSBPYmplY3RUeXBlLkVtb2ppID8gcGFnZS5pY29uLmVtb2ppIDogJ/Cfk5UnXG5cbiAgICAgICAgICAgIGNvbnN0IGl0ZW06IFRhZ0l0ZW08dW5kZWZpbmVkPiA9IHtcbiAgICAgICAgICAgICAgICBjb2xvcjogdW5kZWZpbmVkLCBkYXRhOiB1bmRlZmluZWQsIGlkLCB0aXRsZTogYCR7ZW1vaml9ICR7ZGIucGFnZS5uYW1lfWAsIHZhbHVlOiBpZFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaXRlbXMucHVzaChpdGVtKVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpdGVtc1xuICAgIH0sIFtkYXRhYmFzZVBhZ2VTdG9yZSwgZGF0YWJhc2VQYWdlc0lkLCBkYXRhYmFzZUlkXSlcblxuICAgIGlmICghZGIpIHJldHVybiBudWxsO1xuXG4gICAgY29uc3Qge3BhZ2V9ID0gZGJcbiAgICBjb25zdCBlbW9qaSA9IHBhZ2UuaWNvbiAmJiBwYWdlLmljb24udHlwZSA9PT0gT2JqZWN0VHlwZS5FbW9qaSA/IHBhZ2UuaWNvbi5lbW9qaSA6ICfwn5OVJ1xuICAgIGNvbnN0IHRpdGxlID0gPD57ZW1vaml9ICZuYnNwOyB7ZGIucGFnZS5uYW1lfTwvPlxuICAgIHJldHVybiA8PlxuICAgICAgICA8RHJvcGRvd25NZW51PlxuICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgY2xhc3NOYW1lPXtjbihcInRleHQteHMgcm91bmRlZC1mdWxsIHAtMS41ICFweC0zIGgtYXV0byBnYXAtMiBqdXN0aWZ5LXN0YXJ0IG1heC13LTQ4IGl0ZW1zLWNlbnRlclwiKX0+XG4gICAgICAgICAgICAgICAgICAgIDxEYXRhYmFzZUljb24gY2xhc3NOYW1lPVwic2l6ZS0zXCIvPlxuICAgICAgICAgICAgICAgICAgICB7Lyo8c3BhbiBjbGFzc05hbWU9J3RleHQtbXV0ZWQtZm9yZWdyb3VuZCc+RnJvbTo8L3NwYW4+Ki99XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT0ndHJ1bmNhdGUgdGV4dC1ibGFjayBmb250LW1lZGl1bSc+e3RpdGxlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cbiAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDb250ZW50IGNsYXNzTmFtZT0ncC0wIHJvdW5kZWQtbm9uZSBtaW4tdy04MCcgYWxpZ249J3N0YXJ0Jz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ncC0yIHB0LTAnPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImRiLXNlbGVjdFwiIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1uZXV0cmFsLTUwMFwiPlNvdXJjZSBkYXRhYmFzZTwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxDdXN0b21TZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXt2ID0+IHtcblxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkXG4gICAgICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZElkcz17W2RhdGFiYXNlSWRdfVxuICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDaG9vc2UgYSBkYXRhYmFzZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtkYkl0ZW1zfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxuICAgICAgICA8L0Ryb3Bkb3duTWVudT5cblxuICAgIDwvPlxufVxuXG5pbnRlcmZhY2UgQWRkUmVjb3JkUG9wdXBQcm9wcyB7XG4gICAgdHJpZ2dlcj86IFJlYWN0LlJlYWN0Tm9kZVxuICAgIHJlY29yZElkOiBzdHJpbmdcbiAgICBkYXRhYmFzZUlkOiBzdHJpbmdcbiAgICBvbkNsb3NlPzogKCkgPT4gdm9pZFxuICAgIGFjdGlvbj86ICdhZGQnIHwgJ3BlZWsnIHwgJ2VkaXQnXG4gICAgY2FuRWRpdD86IGJvb2xlYW5cbn1cblxuY29uc3QgUGVla1JlY29yZCA9IChwcm9wczogQWRkUmVjb3JkUG9wdXBQcm9wcykgPT4ge1xuICAgIGNvbnN0IHtkYXRhYmFzZVN0b3JlLCBtZW1iZXJzLCB1cmx9ID0gdXNlV29ya3NwYWNlKClcbiAgICBjb25zdCB0ZW1wbGF0ZSA9IHVzZU1heWJlVGVtcGxhdGUoKVxuICAgIGNvbnN0IHNoYXJlZCA9IHVzZU1heWJlU2hhcmVkKClcblxuICAgIGNvbnN0IGNhbkV4cGFuZCA9ICFzaGFyZWQgJiYgIXRlbXBsYXRlXG5cbiAgICBjb25zdCBkYXRhYmFzZSA9IGRhdGFiYXNlU3RvcmVbcHJvcHMuZGF0YWJhc2VJZF1cbiAgICBjb25zdCByZWNvcmQgPSBkYXRhYmFzZVN0b3JlW3Byb3BzLmRhdGFiYXNlSWRdLnJlY29yZHNJZE1hcFtwcm9wcy5yZWNvcmRJZF1cbiAgICBjb25zdCBvbk9wZW5DaGFuZ2UgPSAobzogYm9vbGVhbikgPT4ge1xuICAgICAgICBpZiAoIW8pIHtcbiAgICAgICAgICAgIHByb3BzLm9uQ2xvc2U/LigpXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIWRhdGFiYXNlIHx8ICFyZWNvcmQpIHJldHVybiBudWxsXG5cbiAgICBjb25zdCByZWNvcmRJbmZvID0gcmVjb3JkXG5cbiAgICBsZXQgcHJvY2Vzc2VkUmVjb3JkOiBQcm9jZXNzZWREYlJlY29yZCB8IG51bGwgPSBudWxsO1xuXG4gICAgY29uc3QgcGVyc29ucyA9IG1lbWJlcnNUb1BlcnNvbnMobWVtYmVycylcblxuICAgIGNvbnN0IGxpbmtlZERhdGFiYXNlSWQgPSBPYmplY3QudmFsdWVzKGRhdGFiYXNlLmRhdGFiYXNlLmRlZmluaXRpb24uY29sdW1uc01hcClcbiAgICAgICAgLmZpbHRlcihjID0+IGMudHlwZSA9PT0gRGF0YWJhc2VGaWVsZERhdGFUeXBlLkxpbmtlZCAmJiBjLmRhdGFiYXNlSWQpXG4gICAgICAgIC5tYXAoYyA9PiAoYyBhcyBMaW5rZWRDb2x1bW4pLmRhdGFiYXNlSWQpXG5cbiAgICBjb25zdCBsaW5rZWREYXRhYmFzZXM6IExpbmtlZERhdGFiYXNlcyA9IHt9XG5cbiAgICBmb3IgKGNvbnN0IGlkIG9mIGxpbmtlZERhdGFiYXNlSWQpIHtcbiAgICAgICAgY29uc3QgZGIgPSBkYXRhYmFzZVN0b3JlW2lkXVxuICAgICAgICBpZiAoZGIpIHtcbiAgICAgICAgICAgIGxpbmtlZERhdGFiYXNlc1tpZF0gPSB7XG4gICAgICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICAgICAgZGVmaW5pdGlvbjogZGIuZGF0YWJhc2UuZGVmaW5pdGlvbixcbiAgICAgICAgICAgICAgICByZWNvcmRzTWFwOiB7fSxcbiAgICAgICAgICAgICAgICBzcmNQYWNrYWdlTmFtZTogZGIuZGF0YWJhc2Uuc3JjUGFja2FnZU5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZvciAobGV0IHIgb2YgT2JqZWN0LnZhbHVlcyhkYi5yZWNvcmRzSWRNYXApKSB7XG4gICAgICAgICAgICAgICAgbGlua2VkRGF0YWJhc2VzW2lkXS5yZWNvcmRzTWFwW3IucmVjb3JkLmlkXSA9IHIucmVjb3JkXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgcmVjb3JkcyA9IFtyZWNvcmRJbmZvLnJlY29yZF1cbiAgICBjb25zdCBwcm9jZXNzZWRSZWNvcmRzID0gdHJhbnNmb3JtUmF3UmVjb3JkcyhcbiAgICAgICAgZGF0YWJhc2UuZGF0YWJhc2UuZGVmaW5pdGlvbixcbiAgICAgICAgcmVjb3JkcyxcbiAgICAgICAgcGVyc29ucyxcbiAgICAgICAgbGlua2VkRGF0YWJhc2VzXG4gICAgKVxuICAgIHByb2Nlc3NlZFJlY29yZCA9IHByb2Nlc3NlZFJlY29yZHNbMF1cblxuICAgIGxldCBocmVmID0gdXJsKGAvZGF0YWJhc2VzLyR7cmVjb3JkLnJlY29yZC5kYXRhYmFzZUlkfS9yZWNvcmRzLyR7cmVjb3JkLnJlY29yZC5pZH1gKVxuXG5cbiAgICByZXR1cm4gPD5cbiAgICAgICAgPFNoZWV0IGRlZmF1bHRPcGVuPXt0cnVlfSBvbk9wZW5DaGFuZ2U9e29uT3BlbkNoYW5nZX0+XG4gICAgICAgICAgICA8U2hlZXRUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAge3Byb3BzLnRyaWdnZXJ9XG4gICAgICAgICAgICA8L1NoZWV0VHJpZ2dlcj5cbiAgICAgICAgICAgIDxTaGVldENvbnRlbnQgY2xhc3NOYW1lPSchdy1bNTB2d10gIW1pbi13LVs0MDBweF0gIW1heC13LWZ1bGwgYmctd2hpdGUgcC0wIHB0LTgnPlxuICAgICAgICAgICAgICAgIHtjYW5FeHBhbmQgJiYgPEJ1dHRvbiB2YXJpYW50PSdnaG9zdCcgYXNDaGlsZCBjbGFzc05hbWU9J2Fic29sdXRlIHJpZ2h0LTEyIHRvcC0yLjUgIXNpemUtNiAhcC0xLjUgcm91bmRlZC1mdWxsJz5cbiAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj17aHJlZn0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QXJyb3dVcFJpZ2h0QW5kQXJyb3dEb3duTGVmdEZyb21DZW50ZXJJY29uIGNsYXNzTmFtZT0nc2l6ZS1mdWxsJy8+XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj59XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3NpemUtZnVsbCBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlbic+XG4gICAgICAgICAgICAgICAgICAgIDxTaGVldEhlYWRlciBjbGFzc05hbWU9J2hpZGRlbic+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2hlZXRUaXRsZSBjbGFzc05hbWU9J2ZvbnQtYm9sZCB0ZXh0LWJhc2UnPlBlZWsgUmVjb3JkPC9TaGVldFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoZWV0RGVzY3JpcHRpb24gY2xhc3NOYW1lPSdoaWRkZW4nPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIE1ha2UgY2hhbmdlcyB0byB5b3VyIHJlY29yZCBoZXJlLiBDbGljayBzYXZlIHdoZW4geW91J3JlIGRvbmUuXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NoZWV0RGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvU2hlZXRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuJz5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKjxTY3JvbGxBcmVhIGNsYXNzTmFtZT0nc2Nyb2xsQmxvY2tDaGlsZCc+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAvISo8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTQgcHktNFwiPiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKiAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTQgaXRlbXMtY2VudGVyIGdhcC00XCI+KiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5hbWVcIiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+KiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgICAgICAgICAgTmFtZSohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKiAgICAgICAgPC9MYWJlbD4qIS8qL31cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiAgICAvISogICAgICAgIDxJbnB1dCBpZD1cIm5hbWVcIiB2YWx1ZT1cIlBlZHJvIER1YXJ0ZVwiIGNsYXNzTmFtZT1cImNvbC1zcGFuLTNcIi8+KiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgIDwvZGl2PiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKiAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTQgaXRlbXMtY2VudGVyIGdhcC00XCI+KiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInVzZXJuYW1lXCIgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKiAgICAgICAgICAgIFVzZXJuYW1lKiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgICAgICA8L0xhYmVsPiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKiAgICAgICAgPElucHV0IGlkPVwidXNlcm5hbWVcIiB2YWx1ZT1cIkBwZWR1YXJ0ZVwiIGNsYXNzTmFtZT1cImNvbC1zcGFuLTNcIi8+KiEvKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogICAgLyEqICAgIDwvZGl2PiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qICAgIC8hKjwvZGl2PiohLyovfVxuICAgICAgICAgICAgICAgICAgICAgICAgey8qPC9TY3JvbGxBcmVhPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPFJlY29yZFRhYlZpZXdzUHJvdmlkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJlY29yZFByb3ZpZGVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlY29yZEluZm89e3suLi5yZWNvcmQsIHByb2Nlc3NlZFJlY29yZH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFiYXNlPXtkYXRhYmFzZS5kYXRhYmFzZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKjxSZWNvcmRPdmVydmlldyBpc1RhYmJlZC8+Ki99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSZWNvcmRFeHRyYXMgc2hvd092ZXJ2aWV3Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qPFJlY29yZERldGFpbCAvPiovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvUmVjb3JkUHJvdmlkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1JlY29yZFRhYlZpZXdzUHJvdmlkZXI+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8U2hlZXRGb290ZXIgY2xhc3NOYW1lPSdoaWRkZW4nPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoZWV0Q2xvc2UgYXNDaGlsZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBjbGFzc05hbWU9J3JvdW5kZWQtZnVsbCc+U2F2ZTwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TaGVldENsb3NlPlxuICAgICAgICAgICAgICAgICAgICA8L1NoZWV0Rm9vdGVyPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8L1NoZWV0Q29udGVudD5cbiAgICAgICAgPC9TaGVldD5cbiAgICA8Lz5cbn1cblxuaW50ZXJmYWNlIFNjYW5uYWJsZUNvZGVTY2FubmVyUHJvcHMge1xuICAgIGRhdGFiYXNlOiBEYXRhYmFzZTtcbiAgICB2aWV3RmlsdGVyOiBEYlJlY29yZEZpbHRlcjtcbiAgICBmaWx0ZXI6IERiUmVjb3JkRmlsdGVyXG4gICAgb25SZWNvcmRTY2FuOiAoaWQ6IHN0cmluZykgPT4gdm9pZFxufVxuXG5jb25zdCBTY2FubmFibGVDb2RlU2Nhbm5lciA9ICh7ZGF0YWJhc2UsIGZpbHRlciwgdmlld0ZpbHRlciwgb25SZWNvcmRTY2FufTogU2Nhbm5hYmxlQ29kZVNjYW5uZXJQcm9wcykgPT4ge1xuICAgIGNvbnN0IHt0b2FzdH0gPSB1c2VBbGVydCgpXG4gICAgY29uc3Qge2RhdGFiYXNlU3RvcmUsIG1lbWJlcnMsIHdvcmtzcGFjZX0gPSB1c2VXb3Jrc3BhY2UoKVxuICAgIGNvbnN0IGRhdGFiYXNlU3RvcmVJdGVtID0gZGF0YWJhc2VTdG9yZVtkYXRhYmFzZS5pZF1cblxuXG4gICAgbGV0IGhhc1NjYW5uYWJsZUZpZWxkID0gZmFsc2VcbiAgICBmb3IgKGxldCB2YWx1ZSBvZiBPYmplY3QudmFsdWVzKGRhdGFiYXNlLmRlZmluaXRpb24uY29sdW1uc01hcCkpIHtcbiAgICAgICAgaWYgKHZhbHVlLnR5cGUgPT09IERhdGFiYXNlRmllbGREYXRhVHlwZS5TY2FubmFibGVDb2RlKSB7XG4gICAgICAgICAgICBoYXNTY2FubmFibGVGaWVsZCA9IHRydWVcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICB9XG5cbiAgICBpZiAoIWhhc1NjYW5uYWJsZUZpZWxkKSByZXR1cm4gbnVsbFxuICAgIGNvbnN0IG9uU2NhbiA9IChyZXN1bHRzOiBJRGV0ZWN0ZWRCYXJjb2RlW10pID0+IHtcbiAgICAgICAgY29uc3QgZGV0ZWN0ZWQgPSByZXN1bHRzWzBdXG4gICAgICAgIGlmICghZGV0ZWN0ZWQpIHtcbiAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiTm90aGluZyB0byBzZWFyY2hcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgcmF3VmFsdWUgPSBkZXRlY3RlZC5yYXdWYWx1ZVxuICAgICAgICBpZiAoIXJhd1ZhbHVlKSB7XG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIk5vdGhpbmcgdG8gc2VhcmNoXCIpXG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHtyb3dzfSA9IGZpbHRlckFuZFNvcnRSZWNvcmRzKFxuICAgICAgICAgICAgZGF0YWJhc2VTdG9yZUl0ZW0sXG4gICAgICAgICAgICBtZW1iZXJzLFxuICAgICAgICAgICAgZGF0YWJhc2VTdG9yZSxcbiAgICAgICAgICAgIHZpZXdGaWx0ZXIsXG4gICAgICAgICAgICBmaWx0ZXIsXG4gICAgICAgICAgICBbXSxcbiAgICAgICAgICAgIHdvcmtzcGFjZS53b3Jrc3BhY2VNZW1iZXIudXNlcklkXG4gICAgICAgIClcbiAgICAgICAgY29uc29sZS5sb2coXCJTY2FubmVkIHZhbHVlOiBcIiwge3Jhd1ZhbHVlLCByb3dzfSlcbiAgICAgICAgY29uc3Qgc2VhcmNoZWQgPSBzZWFyY2hGaWx0ZXJlZFJlY29yZHMocmF3VmFsdWUsIHJvd3MpXG4gICAgICAgIGlmICghc2VhcmNoZWQgfHwgc2VhcmNoZWQubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICB0b2FzdC5lcnJvcihcIk5vIHJlY29yZHMgZm91bmRcIilcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIG9uUmVjb3JkU2NhbihzZWFyY2hlZFswXS5yZWNvcmQuaWQpXG4gICAgfVxuXG4gICAgcmV0dXJuIDw+XG4gICAgICAgIDxQb3BvdmVyPlxuICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyByb3VuZGVkLWZ1bGwgcC0xIHNpemUtNyBnYXAtMiBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJhcmNvZGVSZWFkSWNvbiBjbGFzc05hbWU9XCJzaXplLTNcIi8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L1BvcG92ZXJUcmlnZ2VyPlxuICAgICAgICAgICAgPFBvcG92ZXJDb250ZW50IGNsYXNzTmFtZT1cInctODAgcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT0nbWItNCB0ZXh0LXhzIGZvbnQtbWVkaXVtJz5GaW5kIHJlY29yZCBieSBzY2FubmluZyBiYXJjb2RlL1FSIGNvZGU8L2g0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxTY2FubmVyIGFsbG93TXVsdGlwbGUgb25TY2FuPXtvblNjYW59Lz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvUG9wb3ZlckNvbnRlbnQ+XG4gICAgICAgIDwvUG9wb3Zlcj5cbiAgICA8Lz5cbn1cblxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlUmVmIiwidXNlU3RhdGUiLCJFbnZlbG9wZUljb24iLCJNYWduaWZ5aW5nR2xhc3NDaXJjbGVJY29uIiwiU3BhcmtsZXNJY29uIiwiSW5wdXQiLCJMYWJlbCIsIkFycm93VXBGcm9tQXJjSWNvbiIsIkFycm93VXBSaWdodEFuZEFycm93RG93bkxlZnRGcm9tQ2VudGVySWNvbiIsIkFycm93VXBXaWRlU2hvcnRJY29uIiwiQmFyY29kZVJlYWRJY29uIiwiQ2lyY2xlUGx1c0ljb24iLCJEYXRhYmFzZUljb24iLCJGaWx0ZXJMaXN0SWNvbiIsIlNxdWFyZVBlbkljb24iLCJUcmFzaExpc3RJY29uIiwidXNlUGFnZSIsIkFjY2Vzc0xldmVsIiwiUGFnZUxvYWRlciIsInVzZVdvcmtzcGFjZSIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsIlZpZXdUeXBlIiwiZ2V0Vmlld1B1YmxpY1VybCIsInVzZUFsZXJ0IiwiY29weVRvQ2xpcGJvYXJkIiwidXNlVmlld3MiLCJ1c2VWaWV3RmlsdGVyaW5nIiwidXNlVmlld1NlbGVjdGlvbiIsIkNvbHVtbnNSZW9yZGVyIiwiVmlld0NyZWF0b3IiLCJWaWV3U3dpdGNoZXIiLCJWaWV3RmlsdGVyIiwiVmlld1NvcnQiLCJEYXNoYm9hcmRWaWV3TW9yZU9wdGlvbnMiLCJEb2NWaWV3TW9yZU9wdGlvbnMiLCJGb3JtVmlld01vcmVPcHRpb25zIiwiVmlld01vcmVPcHRpb25zIiwiU3VtbWFyeUNvbHVtbkdyb3VwQnkiLCJTdW1tYXJ5Q29sdW1uc1Jlb3JkZXIiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsImNuIiwiT2JqZWN0VHlwZSIsIkN1c3RvbVNlbGVjdCIsIlNlbmRFbWFpbFdyYXBwZXJGb3JWaWV3IiwiVXBkYXRlUmVjb3JkcyIsIkV2ZW50VHlwZSIsInB1c2hFdmVudCIsInVzZUF1dGgiLCJTaGFyZVZpZXciLCJTaGVldCIsIlNoZWV0Q2xvc2UiLCJTaGVldENvbnRlbnQiLCJTaGVldERlc2NyaXB0aW9uIiwiU2hlZXRGb290ZXIiLCJTaGVldEhlYWRlciIsIlNoZWV0VGl0bGUiLCJTaGVldFRyaWdnZXIiLCJEYXRhYmFzZUZpZWxkRGF0YVR5cGUiLCJNYXRjaCIsIlJlY29yZFByb3ZpZGVyIiwibWVtYmVyc1RvUGVyc29ucyIsInRyYW5zZm9ybVJhd1JlY29yZHMiLCJ1c2VNYXliZVRlbXBsYXRlIiwiTGluayIsInVzZU1heWJlU2hhcmVkIiwiUmVjb3JkRXh0cmFzIiwiU2Nhbm5lciIsIlBvcG92ZXIiLCJQb3BvdmVyQ29udGVudCIsIlBvcG92ZXJUcmlnZ2VyIiwiZmlsdGVyQW5kU29ydFJlY29yZHMiLCJzZWFyY2hGaWx0ZXJlZFJlY29yZHMiLCJ1c2VNYXliZVJlY29yZCIsIlJlY29yZFRhYlZpZXdzUHJvdmlkZXIiLCJBZGRSZWNvcmRNb2RhbCIsInVzZVN0YWNrZWRQZWVrIiwiVmlld0NvbnRleHQiLCJjb250ZXh0IiwidXNlVmlld0NvbnRleHQiLCJWaWV3c1Jvb3RMYXlvdXQiLCJwcm9wcyIsIm1heWJlUmVjb3JkIiwidXJsIiwidG9rZW4iLCJzZWFyY2hQYXJhbXMiLCJkb2N1bWVudElkIiwiZ2V0Iiwicm91dGVyIiwidmlld3NNYXAiLCJhY2Nlc3NMZXZlbCIsInBhZ2UiLCJuZXdWaWV3Iiwic2V0TmV3VmlldyIsImNyZWF0ZVJlY29yZHMiLCJkZWxldGVSZWNvcmRzIiwic21hcnRVcGRhdGVWaWV3RGVmaW5pdGlvbiIsInBlZWtSZWNvcmRJZCIsInNldFBlZWtSZWNvcmRJZCIsImZpbHRlciIsInNvcnRzIiwic2VhcmNoIiwic2V0RmlsdGVyIiwic2V0U29ydHMiLCJzZXRTZWFyY2giLCJzZWxlY3RlZElkcyIsInNldFNlbGVjdGVkSWRzIiwiZGF0YWJhc2VTdG9yZSIsIndvcmtzcGFjZSIsInRvYXN0IiwibWF5YmVUZW1wbGF0ZSIsIm9wZW5SZWNvcmQiLCJzaG93QWRkTW9kYWwiLCJzZXRTaG93QWRkTW9kYWwiLCJ2aWV3IiwiZGF0YWJhc2UiLCJwYXJlbnRJZCIsIm1ldGEiLCJyZWNvcmRWaWV3c01hcCIsInZpZXdJZCIsInZpZXdUeXBlIiwidHlwZSIsImNvbnRleHRBd2FyZVVwZGF0ZVZpZXdEZWZpbml0aW9uIiwidXBkYXRlIiwiaXNSZWNvcmRUYWIiLCJkYXRhYmFzZUlkIiwidW5kZWZpbmVkIiwiaWQiLCJwYWdlSWQiLCJjYW5Db250YWN0IiwiY2FuRWRpdCIsIkZ1bGwiLCJFZGl0IiwiaW5jbHVkZXMiLCJoYXNGdWxsQWNjZXNzIiwidmlld0ZpbHRlciIsImNvbmRpdGlvbnMiLCJtYXRjaCIsIkFsbCIsIkJvYXJkIiwiVGFibGUiLCJkZWZpbml0aW9uIiwiaGFuZGxlQWRkUmVjb3JkIiwiaGFuZGxlRGlyZWN0QWRkUmVjb3JkIiwiclMiLCJzaG91bGRQZWVrIiwidHJpbSIsImxlbmd0aCIsInJlY29yZHMiLCJoYW5kbGVSZWNvcmRDcmVhdGVkIiwicmVjb3JkSWQiLCJkZWxldGVTZWxlY3RlZCIsImNvcHlTaGFyZWRVcmwiLCJuYW1lIiwic3VjY2VzcyIsImRiSWQiLCJTdW1tYXJ5VGFibGUiLCJGb3JtIiwiQ2FsZW5kYXIiLCJMaXN0VmlldyIsImN1cnJlbnRWaWV3SWRSZWYiLCJ3b3Jrc3BhY2VJZCIsInZpZXdFeGlzdHMiLCJ0aW1lb3V0Iiwic2V0VGltZW91dCIsImV2ZW50IiwiVmlldyIsImNsZWFyVGltZW91dCIsImN1cnJlbnQiLCJQcm92aWRlciIsInZhbHVlIiwic2l6ZSIsImVycm9yIiwiY3RhIiwibGFiZWwiLCJvbkNsaWNrIiwicmVwbGFjZSIsImRpdiIsImNsYXNzTmFtZSIsImNyZWF0YWJsZSIsImVkaXRhYmxlIiwiZGVsZXRhYmxlIiwiY2xvbmVhYmxlIiwicmVxdWVzdE5ld1ZpZXciLCJvcGVuIiwic2V0T3BlbiIsImRvbWFpbiIsInRyaWdnZXJBbGlnbiIsInRyaWdnZXIiLCJ2YXJpYW50IiwiaXNQdWJsaXNoZWQiLCJQYWdlQ29udGV4dFNvdXJjZURhdGFiYXNlIiwibG9ja0NvbnRlbnQiLCJzcGFuIiwiaWRzIiwib25VcGRhdGUiLCJvbkNoYW5nZSIsImN1cnJlbnRSZWNvcmRJZCIsInJlY29yZEluZm8iLCJyZWNvcmQiLCJjdXJyZW50UmVjb3JkRGF0YWJhc2VJZCIsImZvcm0iLCJwbGFjZWhvbGRlciIsImUiLCJ0YXJnZXQiLCJTY2FubmFibGVDb2RlU2Nhbm5lciIsIm9uUmVjb3JkU2NhbiIsIm9uRGVmaW5pdGlvblVwZGF0ZSIsInUiLCJkaXNhYmxlZCIsIkRhc2hib2FyZCIsIkRvY3VtZW50IiwiY2hpbGRyZW4iLCJQZWVrUmVjb3JkIiwib25DbG9zZSIsImNvbnRleHR1YWxGaWx0ZXIiLCJvblJlY29yZENyZWF0ZWQiLCJkYXRhYmFzZVBhZ2VTdG9yZSIsImRhdGFiYXNlUGFnZXNJZCIsImRiIiwiZGJJdGVtcyIsIml0ZW1zIiwiZGF0YWJhc2VJZHMiLCJwdXNoIiwiZW1vamkiLCJpY29uIiwiRW1vamkiLCJpdGVtIiwiY29sb3IiLCJkYXRhIiwidGl0bGUiLCJhc0NoaWxkIiwiYWxpZ24iLCJodG1sRm9yIiwidiIsIm9wdGlvbnMiLCJtZW1iZXJzIiwidGVtcGxhdGUiLCJzaGFyZWQiLCJjYW5FeHBhbmQiLCJyZWNvcmRzSWRNYXAiLCJvbk9wZW5DaGFuZ2UiLCJvIiwicHJvY2Vzc2VkUmVjb3JkIiwicGVyc29ucyIsImxpbmtlZERhdGFiYXNlSWQiLCJPYmplY3QiLCJ2YWx1ZXMiLCJjb2x1bW5zTWFwIiwiYyIsIkxpbmtlZCIsIm1hcCIsImxpbmtlZERhdGFiYXNlcyIsInJlY29yZHNNYXAiLCJzcmNQYWNrYWdlTmFtZSIsInIiLCJwcm9jZXNzZWRSZWNvcmRzIiwiaHJlZiIsImRlZmF1bHRPcGVuIiwic2hvd092ZXJ2aWV3IiwiZGF0YWJhc2VTdG9yZUl0ZW0iLCJoYXNTY2FubmFibGVGaWVsZCIsIlNjYW5uYWJsZUNvZGUiLCJvblNjYW4iLCJyZXN1bHRzIiwiZGV0ZWN0ZWQiLCJyYXdWYWx1ZSIsInJvd3MiLCJ3b3Jrc3BhY2VNZW1iZXIiLCJ1c2VySWQiLCJjb25zb2xlIiwibG9nIiwic2VhcmNoZWQiLCJoNCIsImFsbG93TXVsdGlwbGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/ViewsRootLayout.tsx\n"));

/***/ })

});