"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Calculate position immediately to prevent flash\n                if (divRef.current) {\n                    const inputRect = divRef.current.getBoundingClientRect();\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250;\n                    const minSpaceRequired = 100;\n                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                    setShowAbove(shouldShowAbove);\n                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                    setPopupPosition({\n                        left: inputRect.left + scrollX,\n                        top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                    });\n                }\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Calculate absolute position for portal-based dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                // Determine if we should show above or below\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                // Calculate absolute position for portal\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 588,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 612,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 604,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 587,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"C4RIgA4damfizFcXrLqcY529xaE=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});