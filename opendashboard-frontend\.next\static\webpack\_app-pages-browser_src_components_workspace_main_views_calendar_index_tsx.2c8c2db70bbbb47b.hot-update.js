"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx":
/*!*********************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/views/week.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeekView: function() { return /* binding */ WeekView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfWeek,format,isToday,setHours,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_allday__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/allday */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/allday.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_noevents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/noevents */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/noevents.tsx\");\n/* harmony import */ var _utils_multiDay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/multiDay */ \"(app-pages-browser)/./src/utils/multiDay.ts\");\n/* harmony import */ var _utils_eventCollision__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/eventCollision */ \"(app-pages-browser)/./src/utils/eventCollision.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TimeSlot = (param)=>{\n    let { day, hour, children, onDoubleClick, isDragging } = param;\n    _s();\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentMinute, setCurrentMinute] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_1___default().useRef(null);\n    const shouldShowPrecision = isHovering || isDragging;\n    const calculateMinuteFromPosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((clientY)=>{\n        if (!containerRef.current) return 0;\n        const rect = containerRef.current.getBoundingClientRect();\n        const y = clientY - rect.top;\n        const minute = Math.floor(y / rect.height * 60);\n        return Math.max(0, Math.min(59, minute));\n    }, []);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsHovering(true);\n    }, []);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!isDragging) {\n            setIsHovering(false);\n            setMousePosition(null);\n            setCurrentMinute(0);\n        }\n    }, [\n        isDragging\n    ]);\n    const handleMouseMove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const minute = calculateMinuteFromPosition(e.clientY);\n        setMousePosition({\n            x: e.clientX,\n            y: e.clientY\n        });\n        setCurrentMinute(minute);\n    }, [\n        calculateMinuteFromPosition\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (!isDragging) return;\n        const handleGlobalMouseMove = (e)=>{\n            if (containerRef.current) {\n                const rect = containerRef.current.getBoundingClientRect();\n                if (e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom) {\n                    const minute = calculateMinuteFromPosition(e.clientY);\n                    setMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                    setCurrentMinute(minute);\n                    setIsHovering(true);\n                }\n            }\n        };\n        document.addEventListener(\"mousemove\", handleGlobalMouseMove);\n        return ()=>document.removeEventListener(\"mousemove\", handleGlobalMouseMove);\n    }, [\n        isDragging,\n        calculateMinuteFromPosition\n    ]);\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.detail === 2) {\n            onDoubleClick(currentMinute);\n        }\n    }, [\n        currentMinute,\n        onDoubleClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer\",\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        onMouseMove: handleMouseMove,\n        onClick: handleClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HourDropZone, {\n                day: day,\n                hour: hour,\n                currentMinute: currentMinute,\n                isActive: shouldShowPrecision\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TimeSlot, \"Q86ozqSDjuDy8UM9ZVCfFDNUfYE=\");\n_c = TimeSlot;\nconst HourDropZone = (param)=>{\n    let { day, hour, currentMinute, isActive } = param;\n    _s1();\n    const { setNodeRef, isOver } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable)({\n        id: \"hour-\".concat((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"yyyy-MM-dd\"), \"-\").concat(hour),\n        data: {\n            date: day,\n            hour,\n            minute: currentMinute,\n            type: \"timeslot-minute\"\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: setNodeRef,\n        className: \"absolute inset-0 w-full h-full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(HourDropZone, \"DmJTTt6A5xWIX/faBiFge3FOLrw=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_8__.useDroppable\n    ];\n});\n_c1 = HourDropZone;\nconst WeekView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, savedScrollTop, handleEventClick, activeDragData } = param;\n    _s2();\n    const weekCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const weekStart = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const weekEnd = (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        });\n        const days = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(weekStart, i));\n        const todayIndex = days.findIndex((day)=>(0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day));\n        return {\n            weekStart,\n            weekEnd,\n            days,\n            todayIndex\n        };\n    }, [\n        selectedDate\n    ]);\n    const { days, todayIndex } = weekCalculations;\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const weekSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const allSegments = (0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.eventsToSegments)(events);\n        return (0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForWeek)(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);\n    }, [\n        events,\n        weekCalculations.weekStart,\n        weekCalculations.weekEnd\n    ]);\n    const allDaySegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getAllDaySegments)(weekSegments), [\n        weekSegments\n    ]);\n    const timeSlotSegments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getTimeSlotSegments)(weekSegments), [\n        weekSegments\n    ]);\n    const currentTimePosition = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>todayIndex !== -1 ? {\n            dayIndex: todayIndex,\n            hour: new Date().getHours(),\n            minutes: new Date().getMinutes()\n        } : null, [\n        todayIndex\n    ]);\n    const getEventDurationInMinutes = (event)=>{\n        const start = new Date(event.start);\n        const end = new Date(event.end);\n        return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));\n    };\n    const renderEmptyState = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_noevents__WEBPACK_IMPORTED_MODULE_5__.NoEvents, {\n            title: \"No events this week\",\n            message: \"Your week is completely free. Add some events to get organized!\",\n            showCreateButton: canEditData,\n            onCreate: ()=>openAddEventForm(selectedDate)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n            lineNumber: 204,\n            columnNumber: 5\n        }, undefined);\n    const renderTimeSlots = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto\",\n            id: \"week-view-container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-x-auto lg:overflow-x-visible\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col min-w-[700px] lg:min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors\", i === hours.length - 1 && \"border-b-neutral-300\"),\n                                    style: {\n                                        height: \"60px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            \"data-time-labels\": \"true\",\n                                            className: \"sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 bg-white z-20 w-14 lg:w-20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs font-semibold\",\n                                                    children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])((0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(new Date(), hour), \"h a\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        days.map((day)=>{\n                                            const daySegments = (0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getSegmentsForDay)(timeSlotSegments, day);\n                                            const { segmentLayouts } = (0,_utils_eventCollision__WEBPACK_IMPORTED_MODULE_7__.calculateLayout)(daySegments);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                                day: day,\n                                                hour: hour,\n                                                isDragging: !!activeDragData,\n                                                onDoubleClick: (minute)=>{\n                                                    if (canEditData) {\n                                                        const newDate = new Date(day);\n                                                        newDate.setHours(hour, minute, 0, 0);\n                                                        openAddEventForm(newDate);\n                                                    }\n                                                },\n                                                children: segmentLayouts.map((layout)=>{\n                                                    var _activeDragData_payload, _activeDragData_payload1;\n                                                    const segmentStart = layout.segment.startTime;\n                                                    const isFirstHour = segmentStart.getHours() === hour;\n                                                    if (!isFirstHour) return null;\n                                                    const segmentHeight = (0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getSegmentHeight)(layout.segment);\n                                                    const topOffset = (0,_utils_multiDay__WEBPACK_IMPORTED_MODULE_6__.getSegmentTopOffset)(layout.segment);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                        segment: layout.segment,\n                                                        style: {\n                                                            height: \"\".concat(segmentHeight, \"px\"),\n                                                            position: \"absolute\",\n                                                            top: \"\".concat(topOffset, \"px\"),\n                                                            left: \"\".concat(layout.left, \"%\"),\n                                                            width: \"\".concat(layout.width, \"%\"),\n                                                            zIndex: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === layout.segment.id ? 50 : layout.zIndex,\n                                                            paddingRight: \"2px\",\n                                                            border: layout.hasOverlap ? \"1px solid white\" : \"none\"\n                                                        },\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            const container = document.getElementById(\"week-view-container\");\n                                                            if (container) {\n                                                                savedScrollTop.current = container.scrollTop;\n                                                            }\n                                                            setSelectedEvent(layout.segment.originalEventId);\n                                                            handleEventClick(layout.segment.originalEvent);\n                                                        },\n                                                        view: \"week\",\n                                                        isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload1 = activeDragData.payload) === null || _activeDragData_payload1 === void 0 ? void 0 : _activeDragData_payload1.id) === layout.segment.id\n                                                    }, layout.segment.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 27\n                                                    }, undefined);\n                                                })\n                                            }, \"\".concat(day.toISOString(), \"-\").concat(hour), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, hour, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-full w-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute flex items-center\",\n                                    style: {\n                                        top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\"),\n                                        left: \"\".concat(currentTimePosition.dayIndex / 7 * 100, \"%\"),\n                                        width: \"\".concat(1 / 7 * 100, \"%\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n            lineNumber: 213,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-day-headers\": \"true\",\n                className: \"border-b border-neutral-300 bg-white sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sticky left-0 bg-white z-10 w-14 lg:w-20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0\",\n                            children: days.map((day, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center cursor-pointer py-3 px-0 lg:py-4\",\n                                    onClick: ()=>setSelectedDate(day),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold text-black mb-1\", \"text-xs\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"EEE\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full\", (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(day) ? \"bg-black text-white\" : \"text-black hover:bg-neutral-100\"),\n                                            children: (0,_barrel_optimize_names_addDays_endOfWeek_format_isToday_setHours_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, \"d\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_allday__WEBPACK_IMPORTED_MODULE_4__.AllDayRow, {\n                selectedDate: selectedDate,\n                segments: allDaySegments,\n                selectedEvent: selectedEvent,\n                setSelectedEvent: setSelectedEvent,\n                handleEventClick: handleEventClick,\n                canEditData: canEditData,\n                openAddEventForm: openAddEventForm,\n                view: \"week\",\n                activeDragData: activeDragData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, undefined),\n            weekSegments.length === 0 ? renderEmptyState() : renderTimeSlots()\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\views\\\\week.tsx\",\n        lineNumber: 322,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(WeekView, \"OfgQ1j/ZHWAQ0Q+mdykk0ntfinw=\");\n_c2 = WeekView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"TimeSlot\");\n$RefreshReg$(_c1, \"HourDropZone\");\n$RefreshReg$(_c2, \"WeekView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx\n"));

/***/ })

});