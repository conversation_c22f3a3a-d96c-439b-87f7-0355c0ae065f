"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_board_board_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    const [positionCalculated, setPositionCalculated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // ensure position is calculated before showing\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Calculate position immediately to prevent flash\n                if (divRef.current) {\n                    const inputRect = divRef.current.getBoundingClientRect();\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250;\n                    const minSpaceRequired = 100;\n                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                    setShowAbove(shouldShowAbove);\n                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                    setPopupPosition({\n                        left: inputRect.left + scrollX,\n                        top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                    });\n                    // Mark position as calculated\n                    setPositionCalculated(true);\n                }\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Update position on window resize or scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            // Only listen for resize and scroll events, don't run immediately\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 588,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 614,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 624,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 612,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 604,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 587,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"LwTjOlhT2DVbFeCb0bHxaVUm1S0=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});