"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _views_day__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./views/day */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/day.tsx\");\n/* harmony import */ var _views_week__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./views/week */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx\");\n/* harmony import */ var _views_month__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./views/month */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/month.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventitem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventitem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/utils/dragconstraints */ \"(app-pages-browser)/./src/utils/dragconstraints.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisHorizontalIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisHorizontalIcon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords, smartUpdateViewDefinition } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (prevPeekRecordId && !peekRecordId) {\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 126,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            // Calculate isMultiDay and isAllDay properties\n            const isMultiDay = !(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(startDate, endDate);\n            const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);\n            const isAllDay = durationHours >= 23 || isMultiDay && durationHours >= 22;\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord,\n                isMultiDay,\n                isAllDay\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to update event.\");\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_38__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_39__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_40__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to delete event\");\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_28__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_28__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 hover:bg-neutral-300 text-black\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisHorizontalIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_28__.DropdownMenuContent, {\n                                                className: \"w-56 rounded-none py-2 flex flex-col gap-1\",\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_29__.Label, {\n                                                    className: \"text-xs rounded-none p-1.5 !px-3 flex gap-2 font-medium items-center hover:bg-accent cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.LockIcon, {\n                                                            className: \"size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 18\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-1 capitalize\",\n                                                            children: \"Lock content\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 18\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_30__.Switch, {\n                                                            className: \"h-4 w-8\",\n                                                            checked: !!definition.lockContent,\n                                                            onCheckedChange: (lockContent)=>{\n                                                                // Update the view definition with lock content using the proper method\n                                                                smartUpdateViewDefinition(props.view.id, props.view.pageId, {\n                                                                    lockContent\n                                                                });\n                                                            },\n                                                            thumbClassName: \"!size-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 18\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 421,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_25__.restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_day__WEBPACK_IMPORTED_MODULE_18__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_week__WEBPACK_IMPORTED_MODULE_19__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_month__WEBPACK_IMPORTED_MODULE_20__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_21__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 644,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"OY6vt8MasBgLreSUt37ABgeJY50=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        usePrevious,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});