"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/views/layout",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Compute caret position relative to viewport.\n                let rect = currentRange.getBoundingClientRect();\n                // If the rectangle is all zeros, create a temporary marker to compute correct coordinates.\n                if (rect.width === 0 && rect.height === 0) {\n                    var _marker_parentNode;\n                    const marker = document.createElement(\"span\");\n                    marker.textContent = \"​\"; // zero width space\n                    currentRange.insertNode(marker);\n                    rect = marker.getBoundingClientRect();\n                    (_marker_parentNode = marker.parentNode) === null || _marker_parentNode === void 0 ? void 0 : _marker_parentNode.removeChild(marker);\n                    sel.removeAllRanges();\n                    sel.addRange(currentRange);\n                }\n            // Position will be calculated in useEffect to ensure proper positioning\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Calculate absolute position for portal-based dropdown\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                // Determine if we should show above or below\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                // Calculate absolute position for portal\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 580,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 604,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 596,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 645,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 642,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 579,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"C4RIgA4damfizFcXrLqcY529xaE=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});