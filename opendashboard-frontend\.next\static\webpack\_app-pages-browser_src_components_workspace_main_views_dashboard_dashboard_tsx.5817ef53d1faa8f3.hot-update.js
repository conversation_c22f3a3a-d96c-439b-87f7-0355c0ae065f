"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_dashboard_dashboard_tsx",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Compute caret position relative to viewport.\n                let rect = currentRange.getBoundingClientRect();\n                // If the rectangle is all zeros, create a temporary marker to compute correct coordinates.\n                if (rect.width === 0 && rect.height === 0) {\n                    var _marker_parentNode;\n                    const marker = document.createElement(\"span\");\n                    marker.textContent = \"​\"; // zero width space\n                    currentRange.insertNode(marker);\n                    rect = marker.getBoundingClientRect();\n                    (_marker_parentNode = marker.parentNode) === null || _marker_parentNode === void 0 ? void 0 : _marker_parentNode.removeChild(marker);\n                    sel.removeAllRanges();\n                    sel.addRange(currentRange);\n                }\n            // Position will be calculated in useEffect to ensure proper positioning\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Smart positioning: check if dropdown should appear above or below cursor position\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                // Get the input element's position as the primary reference\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                // Use input bottom as the reference point for positioning\n                const referenceBottom = inputRect.bottom;\n                const referenceTop = inputRect.top;\n                const spaceBelow = window.innerHeight - referenceBottom;\n                const spaceAbove = referenceTop;\n                const dropdownHeight = 250; // approximate max height of dropdown\n                const minSpaceRequired = 100; // minimum space needed to show dropdown\n                // Prefer showing below, but if not enough space below and more space above, show above\n                if (spaceBelow < minSpaceRequired && spaceAbove > spaceBelow) {\n                    setShowAbove(true);\n                } else {\n                    setShowAbove(false);\n                }\n            };\n            updatePosition();\n            // Update position on window resize or scroll\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 575,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"absolute z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: showAbove ? {\n                    bottom: \"100%\",\n                    left: \"0\",\n                    marginBottom: \"2px\"\n                } : {\n                    top: \"100%\",\n                    left: \"0\",\n                    marginTop: \"2px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 613,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 591,\n                columnNumber: 17\n            }, this),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 640,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 574,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"P2/CRSNwihOzgUKHan2mPDJ45XA=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});