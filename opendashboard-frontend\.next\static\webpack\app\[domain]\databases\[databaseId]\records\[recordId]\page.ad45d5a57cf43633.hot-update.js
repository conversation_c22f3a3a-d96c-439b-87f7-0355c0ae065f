"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/records/[recordId]/page",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    const [positionCalculated, setPositionCalculated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // ensure position is calculated before showing\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Calculate position immediately to prevent flash\n                if (divRef.current) {\n                    const inputRect = divRef.current.getBoundingClientRect();\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250;\n                    const minSpaceRequired = 100;\n                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                    setShowAbove(shouldShowAbove);\n                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                    setPopupPosition({\n                        left: inputRect.left + scrollX,\n                        top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                    });\n                    // Mark position as calculated\n                    setPositionCalculated(true);\n                }\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n        setPositionCalculated(false); // Reset position calculated state\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is inside the input field\n            if (divRef.current && divRef.current.contains(target)) {\n                return; // Don't close if clicking inside input\n            }\n            // Check if click is inside the popup (now rendered as portal)\n            if (popupRef.current && popupRef.current.contains(target)) {\n                return; // Don't close if clicking inside popup\n            }\n            // Close if click is outside both input and popup\n            closeMentionPopup();\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Update position on window resize or scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            // Only listen for resize and scroll events, don't run immediately\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 597,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && positionCalculated && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 631,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 621,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 613,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 596,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"LwTjOlhT2DVbFeCb0bHxaVUm1S0=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});