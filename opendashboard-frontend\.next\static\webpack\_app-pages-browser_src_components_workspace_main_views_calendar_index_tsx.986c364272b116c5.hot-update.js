"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/allday.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/allday.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AllDayRow: function() { return /* binding */ AllDayRow; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,format,isSameDay,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _utils_eventCollision__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/eventCollision */ \"(app-pages-browser)/./src/utils/eventCollision.ts\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AllDayRow = (param)=>{\n    let { selectedDate, segments, selectedEvent, setSelectedEvent, handleEventClick, canEditData, openAddEventForm, view, activeDragData } = param;\n    _s();\n    const dayViewHook = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"yyyy-MM-dd\")),\n        data: {\n            date: selectedDate,\n            type: \"allday-day\"\n        }\n    });\n    const weekDay1 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 0), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 0),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay2 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 1), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 1),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay3 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 2), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 2),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay4 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 3), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 3),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay5 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 4), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 4),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay6 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 5), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 5),\n            type: \"allday-week\"\n        }\n    });\n    const weekDay7 = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable)({\n        id: \"allday-\".concat((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n            weekStartsOn: 0\n        }), 6), \"yyyy-MM-dd\")),\n        data: {\n            date: (0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), 6),\n            type: \"allday-week\"\n        }\n    });\n    const weekViewHooks = [\n        weekDay1,\n        weekDay2,\n        weekDay3,\n        weekDay4,\n        weekDay5,\n        weekDay6,\n        weekDay7\n    ];\n    if (segments.length === 0 && !activeDragData) {\n        return null;\n    }\n    const renderDayView = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: dayViewHook.setNodeRef,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1 relative p-2 space-y-1\", dayViewHook.isOver && \"bg-blue-50\"),\n                        children: [\n                            segments.slice(0, 3).map((segment)=>{\n                                var _activeDragData_payload;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                    segment: segment,\n                                    style: {\n                                        height: \"24px\",\n                                        width: \"100%\"\n                                    },\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setSelectedEvent(segment.originalEventId);\n                                        handleEventClick(segment.originalEvent);\n                                    },\n                                    view: \"day\",\n                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === segment.id\n                                }, segment.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined);\n                            }),\n                            segments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-neutral-600 font-medium cursor-pointer hover:text-neutral-800\",\n                                children: [\n                                    \"+ \",\n                                    segments.length - 3,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n            lineNumber: 114,\n            columnNumber: 5\n        }, undefined);\n    const renderWeekView = ()=>{\n        const weekDays = Array.from({\n            length: 7\n        }, (_, i)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])((0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate, {\n                weekStartsOn: 0\n            }), i));\n        const spanningEvents = (()=>{\n            const eventGroups = new Map();\n            segments.forEach((segment)=>{\n                if (segment.isMultiDay || segment.isAllDay) {\n                    const eventId = segment.originalEventId;\n                    if (!eventGroups.has(eventId)) eventGroups.set(eventId, []);\n                    eventGroups.get(eventId).push(segment);\n                }\n            });\n            const spanning = [];\n            eventGroups.forEach((eventSegments)=>{\n                eventSegments.sort((a, b)=>a.date.getTime() - b.date.getTime());\n                const firstSegmentInWeek = eventSegments[0];\n                const lastSegmentInWeek = eventSegments[eventSegments.length - 1];\n                const startDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, firstSegmentInWeek.date));\n                const endDayIndex = weekDays.findIndex((day)=>(0,_barrel_optimize_names_addDays_format_isSameDay_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(day, lastSegmentInWeek.date));\n                if (startDayIndex >= 0 && endDayIndex >= 0) {\n                    spanning.push({\n                        segment: firstSegmentInWeek,\n                        startDayIndex,\n                        endDayIndex,\n                        colSpan: endDayIndex - startDayIndex + 1,\n                        isEndOfEvent: lastSegmentInWeek.isLastSegment\n                    });\n                }\n            });\n            return spanning;\n        })();\n        const positionedEvents = (()=>{\n            const positioned = [];\n            const rows = [];\n            const sortedEvents = [\n                ...spanningEvents\n            ].sort((a, b)=>a.startDayIndex - b.startDayIndex || b.colSpan - a.colSpan);\n            sortedEvents.forEach((event)=>{\n                let assigned = false;\n                for(let i = 0; i < rows.length; i++){\n                    const row = rows[i];\n                    if (row.every((rowEvent)=>event.startDayIndex > rowEvent.endDayIndex || event.endDayIndex < rowEvent.startDayIndex)) {\n                        row.push(event);\n                        positioned.push({\n                            ...event,\n                            row: i\n                        });\n                        assigned = true;\n                        break;\n                    }\n                }\n                if (!assigned) {\n                    rows.push([\n                        event\n                    ]);\n                    positioned.push({\n                        ...event,\n                        row: rows.length - 1\n                    });\n                }\n            });\n            return positioned;\n        })();\n        const { visibleSegments, moreCount } = (0,_utils_eventCollision__WEBPACK_IMPORTED_MODULE_4__.calculateAllDayLayout)(positionedEvents.map((e)=>e.segment), 3);\n        const visibleEvents = positionedEvents.filter((p)=>visibleSegments.some((s)=>s.id === p.segment.id));\n        const hasMore = moreCount > 0;\n        const firstEventDayIndex = positionedEvents.length > 0 ? Math.min(...positionedEvents.map((e)=>e.startDayIndex)) : 0;\n        if (positionedEvents.length === 0) return null;\n        const maxRows = positionedEvents.length > 0 ? Math.max(...positionedEvents.map((e)=>e.row)) + 1 : 0;\n        const rowHeight = 28;\n        const displayRows = hasMore ? 3.5 : Math.max(1, maxRows);\n        const totalHeight = displayRows * rowHeight + 16;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-all-day-row\": \"true\",\n            className: \"border-b border-neutral-300 bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sticky left-0 bg-white z-10 w-14 lg:w-20 border-r border-neutral-300 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-neutral-500\",\n                        children: \"All-day\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative p-2\",\n                        style: {\n                            height: \"\".concat(totalHeight, \"px\")\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-7 gap-1 h-full\",\n                            children: weekDays.map((day, dayIndex)=>{\n                                const hook = weekViewHooks[dayIndex];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: hook.setNodeRef,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative cursor-pointer hover:bg-neutral-50 rounded-sm transition-colors\", hook.isOver && \"bg-blue-50\"),\n                                    onDoubleClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(day);\n                                            newDate.setHours(9, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: [\n                                        visibleEvents.filter((spanningEvent)=>spanningEvent.startDayIndex === dayIndex).map((spanningEvent)=>{\n                                            var _activeDragData_payload;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute z-10\",\n                                                style: {\n                                                    top: \"\".concat(spanningEvent.row * rowHeight + 2, \"px\"),\n                                                    left: \"0px\",\n                                                    width: \"calc(\".concat(spanningEvent.colSpan * 100, \"% + \").concat((spanningEvent.colSpan - 1) * 4, \"px)\"),\n                                                    height: \"24px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_3__.CalendarEventSegment, {\n                                                    segment: spanningEvent.segment,\n                                                    isEndOfEvent: spanningEvent.isEndOfEvent,\n                                                    style: {\n                                                        height: \"24px\",\n                                                        width: \"100%\"\n                                                    },\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedEvent(spanningEvent.segment.originalEventId);\n                                                        handleEventClick(spanningEvent.segment.originalEvent);\n                                                    },\n                                                    view: view,\n                                                    isDragging: (activeDragData === null || activeDragData === void 0 ? void 0 : (_activeDragData_payload = activeDragData.payload) === null || _activeDragData_payload === void 0 ? void 0 : _activeDragData_payload.id) === spanningEvent.segment.id\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, spanningEvent.segment.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        }),\n                                        hasMore && dayIndex === firstEventDayIndex && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute z-10 cursor-pointer text-xs text-gray-600 hover:underline\",\n                                            style: {\n                                                top: \"\".concat(3 * rowHeight + 2, \"px\"),\n                                                left: \"4px\",\n                                                right: \"4px\"\n                                            },\n                                            onClick: ()=>console.log(\"More clicked\"),\n                                            children: [\n                                                \"+\",\n                                                moreCount,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, dayIndex, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\allday.tsx\",\n            lineNumber: 225,\n            columnNumber: 5\n        }, undefined);\n    };\n    return view === \"day\" ? renderDayView() : renderWeekView();\n};\n_s(AllDayRow, \"7+zBFPGq0SU8Pt7cRj6jYFJX7rY=\", false, function() {\n    return [\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_5__.useDroppable\n    ];\n});\n_c = AllDayRow;\nvar _c;\n$RefreshReg$(_c, \"AllDayRow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvYWxsZGF5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QztBQUN1QztBQUM3QztBQUd5RTtBQUMzQztBQUNsQjtBQXNCdEMsTUFBTVMsWUFBc0M7UUFBQyxFQUNsREMsWUFBWSxFQUNaQyxRQUFRLEVBQ1JDLGFBQWEsRUFDYkMsZ0JBQWdCLEVBQ2hCQyxnQkFBZ0IsRUFDaEJDLFdBQVcsRUFDWEMsZ0JBQWdCLEVBQ2hCQyxJQUFJLEVBQ0pDLGNBQWMsRUFDZjs7SUFDQyxNQUFNQyxjQUFjWCwyREFBWUEsQ0FBQztRQUMvQlksSUFBSSxVQUE2QyxPQUFuQ25CLGdIQUFNQSxDQUFDUyxjQUFjO1FBQ25DVyxNQUFNO1lBQ0pDLE1BQU1aO1lBQ05hLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTUMsV0FBV2hCLDJEQUFZQSxDQUFDO1FBQzVCWSxJQUFJLFVBQTJGLE9BQWpGbkIsZ0hBQU1BLENBQUNDLGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztZQUFFZSxjQUFjO1FBQUUsSUFBSSxJQUFJO1FBQ2pGSixNQUFNO1lBQ0pDLE1BQU1wQixnSEFBT0EsQ0FBQ0MsZ0hBQVdBLENBQUNPLGNBQWM7Z0JBQUVlLGNBQWM7WUFBRSxJQUFJO1lBQzlERixNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1HLFdBQVdsQiwyREFBWUEsQ0FBQztRQUM1QlksSUFBSSxVQUEyRixPQUFqRm5CLGdIQUFNQSxDQUFDQyxnSEFBT0EsQ0FBQ0MsZ0hBQVdBLENBQUNPLGNBQWM7WUFBRWUsY0FBYztRQUFFLElBQUksSUFBSTtRQUNqRkosTUFBTTtZQUNKQyxNQUFNcEIsZ0hBQU9BLENBQUNDLGdIQUFXQSxDQUFDTyxjQUFjO2dCQUFFZSxjQUFjO1lBQUUsSUFBSTtZQUM5REYsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNSSxXQUFXbkIsMkRBQVlBLENBQUM7UUFDNUJZLElBQUksVUFBMkYsT0FBakZuQixnSEFBTUEsQ0FBQ0MsZ0hBQU9BLENBQUNDLGdIQUFXQSxDQUFDTyxjQUFjO1lBQUVlLGNBQWM7UUFBRSxJQUFJLElBQUk7UUFDakZKLE1BQU07WUFDSkMsTUFBTXBCLGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztnQkFBRWUsY0FBYztZQUFFLElBQUk7WUFDOURGLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTUssV0FBV3BCLDJEQUFZQSxDQUFDO1FBQzVCWSxJQUFJLFVBQTJGLE9BQWpGbkIsZ0hBQU1BLENBQUNDLGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztZQUFFZSxjQUFjO1FBQUUsSUFBSSxJQUFJO1FBQ2pGSixNQUFNO1lBQ0pDLE1BQU1wQixnSEFBT0EsQ0FBQ0MsZ0hBQVdBLENBQUNPLGNBQWM7Z0JBQUVlLGNBQWM7WUFBRSxJQUFJO1lBQzlERixNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1NLFdBQVdyQiwyREFBWUEsQ0FBQztRQUM1QlksSUFBSSxVQUEyRixPQUFqRm5CLGdIQUFNQSxDQUFDQyxnSEFBT0EsQ0FBQ0MsZ0hBQVdBLENBQUNPLGNBQWM7WUFBRWUsY0FBYztRQUFFLElBQUksSUFBSTtRQUNqRkosTUFBTTtZQUNKQyxNQUFNcEIsZ0hBQU9BLENBQUNDLGdIQUFXQSxDQUFDTyxjQUFjO2dCQUFFZSxjQUFjO1lBQUUsSUFBSTtZQUM5REYsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNTyxXQUFXdEIsMkRBQVlBLENBQUM7UUFDNUJZLElBQUksVUFBMkYsT0FBakZuQixnSEFBTUEsQ0FBQ0MsZ0hBQU9BLENBQUNDLGdIQUFXQSxDQUFDTyxjQUFjO1lBQUVlLGNBQWM7UUFBRSxJQUFJLElBQUk7UUFDakZKLE1BQU07WUFDSkMsTUFBTXBCLGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztnQkFBRWUsY0FBYztZQUFFLElBQUk7WUFDOURGLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTVEsV0FBV3ZCLDJEQUFZQSxDQUFDO1FBQzVCWSxJQUFJLFVBQTJGLE9BQWpGbkIsZ0hBQU1BLENBQUNDLGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztZQUFFZSxjQUFjO1FBQUUsSUFBSSxJQUFJO1FBQ2pGSixNQUFNO1lBQ0pDLE1BQU1wQixnSEFBT0EsQ0FBQ0MsZ0hBQVdBLENBQUNPLGNBQWM7Z0JBQUVlLGNBQWM7WUFBRSxJQUFJO1lBQzlERixNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1TLGdCQUFnQjtRQUFDUjtRQUFVRTtRQUFVQztRQUFVQztRQUFVQztRQUFVQztRQUFVQztLQUFTO0lBRzVGLElBQUlwQixTQUFTc0IsTUFBTSxLQUFLLEtBQUssQ0FBQ2YsZ0JBQWdCO1FBQzVDLE9BQU87SUFDVDtJQUdBLE1BQU1nQixnQkFBZ0Isa0JBQ3BCLDhEQUFDQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFtSjs7Ozs7O2tDQUdsSyw4REFBQ0Q7d0JBQ0NFLEtBQUtsQixZQUFZbUIsVUFBVTt3QkFDM0JGLFdBQVcvQiw4Q0FBRUEsQ0FDWCxpQ0FDQWMsWUFBWW9CLE1BQU0sSUFBSTs7NEJBR3ZCNUIsU0FBUzZCLEtBQUssQ0FBQyxHQUFHLEdBQUdDLEdBQUcsQ0FBQyxDQUFDQztvQ0FXWHhCO3FEQVZkLDhEQUFDWixtSEFBb0JBO29DQUVuQm9DLFNBQVNBO29DQUNUQyxPQUFPO3dDQUFFQyxRQUFRO3dDQUFRQyxPQUFPO29DQUFPO29DQUN2Q0MsU0FBUyxDQUFDQzt3Q0FDUkEsRUFBRUMsZUFBZTt3Q0FDakJuQyxpQkFBaUI2QixRQUFRTyxlQUFlO3dDQUN4Q25DLGlCQUFpQjRCLFFBQVFRLGFBQWE7b0NBQ3hDO29DQUNBakMsTUFBSztvQ0FDTGtDLFlBQVlqQyxDQUFBQSwyQkFBQUEsc0NBQUFBLDBCQUFBQSxlQUFnQmtDLE9BQU8sY0FBdkJsQyw4Q0FBQUEsd0JBQXlCRSxFQUFFLE1BQUtzQixRQUFRdEIsRUFBRTttQ0FUakRzQixRQUFRdEIsRUFBRTs7Ozs7OzRCQVlsQlQsU0FBU3NCLE1BQU0sR0FBRyxtQkFDakIsOERBQUNFO2dDQUFJQyxXQUFVOztvQ0FBNkU7b0NBQ3ZGekIsU0FBU3NCLE1BQU0sR0FBRztvQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBUW5DLE1BQU1vQixpQkFBaUI7UUFDckIsTUFBTUMsV0FBV0MsTUFBTUMsSUFBSSxDQUFDO1lBQUV2QixRQUFRO1FBQUUsR0FBRyxDQUFDd0IsR0FBR0MsSUFBTXhELGdIQUFPQSxDQUFDQyxnSEFBV0EsQ0FBQ08sY0FBYztnQkFBRWUsY0FBYztZQUFFLElBQUlpQztRQUU3RyxNQUFNQyxpQkFBaUIsQ0FBQztZQUN0QixNQUFNQyxjQUFjLElBQUlDO1lBQ3hCbEQsU0FBU21ELE9BQU8sQ0FBQ3BCLENBQUFBO2dCQUNmLElBQUlBLFFBQVFxQixVQUFVLElBQUlyQixRQUFRc0IsUUFBUSxFQUFFO29CQUMxQyxNQUFNQyxVQUFVdkIsUUFBUU8sZUFBZTtvQkFDdkMsSUFBSSxDQUFDVyxZQUFZTSxHQUFHLENBQUNELFVBQVVMLFlBQVlPLEdBQUcsQ0FBQ0YsU0FBUyxFQUFFO29CQUMxREwsWUFBWVEsR0FBRyxDQUFDSCxTQUFVSSxJQUFJLENBQUMzQjtnQkFDakM7WUFDRjtZQUVBLE1BQU00QixXQUE0QixFQUFFO1lBQ3BDVixZQUFZRSxPQUFPLENBQUMsQ0FBQ1M7Z0JBQ25CQSxjQUFjQyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRW5ELElBQUksQ0FBQ3FELE9BQU8sS0FBS0QsRUFBRXBELElBQUksQ0FBQ3FELE9BQU87Z0JBQzlELE1BQU1DLHFCQUFxQkwsYUFBYSxDQUFDLEVBQUU7Z0JBQzNDLE1BQU1NLG9CQUFvQk4sYUFBYSxDQUFDQSxjQUFjdEMsTUFBTSxHQUFHLEVBQUU7Z0JBQ2pFLE1BQU02QyxnQkFBZ0J4QixTQUFTeUIsU0FBUyxDQUFDQyxDQUFBQSxNQUFPNUUsZ0hBQVNBLENBQUM0RSxLQUFLSixtQkFBbUJ0RCxJQUFJO2dCQUN0RixNQUFNMkQsY0FBYzNCLFNBQVN5QixTQUFTLENBQUNDLENBQUFBLE1BQU81RSxnSEFBU0EsQ0FBQzRFLEtBQUtILGtCQUFrQnZELElBQUk7Z0JBRW5GLElBQUl3RCxpQkFBaUIsS0FBS0csZUFBZSxHQUFHO29CQUMxQ1gsU0FBU0QsSUFBSSxDQUFDO3dCQUNaM0IsU0FBU2tDO3dCQUNURTt3QkFDQUc7d0JBQ0FDLFNBQVNELGNBQWNILGdCQUFnQjt3QkFDdkNLLGNBQWNOLGtCQUFrQk8sYUFBYTtvQkFDL0M7Z0JBQ0Y7WUFDRjtZQUNBLE9BQU9kO1FBQ1Q7UUFFQSxNQUFNZSxtQkFBbUIsQ0FBQztZQUN4QixNQUFNQyxhQUFxRCxFQUFFO1lBQzdELE1BQU1DLE9BQStCLEVBQUU7WUFDdkMsTUFBTUMsZUFBZTttQkFBSTdCO2FBQWUsQ0FBQ2EsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELEVBQUVLLGFBQWEsR0FBR0osRUFBRUksYUFBYSxJQUFJSixFQUFFUSxPQUFPLEdBQUdULEVBQUVTLE9BQU87WUFFbEhNLGFBQWExQixPQUFPLENBQUMyQixDQUFBQTtnQkFDbkIsSUFBSUMsV0FBVztnQkFDZixJQUFLLElBQUloQyxJQUFJLEdBQUdBLElBQUk2QixLQUFLdEQsTUFBTSxFQUFFeUIsSUFBSztvQkFDcEMsTUFBTWlDLE1BQU1KLElBQUksQ0FBQzdCLEVBQUU7b0JBQ25CLElBQUlpQyxJQUFJQyxLQUFLLENBQUNDLENBQUFBLFdBQVlKLE1BQU1YLGFBQWEsR0FBR2UsU0FBU1osV0FBVyxJQUFJUSxNQUFNUixXQUFXLEdBQUdZLFNBQVNmLGFBQWEsR0FBRzt3QkFDbkhhLElBQUl0QixJQUFJLENBQUNvQjt3QkFDVEgsV0FBV2pCLElBQUksQ0FBQzs0QkFBRSxHQUFHb0IsS0FBSzs0QkFBRUUsS0FBS2pDO3dCQUFFO3dCQUNuQ2dDLFdBQVc7d0JBQ1g7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0EsSUFBSSxDQUFDQSxVQUFVO29CQUNiSCxLQUFLbEIsSUFBSSxDQUFDO3dCQUFDb0I7cUJBQU07b0JBQ2pCSCxXQUFXakIsSUFBSSxDQUFDO3dCQUFFLEdBQUdvQixLQUFLO3dCQUFFRSxLQUFLSixLQUFLdEQsTUFBTSxHQUFHO29CQUFFO2dCQUNuRDtZQUNGO1lBQ0EsT0FBT3FEO1FBQ1Q7UUFFQSxNQUFNLEVBQUVRLGVBQWUsRUFBRUMsU0FBUyxFQUFFLEdBQUd4Riw0RUFBcUJBLENBQzFEOEUsaUJBQWlCNUMsR0FBRyxDQUFDTSxDQUFBQSxJQUFLQSxFQUFFTCxPQUFPLEdBQUc7UUFHeEMsTUFBTXNELGdCQUFnQlgsaUJBQWlCWSxNQUFNLENBQUNDLENBQUFBLElBQUtKLGdCQUFnQkssSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFaEYsRUFBRSxLQUFLOEUsRUFBRXhELE9BQU8sQ0FBQ3RCLEVBQUU7UUFDbEcsTUFBTWlGLFVBQVVOLFlBQVk7UUFFNUIsTUFBTU8scUJBQXFCakIsaUJBQWlCcEQsTUFBTSxHQUFHLElBQUlzRSxLQUFLQyxHQUFHLElBQUluQixpQkFBaUI1QyxHQUFHLENBQUNNLENBQUFBLElBQUtBLEVBQUUrQixhQUFhLEtBQUs7UUFFbkgsSUFBSU8saUJBQWlCcEQsTUFBTSxLQUFLLEdBQUcsT0FBTztRQUUxQyxNQUFNd0UsVUFBVXBCLGlCQUFpQnBELE1BQU0sR0FBRyxJQUFJc0UsS0FBS0csR0FBRyxJQUFJckIsaUJBQWlCNUMsR0FBRyxDQUFDTSxDQUFBQSxJQUFLQSxFQUFFNEMsR0FBRyxLQUFLLElBQUk7UUFDbEcsTUFBTWdCLFlBQVk7UUFDbEIsTUFBTUMsY0FBY1AsVUFBVSxNQUFNRSxLQUFLRyxHQUFHLENBQUMsR0FBR0Q7UUFDaEQsTUFBTUksY0FBY0QsY0FBY0QsWUFBWTtRQUU1QyxxQkFDRiw4REFBQ3hFO1lBQ0MyRSxvQkFBaUI7WUFDakIxRSxXQUFVO3NCQUVSLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUFtSjs7Ozs7O2tDQUdsSyw4REFBQ0Q7d0JBQUlDLFdBQVU7d0JBQXNCTyxPQUFPOzRCQUFFQyxRQUFRLEdBQWUsT0FBWmlFLGFBQVk7d0JBQUk7a0NBQ3ZFLDRFQUFDMUU7NEJBQUlDLFdBQVU7c0NBQ1prQixTQUFTYixHQUFHLENBQUMsQ0FBQ3VDLEtBQUsrQjtnQ0FDbEIsTUFBTUMsT0FBT2hGLGFBQWEsQ0FBQytFLFNBQVM7Z0NBRXBDLHFCQUNBLDhEQUFDNUU7b0NBQ0NFLEtBQUsyRSxLQUFLMUUsVUFBVTtvQ0FFcEJGLFdBQVcvQiw4Q0FBRUEsQ0FDWCw0RUFDQTJHLEtBQUt6RSxNQUFNLElBQUk7b0NBRWpCMEUsZUFBZTt3Q0FDYixJQUFJbEcsYUFBYTs0Q0FDZixNQUFNbUcsVUFBVSxJQUFJQyxLQUFLbkM7NENBQ3pCa0MsUUFBUUUsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHOzRDQUMxQnBHLGlCQUFpQmtHO3dDQUNuQjtvQ0FDRjs7d0NBRUNsQixjQUNFQyxNQUFNLENBQUNvQixDQUFBQSxnQkFBaUJBLGNBQWN2QyxhQUFhLEtBQUtpQyxVQUN4RHRFLEdBQUcsQ0FBQyxDQUFDNEU7Z0RBcUJZbkc7aUVBcEJoQiw4REFBQ2lCO2dEQUVDQyxXQUFVO2dEQUNWTyxPQUFPO29EQUNMMkUsS0FBSyxHQUFxQyxPQUFsQ0QsY0FBYzFCLEdBQUcsR0FBR2dCLFlBQVksR0FBRTtvREFDMUNZLE1BQU07b0RBQ04xRSxPQUFPLFFBQTBDLE9BQWxDd0UsY0FBY25DLE9BQU8sR0FBRyxLQUFJLFFBQXNDLE9BQWhDLENBQUNtQyxjQUFjbkMsT0FBTyxHQUFHLEtBQUssR0FBRTtvREFDakZ0QyxRQUFRO2dEQUNWOzBEQUVBLDRFQUFDdEMsbUhBQW9CQTtvREFDbkJvQyxTQUFTMkUsY0FBYzNFLE9BQU87b0RBQzlCeUMsY0FBY2tDLGNBQWNsQyxZQUFZO29EQUN4Q3hDLE9BQU87d0RBQUVDLFFBQVE7d0RBQVFDLE9BQU87b0RBQU87b0RBQ3ZDQyxTQUFTLENBQUNDO3dEQUNSQSxFQUFFQyxlQUFlO3dEQUNqQm5DLGlCQUFpQndHLGNBQWMzRSxPQUFPLENBQUNPLGVBQWU7d0RBQ3REbkMsaUJBQWlCdUcsY0FBYzNFLE9BQU8sQ0FBQ1EsYUFBYTtvREFDdEQ7b0RBQ0FqQyxNQUFNQTtvREFDTmtDLFlBQVlqQyxDQUFBQSwyQkFBQUEsc0NBQUFBLDBCQUFBQSxlQUFnQmtDLE9BQU8sY0FBdkJsQyw4Q0FBQUEsd0JBQXlCRSxFQUFFLE1BQUtpRyxjQUFjM0UsT0FBTyxDQUFDdEIsRUFBRTs7Ozs7OytDQW5CakVpRyxjQUFjM0UsT0FBTyxDQUFDdEIsRUFBRTs7Ozs7O3dDQXVCbENpRixXQUFXVSxhQUFhVCxvQ0FDdkIsOERBQUNuRTs0Q0FDQ0MsV0FBVTs0Q0FDVk8sT0FBTztnREFDTDJFLEtBQUssR0FBcUIsT0FBbEIsSUFBSVgsWUFBWSxHQUFFO2dEQUMxQlksTUFBTTtnREFDTkMsT0FBTzs0Q0FDVDs0Q0FDQTFFLFNBQVMsSUFBTTJFLFFBQVFDLEdBQUcsQ0FBQzs7Z0RBQzVCO2dEQUNHM0I7Z0RBQVU7Ozs7Ozs7O21DQWxEWGdCOzs7Ozs0QkF1RFQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNWjtJQUVBLE9BQU85RixTQUFTLFFBQVFpQixrQkFBa0JtQjtBQUM1QyxFQUFFO0dBblJXNUM7O1FBV1NELHVEQUFZQTtRQVFmQSx1REFBWUE7UUFRWkEsdURBQVlBO1FBUVpBLHVEQUFZQTtRQVFaQSx1REFBWUE7UUFRWkEsdURBQVlBO1FBUVpBLHVEQUFZQTtRQVFaQSx1REFBWUE7OztLQW5FbEJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvYWxsZGF5LnRzeD9lNTMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZm9ybWF0LCBhZGREYXlzLCBzdGFydE9mV2VlaywgZW5kT2ZXZWVrLCBpc1NhbWVEYXkgfSBmcm9tICdkYXRlLWZucyc7XG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvbGliL3V0aWxzJztcbmltcG9ydCB7IENhbGVuZGFyRXZlbnQgfSBmcm9tICdAL3R5cGluZ3MvcGFnZSc7XG5pbXBvcnQgeyBFdmVudFNlZ21lbnQgfSBmcm9tICdAL3V0aWxzL211bHRpRGF5JztcbmltcG9ydCB7IENhbGVuZGFyRXZlbnRTZWdtZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL3dvcmtzcGFjZS9tYWluL3ZpZXdzL2NhbGVuZGFyL2NvbXBvbmVudHMvZXZlbnRzZWdtZW50JztcbmltcG9ydCB7IGNhbGN1bGF0ZUFsbERheUxheW91dCB9IGZyb20gJ0AvdXRpbHMvZXZlbnRDb2xsaXNpb24nO1xuaW1wb3J0IHsgdXNlRHJvcHBhYmxlIH0gZnJvbSAnQGRuZC1raXQvY29yZSc7XG5cbmludGVyZmFjZSBBbGxEYXlSb3dQcm9wcyB7XG4gIHNlbGVjdGVkRGF0ZTogRGF0ZTtcbiAgc2VnbWVudHM6IEV2ZW50U2VnbWVudFtdO1xuICBzZWxlY3RlZEV2ZW50OiBzdHJpbmcgfCBudWxsO1xuICBzZXRTZWxlY3RlZEV2ZW50OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgaGFuZGxlRXZlbnRDbGljazogKGV2ZW50OiBDYWxlbmRhckV2ZW50KSA9PiB2b2lkO1xuICBjYW5FZGl0RGF0YTogYm9vbGVhbjtcbiAgb3BlbkFkZEV2ZW50Rm9ybTogKGRhdGU6IERhdGUpID0+IHZvaWQ7XG4gIHZpZXc/OiAnZGF5JyB8ICd3ZWVrJztcbiAgYWN0aXZlRHJhZ0RhdGE/OiBhbnk7XG59XG5cbmludGVyZmFjZSBTcGFubmluZ0V2ZW50IHtcbiAgc2VnbWVudDogRXZlbnRTZWdtZW50O1xuICBzdGFydERheUluZGV4OiBudW1iZXI7XG4gIGVuZERheUluZGV4OiBudW1iZXI7XG4gIGNvbFNwYW46IG51bWJlcjtcbiAgaXNFbmRPZkV2ZW50OiBib29sZWFuO1xufVxuXG5leHBvcnQgY29uc3QgQWxsRGF5Um93OiBSZWFjdC5GQzxBbGxEYXlSb3dQcm9wcz4gPSAoe1xuICBzZWxlY3RlZERhdGUsXG4gIHNlZ21lbnRzLFxuICBzZWxlY3RlZEV2ZW50LFxuICBzZXRTZWxlY3RlZEV2ZW50LFxuICBoYW5kbGVFdmVudENsaWNrLFxuICBjYW5FZGl0RGF0YSxcbiAgb3BlbkFkZEV2ZW50Rm9ybSxcbiAgdmlldyxcbiAgYWN0aXZlRHJhZ0RhdGFcbn0pID0+IHtcbiAgY29uc3QgZGF5Vmlld0hvb2sgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ3l5eXktTU0tZGQnKX1gLFxuICAgIGRhdGE6IHtcbiAgICAgIGRhdGU6IHNlbGVjdGVkRGF0ZSxcbiAgICAgIHR5cGU6ICdhbGxkYXktZGF5J1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTEgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgMCksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDApLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTIgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgMSksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDEpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTMgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgMiksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDIpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTQgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgMyksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDMpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTUgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgNCksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDQpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTYgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgNSksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDUpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla0RheTcgPSB1c2VEcm9wcGFibGUoe1xuICAgIGlkOiBgYWxsZGF5LSR7Zm9ybWF0KGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgNiksICd5eXl5LU1NLWRkJyl9YCxcbiAgICBkYXRhOiB7XG4gICAgICBkYXRlOiBhZGREYXlzKHN0YXJ0T2ZXZWVrKHNlbGVjdGVkRGF0ZSwgeyB3ZWVrU3RhcnRzT246IDAgfSksIDYpLFxuICAgICAgdHlwZTogJ2FsbGRheS13ZWVrJ1xuICAgIH1cbiAgfSk7XG5cbiAgY29uc3Qgd2Vla1ZpZXdIb29rcyA9IFt3ZWVrRGF5MSwgd2Vla0RheTIsIHdlZWtEYXkzLCB3ZWVrRGF5NCwgd2Vla0RheTUsIHdlZWtEYXk2LCB3ZWVrRGF5N107XG5cbiAgXG4gIGlmIChzZWdtZW50cy5sZW5ndGggPT09IDAgJiYgIWFjdGl2ZURyYWdEYXRhKSB7IFxuICAgIHJldHVybiBudWxsO1xuICB9XG5cblxuICBjb25zdCByZW5kZXJEYXlWaWV3ID0gKCkgPT4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXdoaXRlXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzdGlja3kgbGVmdC0wIGJnLXdoaXRlIHotMTAgdy0xNCBsZzp3LTIwIGJvcmRlci1yIGJvcmRlci1uZXV0cmFsLTMwMCBmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktZW5kIHByLTQgcHQtMiB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtbmV1dHJhbC01MDBcIj5cbiAgICAgICAgICBBbGwtZGF5XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIHJlZj17ZGF5Vmlld0hvb2suc2V0Tm9kZVJlZn1cbiAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgXCJmbGV4LTEgcmVsYXRpdmUgcC0yIHNwYWNlLXktMVwiLFxuICAgICAgICAgICAgZGF5Vmlld0hvb2suaXNPdmVyICYmIFwiYmctYmx1ZS01MFwiXG4gICAgICAgICAgKX1cbiAgICAgICAgPlxuICAgICAgICAgIHtzZWdtZW50cy5zbGljZSgwLCAzKS5tYXAoKHNlZ21lbnQpID0+IChcbiAgICAgICAgICAgIDxDYWxlbmRhckV2ZW50U2VnbWVudFxuICAgICAgICAgICAgICBrZXk9e3NlZ21lbnQuaWR9XG4gICAgICAgICAgICAgIHNlZ21lbnQ9e3NlZ21lbnR9XG4gICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogJzI0cHgnLCB3aWR0aDogJzEwMCUnIH19XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEV2ZW50KHNlZ21lbnQub3JpZ2luYWxFdmVudElkKTtcbiAgICAgICAgICAgICAgICBoYW5kbGVFdmVudENsaWNrKHNlZ21lbnQub3JpZ2luYWxFdmVudCk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIHZpZXc9XCJkYXlcIlxuICAgICAgICAgICAgICBpc0RyYWdnaW5nPXthY3RpdmVEcmFnRGF0YT8ucGF5bG9hZD8uaWQgPT09IHNlZ21lbnQuaWR9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICkpfVxuICAgICAgICAgIHtzZWdtZW50cy5sZW5ndGggPiAzICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW5ldXRyYWwtNjAwIGZvbnQtbWVkaXVtIGN1cnNvci1wb2ludGVyIGhvdmVyOnRleHQtbmV1dHJhbC04MDBcIj5cbiAgICAgICAgICAgICAgKyB7c2VnbWVudHMubGVuZ3RoIC0gM30gbW9yZVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcblxuICBjb25zdCByZW5kZXJXZWVrVmlldyA9ICgpID0+IHtcbiAgICBjb25zdCB3ZWVrRGF5cyA9IEFycmF5LmZyb20oeyBsZW5ndGg6IDcgfSwgKF8sIGkpID0+IGFkZERheXMoc3RhcnRPZldlZWsoc2VsZWN0ZWREYXRlLCB7IHdlZWtTdGFydHNPbjogMCB9KSwgaSkpO1xuXG4gICAgY29uc3Qgc3Bhbm5pbmdFdmVudHMgPSAoKCkgPT4ge1xuICAgICAgY29uc3QgZXZlbnRHcm91cHMgPSBuZXcgTWFwPHN0cmluZywgRXZlbnRTZWdtZW50W10+KCk7XG4gICAgICBzZWdtZW50cy5mb3JFYWNoKHNlZ21lbnQgPT4ge1xuICAgICAgICBpZiAoc2VnbWVudC5pc011bHRpRGF5IHx8IHNlZ21lbnQuaXNBbGxEYXkpIHtcbiAgICAgICAgICBjb25zdCBldmVudElkID0gc2VnbWVudC5vcmlnaW5hbEV2ZW50SWQ7XG4gICAgICAgICAgaWYgKCFldmVudEdyb3Vwcy5oYXMoZXZlbnRJZCkpIGV2ZW50R3JvdXBzLnNldChldmVudElkLCBbXSk7XG4gICAgICAgICAgZXZlbnRHcm91cHMuZ2V0KGV2ZW50SWQpIS5wdXNoKHNlZ21lbnQpO1xuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgY29uc3Qgc3Bhbm5pbmc6IFNwYW5uaW5nRXZlbnRbXSA9IFtdO1xuICAgICAgZXZlbnRHcm91cHMuZm9yRWFjaCgoZXZlbnRTZWdtZW50cykgPT4ge1xuICAgICAgICBldmVudFNlZ21lbnRzLnNvcnQoKGEsIGIpID0+IGEuZGF0ZS5nZXRUaW1lKCkgLSBiLmRhdGUuZ2V0VGltZSgpKTtcbiAgICAgICAgY29uc3QgZmlyc3RTZWdtZW50SW5XZWVrID0gZXZlbnRTZWdtZW50c1swXTtcbiAgICAgICAgY29uc3QgbGFzdFNlZ21lbnRJbldlZWsgPSBldmVudFNlZ21lbnRzW2V2ZW50U2VnbWVudHMubGVuZ3RoIC0gMV07XG4gICAgICAgIGNvbnN0IHN0YXJ0RGF5SW5kZXggPSB3ZWVrRGF5cy5maW5kSW5kZXgoZGF5ID0+IGlzU2FtZURheShkYXksIGZpcnN0U2VnbWVudEluV2Vlay5kYXRlKSk7XG4gICAgICAgIGNvbnN0IGVuZERheUluZGV4ID0gd2Vla0RheXMuZmluZEluZGV4KGRheSA9PiBpc1NhbWVEYXkoZGF5LCBsYXN0U2VnbWVudEluV2Vlay5kYXRlKSk7XG5cbiAgICAgICAgaWYgKHN0YXJ0RGF5SW5kZXggPj0gMCAmJiBlbmREYXlJbmRleCA+PSAwKSB7XG4gICAgICAgICAgc3Bhbm5pbmcucHVzaCh7XG4gICAgICAgICAgICBzZWdtZW50OiBmaXJzdFNlZ21lbnRJbldlZWssXG4gICAgICAgICAgICBzdGFydERheUluZGV4LFxuICAgICAgICAgICAgZW5kRGF5SW5kZXgsXG4gICAgICAgICAgICBjb2xTcGFuOiBlbmREYXlJbmRleCAtIHN0YXJ0RGF5SW5kZXggKyAxLFxuICAgICAgICAgICAgaXNFbmRPZkV2ZW50OiBsYXN0U2VnbWVudEluV2Vlay5pc0xhc3RTZWdtZW50LFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBzcGFubmluZztcbiAgICB9KSgpO1xuXG4gICAgY29uc3QgcG9zaXRpb25lZEV2ZW50cyA9ICgoKSA9PiB7XG4gICAgICBjb25zdCBwb3NpdGlvbmVkOiBBcnJheTxTcGFubmluZ0V2ZW50ICYgeyByb3c6IG51bWJlciB9PiA9IFtdO1xuICAgICAgY29uc3Qgcm93czogQXJyYXk8U3Bhbm5pbmdFdmVudFtdPiA9IFtdO1xuICAgICAgY29uc3Qgc29ydGVkRXZlbnRzID0gWy4uLnNwYW5uaW5nRXZlbnRzXS5zb3J0KChhLCBiKSA9PiBhLnN0YXJ0RGF5SW5kZXggLSBiLnN0YXJ0RGF5SW5kZXggfHwgYi5jb2xTcGFuIC0gYS5jb2xTcGFuKTtcblxuICAgICAgc29ydGVkRXZlbnRzLmZvckVhY2goZXZlbnQgPT4ge1xuICAgICAgICBsZXQgYXNzaWduZWQgPSBmYWxzZTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByb3dzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgY29uc3Qgcm93ID0gcm93c1tpXTtcbiAgICAgICAgICBpZiAocm93LmV2ZXJ5KHJvd0V2ZW50ID0+IGV2ZW50LnN0YXJ0RGF5SW5kZXggPiByb3dFdmVudC5lbmREYXlJbmRleCB8fCBldmVudC5lbmREYXlJbmRleCA8IHJvd0V2ZW50LnN0YXJ0RGF5SW5kZXgpKSB7XG4gICAgICAgICAgICByb3cucHVzaChldmVudCk7XG4gICAgICAgICAgICBwb3NpdGlvbmVkLnB1c2goeyAuLi5ldmVudCwgcm93OiBpIH0pO1xuICAgICAgICAgICAgYXNzaWduZWQgPSB0cnVlO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICghYXNzaWduZWQpIHtcbiAgICAgICAgICByb3dzLnB1c2goW2V2ZW50XSk7XG4gICAgICAgICAgcG9zaXRpb25lZC5wdXNoKHsgLi4uZXZlbnQsIHJvdzogcm93cy5sZW5ndGggLSAxIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBwb3NpdGlvbmVkO1xuICAgIH0pKCk7XG5cbiAgICBjb25zdCB7IHZpc2libGVTZWdtZW50cywgbW9yZUNvdW50IH0gPSBjYWxjdWxhdGVBbGxEYXlMYXlvdXQoXG4gICAgICBwb3NpdGlvbmVkRXZlbnRzLm1hcChlID0+IGUuc2VnbWVudCksIDNcbiAgICApO1xuXG4gICAgY29uc3QgdmlzaWJsZUV2ZW50cyA9IHBvc2l0aW9uZWRFdmVudHMuZmlsdGVyKHAgPT4gdmlzaWJsZVNlZ21lbnRzLnNvbWUocyA9PiBzLmlkID09PSBwLnNlZ21lbnQuaWQpKTtcbiAgICBjb25zdCBoYXNNb3JlID0gbW9yZUNvdW50ID4gMDtcblxuICAgIGNvbnN0IGZpcnN0RXZlbnREYXlJbmRleCA9IHBvc2l0aW9uZWRFdmVudHMubGVuZ3RoID4gMCA/IE1hdGgubWluKC4uLnBvc2l0aW9uZWRFdmVudHMubWFwKGUgPT4gZS5zdGFydERheUluZGV4KSkgOiAwO1xuICAgIFxuICAgIGlmIChwb3NpdGlvbmVkRXZlbnRzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIG51bGw7XG5cbiAgICBjb25zdCBtYXhSb3dzID0gcG9zaXRpb25lZEV2ZW50cy5sZW5ndGggPiAwID8gTWF0aC5tYXgoLi4ucG9zaXRpb25lZEV2ZW50cy5tYXAoZSA9PiBlLnJvdykpICsgMSA6IDA7XG4gICAgY29uc3Qgcm93SGVpZ2h0ID0gMjg7XG4gICAgY29uc3QgZGlzcGxheVJvd3MgPSBoYXNNb3JlID8gMy41IDogTWF0aC5tYXgoMSwgbWF4Um93cyk7IFxuICAgIGNvbnN0IHRvdGFsSGVpZ2h0ID0gZGlzcGxheVJvd3MgKiByb3dIZWlnaHQgKyAxNjtcblxuICAgICAgcmV0dXJuIChcbiAgICA8ZGl2IFxuICAgICAgZGF0YS1hbGwtZGF5LXJvdz1cInRydWVcIlxuICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLW5ldXRyYWwtMzAwIGJnLXdoaXRlXCJcbiAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3RpY2t5IGxlZnQtMCBiZy13aGl0ZSB6LTEwIHctMTQgbGc6dy0yMCBib3JkZXItciBib3JkZXItbmV1dHJhbC0zMDAgZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWVuZCBwci00IHB0LTIgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LW5ldXRyYWwtNTAwXCI+XG4gICAgICAgICAgICBBbGwtZGF5XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcmVsYXRpdmUgcC0yXCIgc3R5bGU9e3sgaGVpZ2h0OiBgJHt0b3RhbEhlaWdodH1weGAgfX0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTcgZ2FwLTEgaC1mdWxsXCI+XG4gICAgICAgICAgICAgIHt3ZWVrRGF5cy5tYXAoKGRheSwgZGF5SW5kZXgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBob29rID0gd2Vla1ZpZXdIb29rc1tkYXlJbmRleF07XG5cbiAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICByZWY9e2hvb2suc2V0Tm9kZVJlZn1cbiAgICAgICAgICAgICAgICAgIGtleT17ZGF5SW5kZXh9IFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgXCJyZWxhdGl2ZSBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1uZXV0cmFsLTUwIHJvdW5kZWQtc20gdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgICAgICAgICAgICAgaG9vay5pc092ZXIgJiYgXCJiZy1ibHVlLTUwXCJcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICBvbkRvdWJsZUNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjYW5FZGl0RGF0YSkge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0RhdGUgPSBuZXcgRGF0ZShkYXkpO1xuICAgICAgICAgICAgICAgICAgICAgIG5ld0RhdGUuc2V0SG91cnMoOSwgMCwgMCwgMCk7XG4gICAgICAgICAgICAgICAgICAgICAgb3BlbkFkZEV2ZW50Rm9ybShuZXdEYXRlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dmlzaWJsZUV2ZW50c1xuICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKHNwYW5uaW5nRXZlbnQgPT4gc3Bhbm5pbmdFdmVudC5zdGFydERheUluZGV4ID09PSBkYXlJbmRleClcbiAgICAgICAgICAgICAgICAgICAgLm1hcCgoc3Bhbm5pbmdFdmVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17c3Bhbm5pbmdFdmVudC5zZWdtZW50LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgei0xMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICB0b3A6IGAke3NwYW5uaW5nRXZlbnQucm93ICogcm93SGVpZ2h0ICsgMn1weGAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6ICcwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogYGNhbGMoJHtzcGFubmluZ0V2ZW50LmNvbFNwYW4gKiAxMDB9JSArICR7KHNwYW5uaW5nRXZlbnQuY29sU3BhbiAtIDEpICogNH1weClgLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcyNHB4J1xuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FsZW5kYXJFdmVudFNlZ21lbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2VnbWVudD17c3Bhbm5pbmdFdmVudC5zZWdtZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBpc0VuZE9mRXZlbnQ9e3NwYW5uaW5nRXZlbnQuaXNFbmRPZkV2ZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6ICcyNHB4Jywgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFdmVudChzcGFubmluZ0V2ZW50LnNlZ21lbnQub3JpZ2luYWxFdmVudElkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVFdmVudENsaWNrKHNwYW5uaW5nRXZlbnQuc2VnbWVudC5vcmlnaW5hbEV2ZW50KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmlldz17dmlld31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgaXNEcmFnZ2luZz17YWN0aXZlRHJhZ0RhdGE/LnBheWxvYWQ/LmlkID09PSBzcGFubmluZ0V2ZW50LnNlZ21lbnQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIHtoYXNNb3JlICYmIGRheUluZGV4ID09PSBmaXJzdEV2ZW50RGF5SW5kZXggJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgei0xMCBjdXJzb3ItcG9pbnRlciB0ZXh0LXhzIHRleHQtZ3JheS02MDAgaG92ZXI6dW5kZXJsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgdG9wOiBgJHszICogcm93SGVpZ2h0ICsgMn1weGAsXG4gICAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiAnNHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAnNHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvbnNvbGUubG9nKCdNb3JlIGNsaWNrZWQnKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICt7bW9yZUNvdW50fSBtb3JlXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfTtcblxuICByZXR1cm4gdmlldyA9PT0gJ2RheScgPyByZW5kZXJEYXlWaWV3KCkgOiByZW5kZXJXZWVrVmlldygpO1xufTsgIl0sIm5hbWVzIjpbIlJlYWN0IiwiZm9ybWF0IiwiYWRkRGF5cyIsInN0YXJ0T2ZXZWVrIiwiaXNTYW1lRGF5IiwiY24iLCJDYWxlbmRhckV2ZW50U2VnbWVudCIsImNhbGN1bGF0ZUFsbERheUxheW91dCIsInVzZURyb3BwYWJsZSIsIkFsbERheVJvdyIsInNlbGVjdGVkRGF0ZSIsInNlZ21lbnRzIiwic2VsZWN0ZWRFdmVudCIsInNldFNlbGVjdGVkRXZlbnQiLCJoYW5kbGVFdmVudENsaWNrIiwiY2FuRWRpdERhdGEiLCJvcGVuQWRkRXZlbnRGb3JtIiwidmlldyIsImFjdGl2ZURyYWdEYXRhIiwiZGF5Vmlld0hvb2siLCJpZCIsImRhdGEiLCJkYXRlIiwidHlwZSIsIndlZWtEYXkxIiwid2Vla1N0YXJ0c09uIiwid2Vla0RheTIiLCJ3ZWVrRGF5MyIsIndlZWtEYXk0Iiwid2Vla0RheTUiLCJ3ZWVrRGF5NiIsIndlZWtEYXk3Iiwid2Vla1ZpZXdIb29rcyIsImxlbmd0aCIsInJlbmRlckRheVZpZXciLCJkaXYiLCJjbGFzc05hbWUiLCJyZWYiLCJzZXROb2RlUmVmIiwiaXNPdmVyIiwic2xpY2UiLCJtYXAiLCJzZWdtZW50Iiwic3R5bGUiLCJoZWlnaHQiLCJ3aWR0aCIsIm9uQ2xpY2siLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwib3JpZ2luYWxFdmVudElkIiwib3JpZ2luYWxFdmVudCIsImlzRHJhZ2dpbmciLCJwYXlsb2FkIiwicmVuZGVyV2Vla1ZpZXciLCJ3ZWVrRGF5cyIsIkFycmF5IiwiZnJvbSIsIl8iLCJpIiwic3Bhbm5pbmdFdmVudHMiLCJldmVudEdyb3VwcyIsIk1hcCIsImZvckVhY2giLCJpc011bHRpRGF5IiwiaXNBbGxEYXkiLCJldmVudElkIiwiaGFzIiwic2V0IiwiZ2V0IiwicHVzaCIsInNwYW5uaW5nIiwiZXZlbnRTZWdtZW50cyIsInNvcnQiLCJhIiwiYiIsImdldFRpbWUiLCJmaXJzdFNlZ21lbnRJbldlZWsiLCJsYXN0U2VnbWVudEluV2VlayIsInN0YXJ0RGF5SW5kZXgiLCJmaW5kSW5kZXgiLCJkYXkiLCJlbmREYXlJbmRleCIsImNvbFNwYW4iLCJpc0VuZE9mRXZlbnQiLCJpc0xhc3RTZWdtZW50IiwicG9zaXRpb25lZEV2ZW50cyIsInBvc2l0aW9uZWQiLCJyb3dzIiwic29ydGVkRXZlbnRzIiwiZXZlbnQiLCJhc3NpZ25lZCIsInJvdyIsImV2ZXJ5Iiwicm93RXZlbnQiLCJ2aXNpYmxlU2VnbWVudHMiLCJtb3JlQ291bnQiLCJ2aXNpYmxlRXZlbnRzIiwiZmlsdGVyIiwicCIsInNvbWUiLCJzIiwiaGFzTW9yZSIsImZpcnN0RXZlbnREYXlJbmRleCIsIk1hdGgiLCJtaW4iLCJtYXhSb3dzIiwibWF4Iiwicm93SGVpZ2h0IiwiZGlzcGxheVJvd3MiLCJ0b3RhbEhlaWdodCIsImRhdGEtYWxsLWRheS1yb3ciLCJkYXlJbmRleCIsImhvb2siLCJvbkRvdWJsZUNsaWNrIiwibmV3RGF0ZSIsIkRhdGUiLCJzZXRIb3VycyIsInNwYW5uaW5nRXZlbnQiLCJ0b3AiLCJsZWZ0IiwicmlnaHQiLCJjb25zb2xlIiwibG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/allday.tsx\n"));

/***/ })

});