"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/views/layout",{

/***/ "(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx":
/*!***************************************************!*\
  !*** ./src/components/custom-ui/mentionInput.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MentionInput: function() { return /* binding */ MentionInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _mentionInput_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mentionInput.css */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.css\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction MentionInput(param) {\n    let { keyMap, value, defaultValue = \"\", onChange, onBlur, onDebounceChange, debounceTimeoutMS = 500, placeholder, className, id, disabled = false, keepNewLine = false } = param;\n    var _modalState_span;\n    _s();\n    const divRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Current output value\n    const currentOutput = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    // For debounced updates\n    const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // For the mention popup\n    const [mentionMode, setMentionMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // showing mention popup?\n    const [mentionSearch, setMentionSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // the user typed in the popup search\n    const popupRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null); // popup container ref\n    const [showAbove, setShowAbove] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // whether to show dropdown above input\n    const [popupPosition, setPopupPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        left: 0\n    }); // absolute position for portal\n    // Store the location of the \"@\" that triggered the popup.\n    const [atRange, setAtRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Ref for the mention popup search input.\n    const mentionInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Used to temporarily bypass the controlled innerHTML update (which can reset the caret)\n    const skipInnerHtmlUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Modal state for setting a default value for a mention.\n    const [modalState, setModalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        span: null,\n        mentionKey: \"\",\n        currentDefault: \"\"\n    });\n    const [draftDefault, setDraftDefault] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // console.log(\"MentionInput:\", {popupPosition, mentionMode, atRange})\n    // Convert final text value to HTML with mention spans.\n    function parseTextToHtml(text) {\n        text = String(text || \"\");\n        const mentionRegex = /\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}/g;\n        let lastIndex = 0;\n        let resultHtml = \"\";\n        for (const match of text.matchAll(mentionRegex)){\n            var _match_index;\n            const start = (_match_index = match.index) !== null && _match_index !== void 0 ? _match_index : 0;\n            if (start > lastIndex) {\n                let plain = safeHtml(text.slice(lastIndex, start));\n                if (keepNewLine) {\n                    plain = plain.replace(/\\n/g, \"<br>\");\n                }\n                resultHtml += plain;\n            }\n            const mentionKey = match[1];\n            const mentionDefault = match[2] || \"\";\n            // Find case-insensitive key match\n            const actualKey = findCaseInsensitiveKey(keyMap, mentionKey);\n            const info = actualKey ? keyMap[actualKey] : null;\n            if (info) {\n                let label = info.label;\n                if (mentionDefault.trim() !== \"\") {\n                    label += \" / \".concat(mentionDefault);\n                }\n                const dataValue = mentionDefault.trim() === \"\" ? \"{{\".concat(actualKey, \"}}\") : \"{{\".concat(actualKey, \"/\").concat(mentionDefault, \"}}\");\n                const spanHtml = '<span contenteditable=\"false\" data-tag=\"'.concat(escapeAttr(dataValue), '\">').concat(safeHtml(label), \"</span>\");\n                resultHtml += spanHtml;\n            } else {\n                resultHtml += safeHtml(match[0]);\n            }\n            lastIndex = start + match[0].length;\n        }\n        if (lastIndex < text.length) {\n            let plain = safeHtml(text.slice(lastIndex));\n            if (keepNewLine) {\n                plain = plain.replace(/\\n/g, \"<br>\");\n            }\n            resultHtml += plain;\n        }\n        return resultHtml;\n    }\n    // Helper function for case-insensitive key lookup\n    function findCaseInsensitiveKey(obj, key) {\n        const lowerKey = key.toLowerCase();\n        for (const k of Object.keys(obj)){\n            if (k.toLowerCase() === lowerKey) {\n                return k;\n            }\n        }\n        return null;\n    }\n    function safeHtml(str) {\n        return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n    }\n    function escapeAttr(str) {\n        return str.replace(/\"/g, \"&quot;\").replace(/'/g, \"&#39;\");\n    }\n    // For uncontrolled mode: on first mount fill innerHTML with defaultValue.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value === undefined && defaultValue !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(defaultValue || \"\");\n            if (currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        defaultValue,\n        value,\n        keyMap\n    ]);\n    // For controlled mode: update innerHTML when value changes.\n    // We skip this update immediately after a mention insertion if necessary.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value !== undefined && divRef.current && !skipInnerHtmlUpdate.current) {\n            const currentHtml = divRef.current.innerHTML;\n            const newHtml = parseTextToHtml(value);\n            // Only update innerHTML if the div is not focused (to preserve the caret position)\n            if (document.activeElement !== divRef.current && currentHtml !== newHtml) {\n                divRef.current.innerHTML = newHtml;\n            }\n        }\n    }, [\n        value,\n        keyMap\n    ]);\n    // Build the processed final text from innerHTML.\n    function getFinalOutput() {\n        if (!divRef.current) return \"\";\n        let result = \"\";\n        function traverse(node) {\n            if (node.nodeType === Node.TEXT_NODE) {\n                result += node.nodeValue || \"\";\n            } else if (node.nodeType === Node.ELEMENT_NODE) {\n                const el = node;\n                if (el.hasAttribute(\"data-tag\")) {\n                    result += el.getAttribute(\"data-tag\");\n                } else if (el.nodeName === \"BR\") {\n                    if (keepNewLine) result += \"\\n\";\n                } else {\n                    Array.from(el.childNodes).forEach((child)=>traverse(child));\n                    if (keepNewLine && (el.nodeName === \"DIV\" || el.nodeName === \"P\")) {\n                        result += \"\\n\";\n                    }\n                }\n            }\n        }\n        Array.from(divRef.current.childNodes).forEach((child)=>traverse(child));\n        return result;\n    }\n    // Update value and trigger onChange immediately\n    function updateValue() {\n        const output = getFinalOutput();\n        currentOutput.current = output;\n        onChange === null || onChange === void 0 ? void 0 : onChange(output);\n        // Set up debounced update\n        if (onDebounceChange) {\n            if (debounceTimerRef.current) {\n                clearTimeout(debounceTimerRef.current);\n            }\n            debounceTimerRef.current = setTimeout(()=>{\n                onDebounceChange(output);\n                debounceTimerRef.current = null;\n            }, debounceTimeoutMS);\n        }\n    }\n    // Handle blur event\n    function handleBlur() {\n        if (disabled) return;\n        if (mentionMode) return;\n        onBlur === null || onBlur === void 0 ? void 0 : onBlur(currentOutput.current || getFinalOutput());\n    }\n    function onInput() {\n        if (disabled) return;\n        updateValue();\n    }\n    //////////////////////////////////////////////////////////////////////////\n    // Mention popup logic\n    //////////////////////////////////////////////////////////////////////////\n    // When the user types \"@\", let it insert normally but store the current Range.\n    function onKeyDown(e) {\n        if (disabled) return;\n        if (e.key === \"@\") {\n            var _window_getSelection, _window;\n            const sel = (_window_getSelection = (_window = window).getSelection) === null || _window_getSelection === void 0 ? void 0 : _window_getSelection.call(_window);\n            if (sel && sel.rangeCount > 0) {\n                // Save a clone of the current range where \"@\" was inserted.\n                const currentRange = sel.getRangeAt(0).cloneRange();\n                setAtRange(currentRange);\n                // Calculate position immediately to prevent flash\n                if (divRef.current) {\n                    const inputRect = divRef.current.getBoundingClientRect();\n                    const spaceBelow = window.innerHeight - inputRect.bottom;\n                    const spaceAbove = inputRect.top;\n                    const dropdownHeight = 250;\n                    const minSpaceRequired = 100;\n                    const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                    setShowAbove(shouldShowAbove);\n                    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                    const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                    setPopupPosition({\n                        left: inputRect.left + scrollX,\n                        top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                    });\n                }\n            }\n            setMentionMode(true);\n            setMentionSearch(\"\");\n        }\n    }\n    // Close the mention popup.\n    function closeMentionPopup() {\n        setMentionMode(false);\n        setMentionSearch(\"\");\n        setAtRange(null);\n    }\n    // Enhanced fuzzy filtering that checks both keys and labels\n    function fuzzyFilter(query, items) {\n        if (!query.trim()) return items.slice(0, 30);\n        const normalizedQuery = query.replace(/\\s+/g, \"\").toLowerCase();\n        const results = [];\n        for (const key of items){\n            const info = keyMap[key];\n            const normalizedKey = key.replace(/\\s+/g, \"\").toLowerCase();\n            const normalizedLabel = info.label.replace(/\\s+/g, \"\").toLowerCase();\n            // Rank priorities (lower is better):\n            // 1: Key starts with query\n            // 2: Label starts with query\n            // 3: Key contains query\n            // 4: Label contains query\n            if (normalizedKey.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 1\n                });\n            } else if (normalizedLabel.startsWith(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 2\n                });\n            } else if (normalizedKey.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 3\n                });\n            } else if (normalizedLabel.includes(normalizedQuery)) {\n                results.push({\n                    item: key,\n                    rank: 4\n                });\n            }\n        }\n        results.sort((a, b)=>a.rank - b.rank);\n        return results.map((r)=>r.item).slice(0, 30);\n    }\n    const allKeys = Object.keys(keyMap);\n    const mentionMatches = mentionMode ? fuzzyFilter(mentionSearch, allKeys) : [];\n    // When a mention is selected from the popup.\n    function onMentionSelect(mentionKey) {\n        var _span_parentNode;\n        if (!divRef.current) return;\n        const sel = window.getSelection();\n        if (!sel) {\n            closeMentionPopup();\n            return;\n        }\n        // Always use the stored range if available.\n        let range = atRange || (sel.rangeCount > 0 ? sel.getRangeAt(0) : null);\n        if (!range) {\n            closeMentionPopup();\n            return;\n        }\n        // Helper: Search backwards in a text node from a given offset to remove \"@\".\n        function removeAtFromTextNode(textNode, pos) {\n            const text = textNode.data;\n            const searchStart = Math.max(0, pos - 5);\n            const searchEnd = Math.min(text.length, pos + 5);\n            for(let i = searchEnd - 1; i >= searchStart; i--){\n                if (text.charAt(i) === \"@\") {\n                    textNode.data = text.substring(0, i) + text.substring(i + 1);\n                    const newRange = document.createRange();\n                    newRange.setStart(textNode, i);\n                    newRange.collapse(true);\n                    return newRange;\n                }\n            }\n            return null;\n        }\n        // Try to remove \"@\" from the current text node.\n        if (range.startContainer.nodeType === Node.TEXT_NODE) {\n            const textNode = range.startContainer;\n            const pos = range.startOffset;\n            const newRng = removeAtFromTextNode(textNode, pos);\n            if (newRng) {\n                range = newRng;\n            }\n        } else {\n            // If not a text node, check previous sibling (if text) from the current container.\n            const container = range.startContainer;\n            if (container.childNodes.length > 0 && range.startOffset > 0) {\n                const prev = container.childNodes[range.startOffset - 1];\n                if (prev && prev.nodeType === Node.TEXT_NODE) {\n                    const textNode = prev;\n                    const newRng = removeAtFromTextNode(textNode, textNode.data.length);\n                    if (newRng) {\n                        range = newRng;\n                    }\n                }\n            }\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeMentionPopup();\n            return;\n        }\n        // Create and insert the mention span.\n        const span = document.createElement(\"span\");\n        span.contentEditable = \"false\";\n        span.setAttribute(\"data-tag\", info.tag);\n        span.textContent = info.label;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        skipInnerHtmlUpdate.current = true;\n        range.insertNode(span);\n        // Insert a zero-width space filler node after the mention span.\n        const filler = document.createTextNode(\"​\");\n        (_span_parentNode = span.parentNode) === null || _span_parentNode === void 0 ? void 0 : _span_parentNode.insertBefore(filler, span.nextSibling);\n        // Position the cursor after the filler node.\n        const newRange = document.createRange();\n        newRange.setStartAfter(filler);\n        newRange.collapse(true);\n        sel.removeAllRanges();\n        sel.addRange(newRange);\n        divRef.current.focus();\n        closeMentionPopup();\n        updateValue();\n        setTimeout(()=>{\n            skipInnerHtmlUpdate.current = false;\n        }, 0);\n    }\n    // Close mention popup on ESC key or outside click.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function onKey(e) {\n            if (e.key === \"Escape\") {\n                closeMentionPopup();\n            }\n        }\n        function onClickOutside(e) {\n            const target = e.target;\n            // Check if click is outside both the popup and the input field\n            if (popupRef.current && !popupRef.current.contains(target) && divRef.current && !divRef.current.contains(target)) {\n                closeMentionPopup();\n            }\n        }\n        if (mentionMode) {\n            document.addEventListener(\"keydown\", onKey);\n            document.addEventListener(\"mousedown\", onClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", onKey);\n            document.removeEventListener(\"mousedown\", onClickOutside);\n        };\n    }, [\n        mentionMode\n    ]);\n    // Autofocus the mention popup search input when it opens.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode) {\n            requestAnimationFrame(()=>{\n                var _mentionInputRef_current;\n                (_mentionInputRef_current = mentionInputRef.current) === null || _mentionInputRef_current === void 0 ? void 0 : _mentionInputRef_current.focus();\n            });\n        }\n    }, [\n        mentionMode\n    ]);\n    // Update position on window resize or scroll\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mentionMode && divRef.current) {\n            const updatePosition = ()=>{\n                var _divRef_current;\n                const inputRect = (_divRef_current = divRef.current) === null || _divRef_current === void 0 ? void 0 : _divRef_current.getBoundingClientRect();\n                if (!inputRect) return;\n                const spaceBelow = window.innerHeight - inputRect.bottom;\n                const spaceAbove = inputRect.top;\n                const dropdownHeight = 250;\n                const minSpaceRequired = 100;\n                const shouldShowAbove = spaceBelow < minSpaceRequired && spaceAbove > spaceBelow;\n                setShowAbove(shouldShowAbove);\n                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;\n                const scrollY = window.pageYOffset || document.documentElement.scrollTop;\n                setPopupPosition({\n                    left: inputRect.left + scrollX,\n                    top: shouldShowAbove ? inputRect.top + scrollY - dropdownHeight - 2 : inputRect.bottom + scrollY + 2\n                });\n            };\n            // Only listen for resize and scroll events, don't run immediately\n            window.addEventListener(\"resize\", updatePosition);\n            window.addEventListener(\"scroll\", updatePosition, true);\n            return ()=>{\n                window.removeEventListener(\"resize\", updatePosition);\n                window.removeEventListener(\"scroll\", updatePosition, true);\n            };\n        }\n    }, [\n        mentionMode\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Double-click mention => open a modal for editing default.\n    //////////////////////////////////////////////////////////////////////////\n    function openDefaultModal(span) {\n        if (disabled) return;\n        const mentionValue = span.getAttribute(\"data-tag\") || \"\";\n        const pattern = /^\\{\\{([^/}]+)(?:\\/(.*?))?\\}\\}\\s*$/;\n        const match = mentionValue.match(pattern);\n        if (!match) return;\n        const mentionKey = match[1];\n        const existingDefault = match[2] || \"\";\n        setModalState({\n            isOpen: true,\n            span,\n            mentionKey,\n            currentDefault: existingDefault\n        });\n        setDraftDefault(existingDefault);\n    }\n    function confirmDefault() {\n        const { span, mentionKey } = modalState;\n        if (!span) {\n            closeModal();\n            return;\n        }\n        const info = keyMap[mentionKey];\n        if (!info) {\n            closeModal();\n            return;\n        }\n        const userDefault = draftDefault.trim();\n        let newValue = \"{{\".concat(mentionKey, \"}}\");\n        let newLabel = info.label;\n        if (userDefault !== \"\") {\n            newValue = \"{{\".concat(mentionKey, \"/\").concat(userDefault, \"}}\");\n            newLabel = \"\".concat(info.label, \" / \").concat(userDefault);\n        }\n        span.setAttribute(\"data-tag\", newValue);\n        span.textContent = newLabel;\n        span.ondblclick = ()=>{\n            openDefaultModal(span);\n        };\n        updateValue();\n        closeModal();\n    }\n    function closeModal() {\n        setModalState({\n            isOpen: false,\n            span: null,\n            mentionKey: \"\",\n            currentDefault: \"\"\n        });\n    }\n    // Ensure existing mention spans are clickable to open the modal.\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!divRef.current || disabled) return;\n        const mentionSpans = divRef.current.querySelectorAll(\"span[data-tag]\");\n        mentionSpans.forEach((el)=>{\n            const span = el;\n            if (!span.ondblclick) {\n                span.ondblclick = ()=>{\n                    openDefaultModal(span);\n                };\n            }\n        });\n    }, [\n        disabled\n    ]);\n    //////////////////////////////////////////////////////////////////////////\n    // Render\n    //////////////////////////////////////////////////////////////////////////\n    // console.log(\"Render:\", {keyMap, value, defaultValue})\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full mI relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: id,\n                ref: divRef,\n                // border border-neutral-300 rounded-none text-xs font-medium focus:border-black focus-within:border-black group\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"border border-neutral-300 overflow-hidden rounded-none px-3 py-1 shadow-sm text-xs outline-none focus-within:border-black min-h-7 font-medium leading-6\", className),\n                contentEditable: !disabled,\n                style: {\n                    whiteSpace: \"pre-wrap\"\n                },\n                suppressContentEditableWarning: true,\n                \"data-placeholder\": !disabled ? placeholder : \"\",\n                onInput: onInput,\n                onKeyDown: onKeyDown,\n                onBlur: handleBlur,\n                \"aria-disabled\": disabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 584,\n                columnNumber: 13\n            }, this),\n            mentionMode && !disabled && typeof document !== \"undefined\" && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: popupRef,\n                className: \"fixed z-[999999] bg-white border rounded-none shadow text-xs min-w-80 max-w-2xl\",\n                style: {\n                    left: \"\".concat(popupPosition.left, \"px\"),\n                    top: \"\".concat(popupPosition.top, \"px\"),\n                    maxHeight: showAbove ? \"250px\" : \"250px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col max-h-60\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-none\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: mentionInputRef,\n                                className: \"border-b p-2.5 font-medium w-full outline-none\",\n                                placeholder: \"Search for mention...\",\n                                value: mentionSearch,\n                                onChange: (e)=>setMentionSearch(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-auto flex-1\",\n                            children: mentionMatches.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 font-medium text-sm italic p-2.5\",\n                                children: \"No results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 33\n                            }, this) : mentionMatches.map((mKey)=>{\n                                const info = keyMap[mKey];\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"cursor-pointer hover:bg-neutral-100 p-2.5 font-medium text-xs overflow-hidden text-ellipsis whitespace-nowrap\",\n                                    onMouseDown: (e)=>{\n                                        e.preventDefault();\n                                        onMentionSelect(mKey);\n                                    },\n                                    children: info.label\n                                }, mKey, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 42\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                lineNumber: 600,\n                columnNumber: 17\n            }, this), document.body),\n            modalState.isOpen && !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                    defaultOpen: true,\n                    onOpenChange: closeModal,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                        className: \"max-w-[600px] !rounded-none p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    className: \"font-bold\",\n                                    children: [\n                                        \"Set default for \",\n                                        ((_modalState_span = modalState.span) === null || _modalState_span === void 0 ? void 0 : _modalState_span.innerText) || modalState.mentionKey\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2 py-4 pt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col flex-1 gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                className: \"text-xs font-medium leading-6 text-gray-900\",\n                                                children: [\n                                                    'Current default: \"',\n                                                    modalState.currentDefault,\n                                                    '\" (leave blank to remove)'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 653,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                type: \"text\",\n                                                autoCapitalize: \"none\",\n                                                autoCorrect: \"off\",\n                                                autoComplete: \"workflow-input-name\",\n                                                value: draftDefault,\n                                                placeholder: \"Type new default...\",\n                                                onChange: (e)=>setDraftDefault(e.target.value),\n                                                className: \"rounded-none text-xs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            onClick: confirmDefault,\n                                            className: \"text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 mt-1\",\n                                            children: \"Confirm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n                    lineNumber: 646,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\custom-ui\\\\mentionInput.tsx\",\n        lineNumber: 583,\n        columnNumber: 9\n    }, this);\n}\n_s(MentionInput, \"C4RIgA4damfizFcXrLqcY529xaE=\");\n_c = MentionInput;\nvar _c;\n$RefreshReg$(_c, \"MentionInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbS11aS9tZW50aW9uSW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUQ7QUFDbEI7QUFDWjtBQUM2RDtBQUM1QztBQUNBO0FBQ0U7QUFDZjtBQThCeEIsU0FBU2EsYUFBYSxLQWFvQjtRQWJwQixFQUNJQyxNQUFNLEVBQ05DLEtBQUssRUFDTEMsZUFBZSxFQUFFLEVBQ2pCQyxRQUFRLEVBQ1JDLE1BQU0sRUFDTkMsZ0JBQWdCLEVBQ2hCQyxvQkFBb0IsR0FBRyxFQUN2QkMsV0FBVyxFQUNYQyxTQUFTLEVBQ1RDLEVBQUUsRUFDRkMsV0FBVyxLQUFLLEVBQ2hCQyxjQUFjLEtBQUssRUFDSCxHQWJwQjtRQW1tQm1EQzs7SUFybEI1RSxNQUFNQyxTQUFTekIsNkNBQU1BLENBQWlCO0lBRXRDLHVCQUF1QjtJQUN2QixNQUFNMEIsZ0JBQWdCMUIsNkNBQU1BLENBQVM7SUFFckMsd0JBQXdCO0lBQ3hCLE1BQU0yQixtQkFBbUIzQiw2Q0FBTUEsQ0FBd0I7SUFHdkQsd0JBQXdCO0lBQ3hCLE1BQU0sQ0FBQzRCLGFBQWFDLGVBQWUsR0FBRzVCLCtDQUFRQSxDQUFDLFFBQVEseUJBQXlCO0lBQ2hGLE1BQU0sQ0FBQzZCLGVBQWVDLGlCQUFpQixHQUFHOUIsK0NBQVFBLENBQUMsS0FBSyxxQ0FBcUM7SUFDN0YsTUFBTStCLFdBQVdoQyw2Q0FBTUEsQ0FBaUIsT0FBTyxzQkFBc0I7SUFDckUsTUFBTSxDQUFDaUMsV0FBV0MsYUFBYSxHQUFHakMsK0NBQVFBLENBQUMsUUFBUSx1Q0FBdUM7SUFDMUYsTUFBTSxDQUFDa0MsZUFBZUMsaUJBQWlCLEdBQUduQywrQ0FBUUEsQ0FBQztRQUFFb0MsS0FBSztRQUFHQyxNQUFNO0lBQUUsSUFBSSwrQkFBK0I7SUFFeEcsMERBQTBEO0lBQzFELE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHdkMsK0NBQVFBLENBQWU7SUFDckQsMENBQTBDO0lBQzFDLE1BQU13QyxrQkFBa0J6Qyw2Q0FBTUEsQ0FBbUI7SUFFakQseUZBQXlGO0lBQ3pGLE1BQU0wQyxzQkFBc0IxQyw2Q0FBTUEsQ0FBQztJQUVuQyx5REFBeUQ7SUFDekQsTUFBTSxDQUFDd0IsWUFBWW1CLGNBQWMsR0FBRzFDLCtDQUFRQSxDQUFvQjtRQUM1RDJDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLGdCQUFnQjtJQUNwQjtJQUNBLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdoRCwrQ0FBUUEsQ0FBQztJQUlqRCxzRUFBc0U7SUFFdEUsdURBQXVEO0lBQ3ZELFNBQVNpRCxnQkFBZ0JDLElBQVk7UUFDakNBLE9BQU9DLE9BQU9ELFFBQVE7UUFDdEIsTUFBTUUsZUFBZTtRQUNyQixJQUFJQyxZQUFZO1FBQ2hCLElBQUlDLGFBQWE7UUFFakIsS0FBSyxNQUFNQyxTQUFTTCxLQUFLTSxRQUFRLENBQUNKLGNBQWU7Z0JBQy9CRztZQUFkLE1BQU1FLFFBQVFGLENBQUFBLGVBQUFBLE1BQU1HLEtBQUssY0FBWEgsMEJBQUFBLGVBQWU7WUFDN0IsSUFBSUUsUUFBUUosV0FBVztnQkFDbkIsSUFBSU0sUUFBUUMsU0FBU1YsS0FBS1csS0FBSyxDQUFDUixXQUFXSTtnQkFDM0MsSUFBSW5DLGFBQWE7b0JBQ2JxQyxRQUFRQSxNQUFNRyxPQUFPLENBQUMsT0FBTztnQkFDakM7Z0JBQ0FSLGNBQWNLO1lBQ2xCO1lBRUEsTUFBTWQsYUFBYVUsS0FBSyxDQUFDLEVBQUU7WUFDM0IsTUFBTVEsaUJBQWlCUixLQUFLLENBQUMsRUFBRSxJQUFJO1lBRW5DLGtDQUFrQztZQUNsQyxNQUFNUyxZQUFZQyx1QkFBdUJ0RCxRQUFRa0M7WUFDakQsTUFBTXFCLE9BQU9GLFlBQVlyRCxNQUFNLENBQUNxRCxVQUFVLEdBQUc7WUFFN0MsSUFBSUUsTUFBTTtnQkFDTixJQUFJQyxRQUFRRCxLQUFLQyxLQUFLO2dCQUN0QixJQUFJSixlQUFlSyxJQUFJLE9BQU8sSUFBSTtvQkFDOUJELFNBQVMsTUFBcUIsT0FBZko7Z0JBQ25CO2dCQUVBLE1BQU1NLFlBQ0ZOLGVBQWVLLElBQUksT0FBTyxLQUNwQixLQUFlLE9BQVZKLFdBQVUsUUFDZixLQUFrQkQsT0FBYkMsV0FBVSxLQUFrQixPQUFmRCxnQkFBZTtnQkFFM0MsTUFBTU8sV0FBVywyQ0FBcUVWLE9BQTFCVyxXQUFXRixZQUFXLE1BQW9CLE9BQWhCVCxTQUFTTyxRQUFPO2dCQUN0R2IsY0FBY2dCO1lBQ2xCLE9BQU87Z0JBQ0hoQixjQUFjTSxTQUFTTCxLQUFLLENBQUMsRUFBRTtZQUNuQztZQUVBRixZQUFZSSxRQUFRRixLQUFLLENBQUMsRUFBRSxDQUFDaUIsTUFBTTtRQUN2QztRQUVBLElBQUluQixZQUFZSCxLQUFLc0IsTUFBTSxFQUFFO1lBQ3pCLElBQUliLFFBQVFDLFNBQVNWLEtBQUtXLEtBQUssQ0FBQ1I7WUFDaEMsSUFBSS9CLGFBQWE7Z0JBQ2JxQyxRQUFRQSxNQUFNRyxPQUFPLENBQUMsT0FBTztZQUNqQztZQUNBUixjQUFjSztRQUNsQjtRQUVBLE9BQU9MO0lBQ1g7SUFFQSxrREFBa0Q7SUFDbEQsU0FBU1csdUJBQXVCUSxHQUF3QixFQUFFQyxHQUFXO1FBQ2pFLE1BQU1DLFdBQVdELElBQUlFLFdBQVc7UUFDaEMsS0FBSyxNQUFNQyxLQUFLQyxPQUFPQyxJQUFJLENBQUNOLEtBQU07WUFDOUIsSUFBSUksRUFBRUQsV0FBVyxPQUFPRCxVQUFVO2dCQUM5QixPQUFPRTtZQUNYO1FBQ0o7UUFDQSxPQUFPO0lBQ1g7SUFFQSxTQUFTakIsU0FBU29CLEdBQVc7UUFDekIsT0FBT0EsSUFDRmxCLE9BQU8sQ0FBQyxNQUFNLFNBQ2RBLE9BQU8sQ0FBQyxNQUFNLFFBQ2RBLE9BQU8sQ0FBQyxNQUFNO0lBQ3ZCO0lBRUEsU0FBU1MsV0FBV1MsR0FBVztRQUMzQixPQUFPQSxJQUFJbEIsT0FBTyxDQUFDLE1BQU0sVUFBVUEsT0FBTyxDQUFDLE1BQU07SUFDckQ7SUFFQSwwRUFBMEU7SUFDMUVoRSxnREFBU0EsQ0FBQztRQUNOLElBQUljLFVBQVVxRSxhQUFhcEUsaUJBQWlCb0UsYUFBYXpELE9BQU8wRCxPQUFPLElBQUksQ0FBQ3pDLG9CQUFvQnlDLE9BQU8sRUFBRTtZQUNyRyxNQUFNQyxjQUFjM0QsT0FBTzBELE9BQU8sQ0FBQ0UsU0FBUztZQUM1QyxNQUFNQyxVQUFVcEMsZ0JBQWdCcEMsZ0JBQWdCO1lBQ2hELElBQUlzRSxnQkFBZ0JFLFNBQVM7Z0JBQ3pCN0QsT0FBTzBELE9BQU8sQ0FBQ0UsU0FBUyxHQUFHQztZQUMvQjtRQUNKO0lBQ0osR0FBRztRQUFDeEU7UUFBY0Q7UUFBT0Q7S0FBTztJQUVoQyw0REFBNEQ7SUFDNUQsMEVBQTBFO0lBQzFFYixnREFBU0EsQ0FBQztRQUNOLElBQUljLFVBQVVxRSxhQUFhekQsT0FBTzBELE9BQU8sSUFBSSxDQUFDekMsb0JBQW9CeUMsT0FBTyxFQUFFO1lBQ3ZFLE1BQU1DLGNBQWMzRCxPQUFPMEQsT0FBTyxDQUFDRSxTQUFTO1lBQzVDLE1BQU1DLFVBQVVwQyxnQkFBZ0JyQztZQUNoQyxtRkFBbUY7WUFDbkYsSUFBSTBFLFNBQVNDLGFBQWEsS0FBSy9ELE9BQU8wRCxPQUFPLElBQUlDLGdCQUFnQkUsU0FBUztnQkFDdEU3RCxPQUFPMEQsT0FBTyxDQUFDRSxTQUFTLEdBQUdDO1lBQy9CO1FBQ0o7SUFDSixHQUFHO1FBQUN6RTtRQUFPRDtLQUFPO0lBRWxCLGlEQUFpRDtJQUNqRCxTQUFTNkU7UUFDTCxJQUFJLENBQUNoRSxPQUFPMEQsT0FBTyxFQUFFLE9BQU87UUFDNUIsSUFBSU8sU0FBUztRQUViLFNBQVNDLFNBQVNDLElBQVU7WUFDeEIsSUFBSUEsS0FBS0MsUUFBUSxLQUFLQyxLQUFLQyxTQUFTLEVBQUU7Z0JBQ2xDTCxVQUFVRSxLQUFLSSxTQUFTLElBQUk7WUFDaEMsT0FBTyxJQUFJSixLQUFLQyxRQUFRLEtBQUtDLEtBQUtHLFlBQVksRUFBRTtnQkFDNUMsTUFBTUMsS0FBS047Z0JBQ1gsSUFBSU0sR0FBR0MsWUFBWSxDQUFDLGFBQWE7b0JBQzdCVCxVQUFVUSxHQUFHRSxZQUFZLENBQUM7Z0JBQzlCLE9BQU8sSUFBSUYsR0FBR0csUUFBUSxLQUFLLE1BQU07b0JBQzdCLElBQUk5RSxhQUFhbUUsVUFBVTtnQkFDL0IsT0FBTztvQkFDSFksTUFBTUMsSUFBSSxDQUFDTCxHQUFHTSxVQUFVLEVBQUVDLE9BQU8sQ0FBQ0MsQ0FBQUEsUUFBU2YsU0FBU2U7b0JBQ3BELElBQUluRixlQUFnQjJFLENBQUFBLEdBQUdHLFFBQVEsS0FBSyxTQUFTSCxHQUFHRyxRQUFRLEtBQUssR0FBRSxHQUFJO3dCQUMvRFgsVUFBVTtvQkFDZDtnQkFDSjtZQUNKO1FBQ0o7UUFFQVksTUFBTUMsSUFBSSxDQUFDOUUsT0FBTzBELE9BQU8sQ0FBQ3FCLFVBQVUsRUFBRUMsT0FBTyxDQUFDQyxDQUFBQSxRQUFTZixTQUFTZTtRQUNoRSxPQUFPaEI7SUFDWDtJQUVBLGdEQUFnRDtJQUNoRCxTQUFTaUI7UUFDTCxNQUFNQyxTQUFTbkI7UUFDZi9ELGNBQWN5RCxPQUFPLEdBQUd5QjtRQUN4QjdGLHFCQUFBQSwrQkFBQUEsU0FBVzZGO1FBRVgsMEJBQTBCO1FBQzFCLElBQUkzRixrQkFBa0I7WUFDbEIsSUFBSVUsaUJBQWlCd0QsT0FBTyxFQUFFO2dCQUMxQjBCLGFBQWFsRixpQkFBaUJ3RCxPQUFPO1lBQ3pDO1lBRUF4RCxpQkFBaUJ3RCxPQUFPLEdBQUcyQixXQUFXO2dCQUNsQzdGLGlCQUFpQjJGO2dCQUNqQmpGLGlCQUFpQndELE9BQU8sR0FBRztZQUMvQixHQUFHakU7UUFDUDtJQUNKO0lBRUEsb0JBQW9CO0lBQ3BCLFNBQVM2RjtRQUNMLElBQUl6RixVQUFVO1FBQ2QsSUFBSU0sYUFBYTtRQUNqQlosbUJBQUFBLDZCQUFBQSxPQUFTVSxjQUFjeUQsT0FBTyxJQUFJTTtJQUN0QztJQUVBLFNBQVN1QjtRQUNMLElBQUkxRixVQUFVO1FBQ2RxRjtJQUNKO0lBRUEsMEVBQTBFO0lBQzFFLHNCQUFzQjtJQUN0QiwwRUFBMEU7SUFFMUUsK0VBQStFO0lBQy9FLFNBQVNNLFVBQVVDLENBQXNDO1FBQ3JELElBQUk1RixVQUFVO1FBQ2QsSUFBSTRGLEVBQUV2QyxHQUFHLEtBQUssS0FBSztnQkFDSHdDLHNCQUFBQTtZQUFaLE1BQU1DLE9BQU1ELHVCQUFBQSxDQUFBQSxVQUFBQSxRQUFPRSxZQUFZLGNBQW5CRiwyQ0FBQUEsMEJBQUFBO1lBQ1osSUFBSUMsT0FBT0EsSUFBSUUsVUFBVSxHQUFHLEdBQUc7Z0JBQzNCLDREQUE0RDtnQkFDNUQsTUFBTUMsZUFBZUgsSUFBSUksVUFBVSxDQUFDLEdBQUdDLFVBQVU7Z0JBQ2pEakYsV0FBVytFO2dCQUVYLGtEQUFrRDtnQkFDbEQsSUFBSTlGLE9BQU8wRCxPQUFPLEVBQUU7b0JBQ2hCLE1BQU11QyxZQUFZakcsT0FBTzBELE9BQU8sQ0FBQ3dDLHFCQUFxQjtvQkFDdEQsTUFBTUMsYUFBYVQsT0FBT1UsV0FBVyxHQUFHSCxVQUFVSSxNQUFNO29CQUN4RCxNQUFNQyxhQUFhTCxVQUFVckYsR0FBRztvQkFDaEMsTUFBTTJGLGlCQUFpQjtvQkFDdkIsTUFBTUMsbUJBQW1CO29CQUV6QixNQUFNQyxrQkFBa0JOLGFBQWFLLG9CQUFvQkYsYUFBYUg7b0JBQ3RFMUYsYUFBYWdHO29CQUViLE1BQU1DLFVBQVVoQixPQUFPaUIsV0FBVyxJQUFJN0MsU0FBUzhDLGVBQWUsQ0FBQ0MsVUFBVTtvQkFDekUsTUFBTUMsVUFBVXBCLE9BQU9xQixXQUFXLElBQUlqRCxTQUFTOEMsZUFBZSxDQUFDSSxTQUFTO29CQUV4RXJHLGlCQUFpQjt3QkFDYkUsTUFBTW9GLFVBQVVwRixJQUFJLEdBQUc2Rjt3QkFDdkI5RixLQUFLNkYsa0JBQ0NSLFVBQVVyRixHQUFHLEdBQUdrRyxVQUFVUCxpQkFBaUIsSUFDM0NOLFVBQVVJLE1BQU0sR0FBR1MsVUFBVTtvQkFDdkM7Z0JBQ0o7WUFDSjtZQUNBMUcsZUFBZTtZQUNmRSxpQkFBaUI7UUFDckI7SUFDSjtJQUVBLDJCQUEyQjtJQUMzQixTQUFTMkc7UUFDTDdHLGVBQWU7UUFDZkUsaUJBQWlCO1FBQ2pCUyxXQUFXO0lBQ2Y7SUFFQSw0REFBNEQ7SUFDNUQsU0FBU21HLFlBQVlDLEtBQWEsRUFBRUMsS0FBZTtRQUMvQyxJQUFJLENBQUNELE1BQU12RSxJQUFJLElBQUksT0FBT3dFLE1BQU0vRSxLQUFLLENBQUMsR0FBRztRQUN6QyxNQUFNZ0Ysa0JBQWtCRixNQUFNN0UsT0FBTyxDQUFDLFFBQVEsSUFBSWMsV0FBVztRQUc3RCxNQUFNa0UsVUFBb0IsRUFBRTtRQUU1QixLQUFLLE1BQU1wRSxPQUFPa0UsTUFBTztZQUNyQixNQUFNMUUsT0FBT3ZELE1BQU0sQ0FBQytELElBQUk7WUFDeEIsTUFBTXFFLGdCQUFnQnJFLElBQUlaLE9BQU8sQ0FBQyxRQUFRLElBQUljLFdBQVc7WUFDekQsTUFBTW9FLGtCQUFrQjlFLEtBQUtDLEtBQUssQ0FBQ0wsT0FBTyxDQUFDLFFBQVEsSUFBSWMsV0FBVztZQUVsRSxxQ0FBcUM7WUFDckMsMkJBQTJCO1lBQzNCLDZCQUE2QjtZQUM3Qix3QkFBd0I7WUFDeEIsMEJBQTBCO1lBRTFCLElBQUltRSxjQUFjRSxVQUFVLENBQUNKLGtCQUFrQjtnQkFDM0NDLFFBQVFJLElBQUksQ0FBQztvQkFBQ0MsTUFBTXpFO29CQUFLMEUsTUFBTTtnQkFBQztZQUNwQyxPQUFPLElBQUlKLGdCQUFnQkMsVUFBVSxDQUFDSixrQkFBa0I7Z0JBQ3BEQyxRQUFRSSxJQUFJLENBQUM7b0JBQUNDLE1BQU16RTtvQkFBSzBFLE1BQU07Z0JBQUM7WUFDcEMsT0FBTyxJQUFJTCxjQUFjTSxRQUFRLENBQUNSLGtCQUFrQjtnQkFDaERDLFFBQVFJLElBQUksQ0FBQztvQkFBQ0MsTUFBTXpFO29CQUFLMEUsTUFBTTtnQkFBQztZQUNwQyxPQUFPLElBQUlKLGdCQUFnQkssUUFBUSxDQUFDUixrQkFBa0I7Z0JBQ2xEQyxRQUFRSSxJQUFJLENBQUM7b0JBQUNDLE1BQU16RTtvQkFBSzBFLE1BQU07Z0JBQUM7WUFDcEM7UUFDSjtRQUVBTixRQUFRUSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRUgsSUFBSSxHQUFHSSxFQUFFSixJQUFJO1FBQ3RDLE9BQU9OLFFBQVFXLEdBQUcsQ0FBQyxDQUFDQyxJQUFNQSxFQUFFUCxJQUFJLEVBQUV0RixLQUFLLENBQUMsR0FBRztJQUMvQztJQUVBLE1BQU04RixVQUFVN0UsT0FBT0MsSUFBSSxDQUFDcEU7SUFDNUIsTUFBTWlKLGlCQUFpQmpJLGNBQWMrRyxZQUFZN0csZUFBZThILFdBQVcsRUFBRTtJQUU3RSw2Q0FBNkM7SUFDN0MsU0FBU0UsZ0JBQWdCaEgsVUFBa0I7WUEwRXZDRDtRQXpFQSxJQUFJLENBQUNwQixPQUFPMEQsT0FBTyxFQUFFO1FBQ3JCLE1BQU1pQyxNQUFNRCxPQUFPRSxZQUFZO1FBQy9CLElBQUksQ0FBQ0QsS0FBSztZQUNOc0I7WUFDQTtRQUNKO1FBQ0EsNENBQTRDO1FBQzVDLElBQUlxQixRQUFzQnhILFdBQVk2RSxDQUFBQSxJQUFJRSxVQUFVLEdBQUcsSUFBSUYsSUFBSUksVUFBVSxDQUFDLEtBQUssSUFBRztRQUNsRixJQUFJLENBQUN1QyxPQUFPO1lBQ1JyQjtZQUNBO1FBQ0o7UUFFQSw2RUFBNkU7UUFDN0UsU0FBU3NCLHFCQUFxQkMsUUFBYyxFQUFFQyxHQUFXO1lBQ3JELE1BQU0vRyxPQUFPOEcsU0FBU0UsSUFBSTtZQUMxQixNQUFNQyxjQUFjQyxLQUFLQyxHQUFHLENBQUMsR0FBR0osTUFBTTtZQUN0QyxNQUFNSyxZQUFZRixLQUFLRyxHQUFHLENBQUNySCxLQUFLc0IsTUFBTSxFQUFFeUYsTUFBTTtZQUM5QyxJQUFLLElBQUlPLElBQUlGLFlBQVksR0FBR0UsS0FBS0wsYUFBYUssSUFBSztnQkFDL0MsSUFBSXRILEtBQUt1SCxNQUFNLENBQUNELE9BQU8sS0FBSztvQkFDeEJSLFNBQVNFLElBQUksR0FBR2hILEtBQUt3SCxTQUFTLENBQUMsR0FBR0YsS0FBS3RILEtBQUt3SCxTQUFTLENBQUNGLElBQUk7b0JBQzFELE1BQU1HLFdBQVdyRixTQUFTc0YsV0FBVztvQkFDckNELFNBQVNFLFFBQVEsQ0FBQ2IsVUFBVVE7b0JBQzVCRyxTQUFTRyxRQUFRLENBQUM7b0JBQ2xCLE9BQU9IO2dCQUNYO1lBQ0o7WUFDQSxPQUFPO1FBQ1g7UUFFQSxnREFBZ0Q7UUFDaEQsSUFBSWIsTUFBTWlCLGNBQWMsQ0FBQ25GLFFBQVEsS0FBS0MsS0FBS0MsU0FBUyxFQUFFO1lBQ2xELE1BQU1rRSxXQUFXRixNQUFNaUIsY0FBYztZQUNyQyxNQUFNZCxNQUFNSCxNQUFNa0IsV0FBVztZQUM3QixNQUFNQyxTQUFTbEIscUJBQXFCQyxVQUFVQztZQUM5QyxJQUFJZ0IsUUFBUTtnQkFDUm5CLFFBQVFtQjtZQUNaO1FBQ0osT0FBTztZQUNILG1GQUFtRjtZQUNuRixNQUFNQyxZQUFZcEIsTUFBTWlCLGNBQWM7WUFDdEMsSUFBSUcsVUFBVTNFLFVBQVUsQ0FBQy9CLE1BQU0sR0FBRyxLQUFLc0YsTUFBTWtCLFdBQVcsR0FBRyxHQUFHO2dCQUMxRCxNQUFNRyxPQUFPRCxVQUFVM0UsVUFBVSxDQUFDdUQsTUFBTWtCLFdBQVcsR0FBRyxFQUFFO2dCQUN4RCxJQUFJRyxRQUFRQSxLQUFLdkYsUUFBUSxLQUFLQyxLQUFLQyxTQUFTLEVBQUU7b0JBQzFDLE1BQU1rRSxXQUFXbUI7b0JBQ2pCLE1BQU1GLFNBQVNsQixxQkFBcUJDLFVBQVVBLFNBQVNFLElBQUksQ0FBQzFGLE1BQU07b0JBQ2xFLElBQUl5RyxRQUFRO3dCQUNSbkIsUUFBUW1CO29CQUNaO2dCQUNKO1lBQ0o7UUFDSjtRQUVBLE1BQU0vRyxPQUFPdkQsTUFBTSxDQUFDa0MsV0FBVztRQUMvQixJQUFJLENBQUNxQixNQUFNO1lBQ1B1RTtZQUNBO1FBQ0o7UUFFQSxzQ0FBc0M7UUFDdEMsTUFBTTdGLE9BQU8wQyxTQUFTOEYsYUFBYSxDQUFDO1FBQ3BDeEksS0FBS3lJLGVBQWUsR0FBRztRQUN2QnpJLEtBQUswSSxZQUFZLENBQUMsWUFBWXBILEtBQUtxSCxHQUFHO1FBQ3RDM0ksS0FBSzRJLFdBQVcsR0FBR3RILEtBQUtDLEtBQUs7UUFDN0J2QixLQUFLNkksVUFBVSxHQUFHO1lBQ2RDLGlCQUFpQjlJO1FBQ3JCO1FBRUFILG9CQUFvQnlDLE9BQU8sR0FBRztRQUM5QjRFLE1BQU02QixVQUFVLENBQUMvSTtRQUVqQixnRUFBZ0U7UUFDaEUsTUFBTWdKLFNBQVN0RyxTQUFTdUcsY0FBYyxDQUFDO1NBQ3ZDakosbUJBQUFBLEtBQUtrSixVQUFVLGNBQWZsSix1Q0FBQUEsaUJBQWlCbUosWUFBWSxDQUFDSCxRQUFRaEosS0FBS29KLFdBQVc7UUFFdEQsNkNBQTZDO1FBQzdDLE1BQU1yQixXQUFXckYsU0FBU3NGLFdBQVc7UUFDckNELFNBQVNzQixhQUFhLENBQUNMO1FBQ3ZCakIsU0FBU0csUUFBUSxDQUFDO1FBQ2xCM0QsSUFBSStFLGVBQWU7UUFDbkIvRSxJQUFJZ0YsUUFBUSxDQUFDeEI7UUFFYm5KLE9BQU8wRCxPQUFPLENBQUNrSCxLQUFLO1FBQ3BCM0Q7UUFDQS9CO1FBRUFHLFdBQVc7WUFDUHBFLG9CQUFvQnlDLE9BQU8sR0FBRztRQUNsQyxHQUFHO0lBQ1A7SUFFQSxtREFBbUQ7SUFDbkRwRixnREFBU0EsQ0FBQztRQUNOLFNBQVN1TSxNQUFNcEYsQ0FBZ0I7WUFDM0IsSUFBSUEsRUFBRXZDLEdBQUcsS0FBSyxVQUFVO2dCQUNwQitEO1lBQ0o7UUFDSjtRQUVBLFNBQVM2RCxlQUFlckYsQ0FBYTtZQUNqQyxNQUFNc0YsU0FBU3RGLEVBQUVzRixNQUFNO1lBQ3ZCLCtEQUErRDtZQUMvRCxJQUFJeEssU0FBU21ELE9BQU8sSUFBSSxDQUFDbkQsU0FBU21ELE9BQU8sQ0FBQ3NILFFBQVEsQ0FBQ0QsV0FDL0MvSyxPQUFPMEQsT0FBTyxJQUFJLENBQUMxRCxPQUFPMEQsT0FBTyxDQUFDc0gsUUFBUSxDQUFDRCxTQUFTO2dCQUNwRDlEO1lBQ0o7UUFDSjtRQUVBLElBQUk5RyxhQUFhO1lBQ2IyRCxTQUFTbUgsZ0JBQWdCLENBQUMsV0FBV0o7WUFDckMvRyxTQUFTbUgsZ0JBQWdCLENBQUMsYUFBYUg7UUFDM0M7UUFDQSxPQUFPO1lBQ0hoSCxTQUFTb0gsbUJBQW1CLENBQUMsV0FBV0w7WUFDeEMvRyxTQUFTb0gsbUJBQW1CLENBQUMsYUFBYUo7UUFDOUM7SUFDSixHQUFHO1FBQUMzSztLQUFZO0lBRWhCLDBEQUEwRDtJQUMxRDdCLGdEQUFTQSxDQUFDO1FBQ04sSUFBSTZCLGFBQWE7WUFDYmdMLHNCQUFzQjtvQkFDbEJuSztpQkFBQUEsMkJBQUFBLGdCQUFnQjBDLE9BQU8sY0FBdkIxQywrQ0FBQUEseUJBQXlCNEosS0FBSztZQUNsQztRQUNKO0lBQ0osR0FBRztRQUFDeks7S0FBWTtJQUVoQiw2Q0FBNkM7SUFDN0M3QixnREFBU0EsQ0FBQztRQUNOLElBQUk2QixlQUFlSCxPQUFPMEQsT0FBTyxFQUFFO1lBQy9CLE1BQU0wSCxpQkFBaUI7b0JBQ0RwTDtnQkFBbEIsTUFBTWlHLGFBQVlqRyxrQkFBQUEsT0FBTzBELE9BQU8sY0FBZDFELHNDQUFBQSxnQkFBZ0JrRyxxQkFBcUI7Z0JBQ3ZELElBQUksQ0FBQ0QsV0FBVztnQkFFaEIsTUFBTUUsYUFBYVQsT0FBT1UsV0FBVyxHQUFHSCxVQUFVSSxNQUFNO2dCQUN4RCxNQUFNQyxhQUFhTCxVQUFVckYsR0FBRztnQkFDaEMsTUFBTTJGLGlCQUFpQjtnQkFDdkIsTUFBTUMsbUJBQW1CO2dCQUV6QixNQUFNQyxrQkFBa0JOLGFBQWFLLG9CQUFvQkYsYUFBYUg7Z0JBQ3RFMUYsYUFBYWdHO2dCQUViLE1BQU1DLFVBQVVoQixPQUFPaUIsV0FBVyxJQUFJN0MsU0FBUzhDLGVBQWUsQ0FBQ0MsVUFBVTtnQkFDekUsTUFBTUMsVUFBVXBCLE9BQU9xQixXQUFXLElBQUlqRCxTQUFTOEMsZUFBZSxDQUFDSSxTQUFTO2dCQUV4RXJHLGlCQUFpQjtvQkFDYkUsTUFBTW9GLFVBQVVwRixJQUFJLEdBQUc2RjtvQkFDdkI5RixLQUFLNkYsa0JBQ0NSLFVBQVVyRixHQUFHLEdBQUdrRyxVQUFVUCxpQkFBaUIsSUFDM0NOLFVBQVVJLE1BQU0sR0FBR1MsVUFBVTtnQkFDdkM7WUFDSjtZQUVBLGtFQUFrRTtZQUNsRXBCLE9BQU91RixnQkFBZ0IsQ0FBQyxVQUFVRztZQUNsQzFGLE9BQU91RixnQkFBZ0IsQ0FBQyxVQUFVRyxnQkFBZ0I7WUFFbEQsT0FBTztnQkFDSDFGLE9BQU93RixtQkFBbUIsQ0FBQyxVQUFVRTtnQkFDckMxRixPQUFPd0YsbUJBQW1CLENBQUMsVUFBVUUsZ0JBQWdCO1lBQ3pEO1FBQ0o7SUFDSixHQUFHO1FBQUNqTDtLQUFZO0lBSWhCLDBFQUEwRTtJQUMxRSw0REFBNEQ7SUFDNUQsMEVBQTBFO0lBRTFFLFNBQVMrSixpQkFBaUI5SSxJQUFxQjtRQUMzQyxJQUFJdkIsVUFBVTtRQUNkLE1BQU13TCxlQUFlakssS0FBS3VELFlBQVksQ0FBQyxlQUFlO1FBQ3RELE1BQU0yRyxVQUFVO1FBQ2hCLE1BQU12SixRQUFRc0osYUFBYXRKLEtBQUssQ0FBQ3VKO1FBQ2pDLElBQUksQ0FBQ3ZKLE9BQU87UUFFWixNQUFNVixhQUFhVSxLQUFLLENBQUMsRUFBRTtRQUMzQixNQUFNd0osa0JBQWtCeEosS0FBSyxDQUFDLEVBQUUsSUFBSTtRQUNwQ2IsY0FBYztZQUNWQyxRQUFRO1lBQ1JDO1lBQ0FDO1lBQ0FDLGdCQUFnQmlLO1FBQ3BCO1FBQ0EvSixnQkFBZ0IrSjtJQUNwQjtJQUVBLFNBQVNDO1FBQ0wsTUFBTSxFQUFDcEssSUFBSSxFQUFFQyxVQUFVLEVBQUMsR0FBR3RCO1FBQzNCLElBQUksQ0FBQ3FCLE1BQU07WUFDUHFLO1lBQ0E7UUFDSjtRQUNBLE1BQU0vSSxPQUFPdkQsTUFBTSxDQUFDa0MsV0FBVztRQUMvQixJQUFJLENBQUNxQixNQUFNO1lBQ1ArSTtZQUNBO1FBQ0o7UUFFQSxNQUFNQyxjQUFjbkssYUFBYXFCLElBQUk7UUFDckMsSUFBSStJLFdBQVcsS0FBZ0IsT0FBWHRLLFlBQVc7UUFDL0IsSUFBSXVLLFdBQVdsSixLQUFLQyxLQUFLO1FBQ3pCLElBQUkrSSxnQkFBZ0IsSUFBSTtZQUNwQkMsV0FBVyxLQUFtQkQsT0FBZHJLLFlBQVcsS0FBZSxPQUFacUssYUFBWTtZQUMxQ0UsV0FBVyxHQUFtQkYsT0FBaEJoSixLQUFLQyxLQUFLLEVBQUMsT0FBaUIsT0FBWitJO1FBQ2xDO1FBRUF0SyxLQUFLMEksWUFBWSxDQUFDLFlBQVk2QjtRQUM5QnZLLEtBQUs0SSxXQUFXLEdBQUc0QjtRQUNuQnhLLEtBQUs2SSxVQUFVLEdBQUc7WUFDZEMsaUJBQWlCOUk7UUFDckI7UUFFQThEO1FBQ0F1RztJQUNKO0lBRUEsU0FBU0E7UUFDTHZLLGNBQWM7WUFDVkMsUUFBUTtZQUNSQyxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsZ0JBQWdCO1FBQ3BCO0lBQ0o7SUFFQSxpRUFBaUU7SUFDakVoRCxnREFBU0EsQ0FBQztRQUNOLElBQUksQ0FBQzBCLE9BQU8wRCxPQUFPLElBQUk3RCxVQUFVO1FBQ2pDLE1BQU1nTSxlQUFlN0wsT0FBTzBELE9BQU8sQ0FBQ29JLGdCQUFnQixDQUFDO1FBQ3JERCxhQUFhN0csT0FBTyxDQUFDLENBQUNQO1lBQ2xCLE1BQU1yRCxPQUFPcUQ7WUFDYixJQUFJLENBQUNyRCxLQUFLNkksVUFBVSxFQUFFO2dCQUNsQjdJLEtBQUs2SSxVQUFVLEdBQUc7b0JBQ2RDLGlCQUFpQjlJO2dCQUNyQjtZQUNKO1FBQ0o7SUFDSixHQUFHO1FBQUN2QjtLQUFTO0lBRWIsMEVBQTBFO0lBQzFFLFNBQVM7SUFDVCwwRUFBMEU7SUFFMUUsd0RBQXdEO0lBRXhELHFCQUNJLDhEQUFDa007UUFBSXBNLFdBQVU7OzBCQUNYLDhEQUFDb007Z0JBQ0duTSxJQUFJQTtnQkFDSm9NLEtBQUtoTTtnQkFDTCxnSEFBZ0g7Z0JBQ2hITCxXQUFXViw4Q0FBRUEsQ0FBRSwySkFBMEpVO2dCQUN6S2tLLGlCQUFpQixDQUFDaEs7Z0JBQ2xCb00sT0FBTztvQkFBQ0MsWUFBWTtnQkFBVTtnQkFDOUJDLDhCQUE4QjtnQkFDOUJDLG9CQUFrQixDQUFDdk0sV0FBV0gsY0FBYztnQkFDNUM2RixTQUFTQTtnQkFDVEMsV0FBV0E7Z0JBQ1hqRyxRQUFRK0Y7Z0JBQ1IrRyxpQkFBZXhNOzs7Ozs7WUFHbEJNLGVBQWUsQ0FBQ04sWUFBWSxPQUFPaUUsYUFBYSw2QkFBZXJGLHVEQUFZQSxlQUN4RSw4REFBQ3NOO2dCQUNHQyxLQUFLekw7Z0JBQ0xaLFdBQVU7Z0JBQ1ZzTSxPQUFPO29CQUNIcEwsTUFBTSxHQUFzQixPQUFuQkgsY0FBY0csSUFBSSxFQUFDO29CQUM1QkQsS0FBSyxHQUFxQixPQUFsQkYsY0FBY0UsR0FBRyxFQUFDO29CQUMxQjBMLFdBQVc5TCxZQUFZLFVBQVU7Z0JBQ3JDOzBCQUNBLDRFQUFDdUw7b0JBQUlwTSxXQUFVOztzQ0FDWCw4REFBQ29NOzRCQUFJcE0sV0FBVTtzQ0FDWCw0RUFBQzRNO2dDQUNHUCxLQUFLaEw7Z0NBQ0xyQixXQUFVO2dDQUNWRCxhQUFZO2dDQUNaTixPQUFPaUI7Z0NBQ1BmLFVBQVUsQ0FBQ21HLElBQU1uRixpQkFBaUJtRixFQUFFc0YsTUFBTSxDQUFDM0wsS0FBSzs7Ozs7Ozs7Ozs7c0NBR3hELDhEQUFDMk07NEJBQUlwTSxXQUFVO3NDQUNWeUksZUFBZXBGLE1BQU0sS0FBSyxrQkFDdkIsOERBQUMrSTtnQ0FBSXBNLFdBQVU7MENBQW9EOzs7Ozt1Q0FJbEV5SSxlQUFlSCxHQUFHLENBQUMsQ0FBQ3VFO2dDQUNoQixNQUFNOUosT0FBT3ZELE1BQU0sQ0FBQ3FOLEtBQUs7Z0NBQ3pCLHFCQUNJLDhEQUFDVDtvQ0FFR3BNLFdBQVU7b0NBQ1Y4TSxhQUFhLENBQUNoSDt3Q0FDVkEsRUFBRWlILGNBQWM7d0NBQ2hCckUsZ0JBQWdCbUU7b0NBQ3BCOzhDQUNDOUosS0FBS0MsS0FBSzttQ0FOTjZKOzs7Ozs0QkFTakI7Ozs7Ozs7Ozs7Ozs7Ozs7c0JBS2pCMUksU0FBUzZJLElBQUk7WUFHaEI1TSxXQUFXb0IsTUFBTSxJQUFJLENBQUN0QiwwQkFBWTswQkFDL0IsNEVBQUNuQix5REFBTUE7b0JBQUNrTyxXQUFXO29CQUFDQyxjQUFjcEI7OEJBQzlCLDRFQUFDOU0sZ0VBQWFBO3dCQUFDZ0IsV0FBVTs7MENBQ3JCLDhEQUFDZiwrREFBWUE7MENBQ1QsNEVBQUNDLDhEQUFXQTtvQ0FBQ2MsV0FBVTs7d0NBQVk7d0NBQWlCSSxFQUFBQSxtQkFBQUEsV0FBV3FCLElBQUksY0FBZnJCLHVDQUFBQSxpQkFBaUIrTSxTQUFTLEtBQUkvTSxXQUFXc0IsVUFBVTs7Ozs7Ozs7Ozs7OzBDQUUzRyw4REFBQzBLO2dDQUFJcE0sV0FBVTs7a0RBQ1gsOERBQUNvTTt3Q0FBSXBNLFdBQVU7OzBEQUNYLDhEQUFDYix1REFBS0E7Z0RBQUNhLFdBQVU7O29EQUE4QztvREFDeENJLFdBQVd1QixjQUFjO29EQUFDOzs7Ozs7OzBEQUVqRCw4REFBQ3ZDLHVEQUFLQTtnREFDRmdPLE1BQUs7Z0RBQ0xDLGdCQUFlO2dEQUNmQyxhQUFZO2dEQUNaQyxjQUFlO2dEQUNmOU4sT0FBT21DO2dEQUNQN0IsYUFBWTtnREFDWkosVUFBVSxDQUFDbUcsSUFBTWpFLGdCQUFnQmlFLEVBQUVzRixNQUFNLENBQUMzTCxLQUFLO2dEQUMvQ08sV0FBVTs7Ozs7Ozs7Ozs7O2tEQUdsQiw4REFBQ29NO2tEQUNHLDRFQUFDL00seURBQU1BOzRDQUNIbU8sU0FBUzNCOzRDQUNUN0wsV0FBVTtzREFBdUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXJIO0dBbG9CZ0JUO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2N1c3RvbS11aS9tZW50aW9uSW5wdXQudHN4P2IzOGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7dXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHtjcmVhdGVQb3J0YWx9IGZyb20gXCJyZWFjdC1kb21cIjtcclxuaW1wb3J0IFwiLi9tZW50aW9uSW5wdXQuY3NzXCJcclxuaW1wb3J0IHtEaWFsb2csIERpYWxvZ0NvbnRlbnQsIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGV9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZGlhbG9nXCI7XHJcbmltcG9ydCB7TGFiZWx9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvbGFiZWxcIjtcclxuaW1wb3J0IHtJbnB1dH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xyXG5pbXBvcnQge0J1dHRvbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHtjbn0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XHJcblxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBNZW50aW9uSW5mbyB7XHJcbiAgICBsYWJlbDogc3RyaW5nO1xyXG4gICAgdGFnOiBzdHJpbmc7IC8vIGUuZy4gXCJ7e2ZpcnN0TmFtZX19XCJcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBNZW50aW9uSW5wdXRQcm9wcyB7XHJcbiAgICBrZXlNYXA6IFJlY29yZDxzdHJpbmcsIE1lbnRpb25JbmZvPjsgLy8geyBba2V5XTogeyBsYWJlbCwgdmFsdWUgfSB9XHJcbiAgICB2YWx1ZT86IHN0cmluZzsgLy8gQ29udHJvbGxlZCB1c2FnZSAoZmluYWwgdGV4dCB3aXRoIHBsYWNlaG9sZGVycylcclxuICAgIGRlZmF1bHRWYWx1ZT86IHN0cmluZzsgLy8gVW5jb250cm9sbGVkIHVzYWdlIChpbml0aWFsIEhUTUwpXHJcbiAgICBvbkNoYW5nZT86IChmaW5hbFRleHQ6IHN0cmluZykgPT4gdm9pZDsgLy8gZmluYWwgcHJvY2Vzc2VkIHRleHRcclxuICAgIHBsYWNlaG9sZGVyPzogc3RyaW5nO1xyXG4gICAgY2xhc3NOYW1lPzogc3RyaW5nO1xyXG4gICAgZGlzYWJsZWQ/OiBib29sZWFuOyAvLyBBZGQgZGlzYWJsZWQgcHJvcFxyXG4gICAga2VlcE5ld0xpbmU/OiBib29sZWFuOyAvLyBBZGQga2VlcE5ld0xpbmUgcHJvcFxyXG4gICAgaWQ/OiBzdHJpbmc7XHJcbiAgICBvbkJsdXI/OiAoZmluYWxUZXh0OiBzdHJpbmcpID0+IHZvaWQ7IC8vIEFkZCBvbkJsdXIgcHJvcFxyXG4gICAgb25EZWJvdW5jZUNoYW5nZT86IChmaW5hbFRleHQ6IHN0cmluZykgPT4gdm9pZDsgLy8gQWRkIG9uRGVib3VuY2VDaGFuZ2UgcHJvcFxyXG4gICAgZGVib3VuY2VUaW1lb3V0TVM/OiBudW1iZXI7IC8vIE1ha2UgZGVib3VuY2UgdGltZW91dCBjb25maWd1cmFibGVcclxufVxyXG5cclxuaW50ZXJmYWNlIE1lbnRpb25Nb2RhbFN0YXRlIHtcclxuICAgIGlzT3BlbjogYm9vbGVhbjtcclxuICAgIHNwYW46IEhUTUxTcGFuRWxlbWVudCB8IG51bGw7XHJcbiAgICBtZW50aW9uS2V5OiBzdHJpbmc7XHJcbiAgICBjdXJyZW50RGVmYXVsdDogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gTWVudGlvbklucHV0KHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5TWFwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVmYXVsdFZhbHVlID0gXCJcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQmx1cixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWJvdW5jZUNoYW5nZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGVib3VuY2VUaW1lb3V0TVMgPSA1MDAsIC8vIERlZmF1bHQgdG8gNTAwbXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkID0gZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtlZXBOZXdMaW5lID0gZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTogTWVudGlvbklucHV0UHJvcHMpIHtcclxuICAgIGNvbnN0IGRpdlJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcblxyXG4gICAgLy8gQ3VycmVudCBvdXRwdXQgdmFsdWVcclxuICAgIGNvbnN0IGN1cnJlbnRPdXRwdXQgPSB1c2VSZWY8c3RyaW5nPihcIlwiKTtcclxuXHJcbiAgICAvLyBGb3IgZGVib3VuY2VkIHVwZGF0ZXNcclxuICAgIGNvbnN0IGRlYm91bmNlVGltZXJSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuXHJcblxyXG4gICAgLy8gRm9yIHRoZSBtZW50aW9uIHBvcHVwXHJcbiAgICBjb25zdCBbbWVudGlvbk1vZGUsIHNldE1lbnRpb25Nb2RlXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gc2hvd2luZyBtZW50aW9uIHBvcHVwP1xyXG4gICAgY29uc3QgW21lbnRpb25TZWFyY2gsIHNldE1lbnRpb25TZWFyY2hdID0gdXNlU3RhdGUoXCJcIik7IC8vIHRoZSB1c2VyIHR5cGVkIGluIHRoZSBwb3B1cCBzZWFyY2hcclxuICAgIGNvbnN0IHBvcHVwUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTsgLy8gcG9wdXAgY29udGFpbmVyIHJlZlxyXG4gICAgY29uc3QgW3Nob3dBYm92ZSwgc2V0U2hvd0Fib3ZlXSA9IHVzZVN0YXRlKGZhbHNlKTsgLy8gd2hldGhlciB0byBzaG93IGRyb3Bkb3duIGFib3ZlIGlucHV0XHJcbiAgICBjb25zdCBbcG9wdXBQb3NpdGlvbiwgc2V0UG9wdXBQb3NpdGlvbl0gPSB1c2VTdGF0ZSh7IHRvcDogMCwgbGVmdDogMCB9KTsgLy8gYWJzb2x1dGUgcG9zaXRpb24gZm9yIHBvcnRhbFxyXG5cclxuICAgIC8vIFN0b3JlIHRoZSBsb2NhdGlvbiBvZiB0aGUgXCJAXCIgdGhhdCB0cmlnZ2VyZWQgdGhlIHBvcHVwLlxyXG4gICAgY29uc3QgW2F0UmFuZ2UsIHNldEF0UmFuZ2VdID0gdXNlU3RhdGU8UmFuZ2UgfCBudWxsPihudWxsKTtcclxuICAgIC8vIFJlZiBmb3IgdGhlIG1lbnRpb24gcG9wdXAgc2VhcmNoIGlucHV0LlxyXG4gICAgY29uc3QgbWVudGlvbklucHV0UmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQ+KG51bGwpO1xyXG5cclxuICAgIC8vIFVzZWQgdG8gdGVtcG9yYXJpbHkgYnlwYXNzIHRoZSBjb250cm9sbGVkIGlubmVySFRNTCB1cGRhdGUgKHdoaWNoIGNhbiByZXNldCB0aGUgY2FyZXQpXHJcbiAgICBjb25zdCBza2lwSW5uZXJIdG1sVXBkYXRlID0gdXNlUmVmKGZhbHNlKTtcclxuXHJcbiAgICAvLyBNb2RhbCBzdGF0ZSBmb3Igc2V0dGluZyBhIGRlZmF1bHQgdmFsdWUgZm9yIGEgbWVudGlvbi5cclxuICAgIGNvbnN0IFttb2RhbFN0YXRlLCBzZXRNb2RhbFN0YXRlXSA9IHVzZVN0YXRlPE1lbnRpb25Nb2RhbFN0YXRlPih7XHJcbiAgICAgICAgaXNPcGVuOiBmYWxzZSxcclxuICAgICAgICBzcGFuOiBudWxsLFxyXG4gICAgICAgIG1lbnRpb25LZXk6IFwiXCIsXHJcbiAgICAgICAgY3VycmVudERlZmF1bHQ6IFwiXCIsXHJcbiAgICB9KTtcclxuICAgIGNvbnN0IFtkcmFmdERlZmF1bHQsIHNldERyYWZ0RGVmYXVsdF0gPSB1c2VTdGF0ZShcIlwiKTtcclxuXHJcblxyXG5cclxuICAgIC8vIGNvbnNvbGUubG9nKFwiTWVudGlvbklucHV0OlwiLCB7cG9wdXBQb3NpdGlvbiwgbWVudGlvbk1vZGUsIGF0UmFuZ2V9KVxyXG5cclxuICAgIC8vIENvbnZlcnQgZmluYWwgdGV4dCB2YWx1ZSB0byBIVE1MIHdpdGggbWVudGlvbiBzcGFucy5cclxuICAgIGZ1bmN0aW9uIHBhcnNlVGV4dFRvSHRtbCh0ZXh0OiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gICAgICAgIHRleHQgPSBTdHJpbmcodGV4dCB8fCAnJyk7XHJcbiAgICAgICAgY29uc3QgbWVudGlvblJlZ2V4ID0gL1xce1xceyhbXi99XSspKD86XFwvKC4qPykpP1xcfVxcfS9nO1xyXG4gICAgICAgIGxldCBsYXN0SW5kZXggPSAwO1xyXG4gICAgICAgIGxldCByZXN1bHRIdG1sID0gXCJcIjtcclxuXHJcbiAgICAgICAgZm9yIChjb25zdCBtYXRjaCBvZiB0ZXh0Lm1hdGNoQWxsKG1lbnRpb25SZWdleCkpIHtcclxuICAgICAgICAgICAgY29uc3Qgc3RhcnQgPSBtYXRjaC5pbmRleCA/PyAwO1xyXG4gICAgICAgICAgICBpZiAoc3RhcnQgPiBsYXN0SW5kZXgpIHtcclxuICAgICAgICAgICAgICAgIGxldCBwbGFpbiA9IHNhZmVIdG1sKHRleHQuc2xpY2UobGFzdEluZGV4LCBzdGFydCkpO1xyXG4gICAgICAgICAgICAgICAgaWYgKGtlZXBOZXdMaW5lKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcGxhaW4gPSBwbGFpbi5yZXBsYWNlKC9cXG4vZywgXCI8YnI+XCIpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgcmVzdWx0SHRtbCArPSBwbGFpbjtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgY29uc3QgbWVudGlvbktleSA9IG1hdGNoWzFdO1xyXG4gICAgICAgICAgICBjb25zdCBtZW50aW9uRGVmYXVsdCA9IG1hdGNoWzJdIHx8IFwiXCI7XHJcblxyXG4gICAgICAgICAgICAvLyBGaW5kIGNhc2UtaW5zZW5zaXRpdmUga2V5IG1hdGNoXHJcbiAgICAgICAgICAgIGNvbnN0IGFjdHVhbEtleSA9IGZpbmRDYXNlSW5zZW5zaXRpdmVLZXkoa2V5TWFwLCBtZW50aW9uS2V5KTtcclxuICAgICAgICAgICAgY29uc3QgaW5mbyA9IGFjdHVhbEtleSA/IGtleU1hcFthY3R1YWxLZXldIDogbnVsbDtcclxuXHJcbiAgICAgICAgICAgIGlmIChpbmZvKSB7XHJcbiAgICAgICAgICAgICAgICBsZXQgbGFiZWwgPSBpbmZvLmxhYmVsO1xyXG4gICAgICAgICAgICAgICAgaWYgKG1lbnRpb25EZWZhdWx0LnRyaW0oKSAhPT0gXCJcIikge1xyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsICs9IGAgLyAke21lbnRpb25EZWZhdWx0fWA7XHJcbiAgICAgICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YVZhbHVlID1cclxuICAgICAgICAgICAgICAgICAgICBtZW50aW9uRGVmYXVsdC50cmltKCkgPT09IFwiXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBge3ske2FjdHVhbEtleX19fWBcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBge3ske2FjdHVhbEtleX0vJHttZW50aW9uRGVmYXVsdH19fWA7XHJcblxyXG4gICAgICAgICAgICAgICAgY29uc3Qgc3Bhbkh0bWwgPSBgPHNwYW4gY29udGVudGVkaXRhYmxlPVwiZmFsc2VcIiBkYXRhLXRhZz1cIiR7ZXNjYXBlQXR0cihkYXRhVmFsdWUpfVwiPiR7c2FmZUh0bWwobGFiZWwpfTwvc3Bhbj5gO1xyXG4gICAgICAgICAgICAgICAgcmVzdWx0SHRtbCArPSBzcGFuSHRtbDtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdEh0bWwgKz0gc2FmZUh0bWwobWF0Y2hbMF0pO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBsYXN0SW5kZXggPSBzdGFydCArIG1hdGNoWzBdLmxlbmd0aDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChsYXN0SW5kZXggPCB0ZXh0Lmxlbmd0aCkge1xyXG4gICAgICAgICAgICBsZXQgcGxhaW4gPSBzYWZlSHRtbCh0ZXh0LnNsaWNlKGxhc3RJbmRleCkpO1xyXG4gICAgICAgICAgICBpZiAoa2VlcE5ld0xpbmUpIHtcclxuICAgICAgICAgICAgICAgIHBsYWluID0gcGxhaW4ucmVwbGFjZSgvXFxuL2csIFwiPGJyPlwiKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICByZXN1bHRIdG1sICs9IHBsYWluO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcmV0dXJuIHJlc3VsdEh0bWw7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGVscGVyIGZ1bmN0aW9uIGZvciBjYXNlLWluc2Vuc2l0aXZlIGtleSBsb29rdXBcclxuICAgIGZ1bmN0aW9uIGZpbmRDYXNlSW5zZW5zaXRpdmVLZXkob2JqOiBSZWNvcmQ8c3RyaW5nLCBhbnk+LCBrZXk6IHN0cmluZyk6IHN0cmluZyB8IG51bGwge1xyXG4gICAgICAgIGNvbnN0IGxvd2VyS2V5ID0ga2V5LnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgZm9yIChjb25zdCBrIG9mIE9iamVjdC5rZXlzKG9iaikpIHtcclxuICAgICAgICAgICAgaWYgKGsudG9Mb3dlckNhc2UoKSA9PT0gbG93ZXJLZXkpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybiBrO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBudWxsO1xyXG4gICAgfVxyXG5cclxuICAgIGZ1bmN0aW9uIHNhZmVIdG1sKHN0cjogc3RyaW5nKSB7XHJcbiAgICAgICAgcmV0dXJuIHN0clxyXG4gICAgICAgICAgICAucmVwbGFjZSgvJi9nLCBcIiZhbXA7XCIpXHJcbiAgICAgICAgICAgIC5yZXBsYWNlKC88L2csIFwiJmx0O1wiKVxyXG4gICAgICAgICAgICAucmVwbGFjZSgvPi9nLCBcIiZndDtcIik7XHJcbiAgICB9XHJcblxyXG4gICAgZnVuY3Rpb24gZXNjYXBlQXR0cihzdHI6IHN0cmluZykge1xyXG4gICAgICAgIHJldHVybiBzdHIucmVwbGFjZSgvXCIvZywgXCImcXVvdDtcIikucmVwbGFjZSgvJy9nLCBcIiYjMzk7XCIpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEZvciB1bmNvbnRyb2xsZWQgbW9kZTogb24gZmlyc3QgbW91bnQgZmlsbCBpbm5lckhUTUwgd2l0aCBkZWZhdWx0VmFsdWUuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkICYmIGRlZmF1bHRWYWx1ZSAhPT0gdW5kZWZpbmVkICYmIGRpdlJlZi5jdXJyZW50ICYmICFza2lwSW5uZXJIdG1sVXBkYXRlLmN1cnJlbnQpIHtcclxuICAgICAgICAgICAgY29uc3QgY3VycmVudEh0bWwgPSBkaXZSZWYuY3VycmVudC5pbm5lckhUTUw7XHJcbiAgICAgICAgICAgIGNvbnN0IG5ld0h0bWwgPSBwYXJzZVRleHRUb0h0bWwoZGVmYXVsdFZhbHVlIHx8ICcnKTtcclxuICAgICAgICAgICAgaWYgKGN1cnJlbnRIdG1sICE9PSBuZXdIdG1sKSB7XHJcbiAgICAgICAgICAgICAgICBkaXZSZWYuY3VycmVudC5pbm5lckhUTUwgPSBuZXdIdG1sO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2RlZmF1bHRWYWx1ZSwgdmFsdWUsIGtleU1hcF0pO1xyXG5cclxuICAgIC8vIEZvciBjb250cm9sbGVkIG1vZGU6IHVwZGF0ZSBpbm5lckhUTUwgd2hlbiB2YWx1ZSBjaGFuZ2VzLlxyXG4gICAgLy8gV2Ugc2tpcCB0aGlzIHVwZGF0ZSBpbW1lZGlhdGVseSBhZnRlciBhIG1lbnRpb24gaW5zZXJ0aW9uIGlmIG5lY2Vzc2FyeS5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKHZhbHVlICE9PSB1bmRlZmluZWQgJiYgZGl2UmVmLmN1cnJlbnQgJiYgIXNraXBJbm5lckh0bWxVcGRhdGUuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjb25zdCBjdXJyZW50SHRtbCA9IGRpdlJlZi5jdXJyZW50LmlubmVySFRNTDtcclxuICAgICAgICAgICAgY29uc3QgbmV3SHRtbCA9IHBhcnNlVGV4dFRvSHRtbCh2YWx1ZSk7XHJcbiAgICAgICAgICAgIC8vIE9ubHkgdXBkYXRlIGlubmVySFRNTCBpZiB0aGUgZGl2IGlzIG5vdCBmb2N1c2VkICh0byBwcmVzZXJ2ZSB0aGUgY2FyZXQgcG9zaXRpb24pXHJcbiAgICAgICAgICAgIGlmIChkb2N1bWVudC5hY3RpdmVFbGVtZW50ICE9PSBkaXZSZWYuY3VycmVudCAmJiBjdXJyZW50SHRtbCAhPT0gbmV3SHRtbCkge1xyXG4gICAgICAgICAgICAgICAgZGl2UmVmLmN1cnJlbnQuaW5uZXJIVE1MID0gbmV3SHRtbDtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH0sIFt2YWx1ZSwga2V5TWFwXSk7XHJcblxyXG4gICAgLy8gQnVpbGQgdGhlIHByb2Nlc3NlZCBmaW5hbCB0ZXh0IGZyb20gaW5uZXJIVE1MLlxyXG4gICAgZnVuY3Rpb24gZ2V0RmluYWxPdXRwdXQoKTogc3RyaW5nIHtcclxuICAgICAgICBpZiAoIWRpdlJlZi5jdXJyZW50KSByZXR1cm4gXCJcIjtcclxuICAgICAgICBsZXQgcmVzdWx0ID0gXCJcIjtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gdHJhdmVyc2Uobm9kZTogTm9kZSkge1xyXG4gICAgICAgICAgICBpZiAobm9kZS5ub2RlVHlwZSA9PT0gTm9kZS5URVhUX05PREUpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdCArPSBub2RlLm5vZGVWYWx1ZSB8fCBcIlwiO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKG5vZGUubm9kZVR5cGUgPT09IE5vZGUuRUxFTUVOVF9OT0RFKSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBlbCA9IG5vZGUgYXMgSFRNTEVsZW1lbnQ7XHJcbiAgICAgICAgICAgICAgICBpZiAoZWwuaGFzQXR0cmlidXRlKFwiZGF0YS10YWdcIikpIHtcclxuICAgICAgICAgICAgICAgICAgICByZXN1bHQgKz0gZWwuZ2V0QXR0cmlidXRlKFwiZGF0YS10YWdcIik7XHJcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGVsLm5vZGVOYW1lID09PSBcIkJSXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoa2VlcE5ld0xpbmUpIHJlc3VsdCArPSBcIlxcblwiO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBBcnJheS5mcm9tKGVsLmNoaWxkTm9kZXMpLmZvckVhY2goY2hpbGQgPT4gdHJhdmVyc2UoY2hpbGQpKTtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoa2VlcE5ld0xpbmUgJiYgKGVsLm5vZGVOYW1lID09PSBcIkRJVlwiIHx8IGVsLm5vZGVOYW1lID09PSBcIlBcIikpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0ICs9IFwiXFxuXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBBcnJheS5mcm9tKGRpdlJlZi5jdXJyZW50LmNoaWxkTm9kZXMpLmZvckVhY2goY2hpbGQgPT4gdHJhdmVyc2UoY2hpbGQpKTtcclxuICAgICAgICByZXR1cm4gcmVzdWx0O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFVwZGF0ZSB2YWx1ZSBhbmQgdHJpZ2dlciBvbkNoYW5nZSBpbW1lZGlhdGVseVxyXG4gICAgZnVuY3Rpb24gdXBkYXRlVmFsdWUoKSB7XHJcbiAgICAgICAgY29uc3Qgb3V0cHV0ID0gZ2V0RmluYWxPdXRwdXQoKTtcclxuICAgICAgICBjdXJyZW50T3V0cHV0LmN1cnJlbnQgPSBvdXRwdXQ7XHJcbiAgICAgICAgb25DaGFuZ2U/LihvdXRwdXQpO1xyXG5cclxuICAgICAgICAvLyBTZXQgdXAgZGVib3VuY2VkIHVwZGF0ZVxyXG4gICAgICAgIGlmIChvbkRlYm91bmNlQ2hhbmdlKSB7XHJcbiAgICAgICAgICAgIGlmIChkZWJvdW5jZVRpbWVyUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dChkZWJvdW5jZVRpbWVyUmVmLmN1cnJlbnQpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBkZWJvdW5jZVRpbWVyUmVmLmN1cnJlbnQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICAgICAgICAgIG9uRGVib3VuY2VDaGFuZ2Uob3V0cHV0KTtcclxuICAgICAgICAgICAgICAgIGRlYm91bmNlVGltZXJSZWYuY3VycmVudCA9IG51bGw7XHJcbiAgICAgICAgICAgIH0sIGRlYm91bmNlVGltZW91dE1TKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLy8gSGFuZGxlIGJsdXIgZXZlbnRcclxuICAgIGZ1bmN0aW9uIGhhbmRsZUJsdXIoKSB7XHJcbiAgICAgICAgaWYgKGRpc2FibGVkKSByZXR1cm47XHJcbiAgICAgICAgaWYgKG1lbnRpb25Nb2RlKSByZXR1cm47XHJcbiAgICAgICAgb25CbHVyPy4oY3VycmVudE91dHB1dC5jdXJyZW50IHx8IGdldEZpbmFsT3V0cHV0KCkpO1xyXG4gICAgfVxyXG5cclxuICAgIGZ1bmN0aW9uIG9uSW5wdXQoKSB7XHJcbiAgICAgICAgaWYgKGRpc2FibGVkKSByZXR1cm47XHJcbiAgICAgICAgdXBkYXRlVmFsdWUoKTtcclxuICAgIH1cclxuXHJcbiAgICAvLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xyXG4gICAgLy8gTWVudGlvbiBwb3B1cCBsb2dpY1xyXG4gICAgLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy9cclxuXHJcbiAgICAvLyBXaGVuIHRoZSB1c2VyIHR5cGVzIFwiQFwiLCBsZXQgaXQgaW5zZXJ0IG5vcm1hbGx5IGJ1dCBzdG9yZSB0aGUgY3VycmVudCBSYW5nZS5cclxuICAgIGZ1bmN0aW9uIG9uS2V5RG93bihlOiBSZWFjdC5LZXlib2FyZEV2ZW50PEhUTUxEaXZFbGVtZW50Pikge1xyXG4gICAgICAgIGlmIChkaXNhYmxlZCkgcmV0dXJuO1xyXG4gICAgICAgIGlmIChlLmtleSA9PT0gXCJAXCIpIHtcclxuICAgICAgICAgICAgY29uc3Qgc2VsID0gd2luZG93LmdldFNlbGVjdGlvbj8uKCk7XHJcbiAgICAgICAgICAgIGlmIChzZWwgJiYgc2VsLnJhbmdlQ291bnQgPiAwKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBTYXZlIGEgY2xvbmUgb2YgdGhlIGN1cnJlbnQgcmFuZ2Ugd2hlcmUgXCJAXCIgd2FzIGluc2VydGVkLlxyXG4gICAgICAgICAgICAgICAgY29uc3QgY3VycmVudFJhbmdlID0gc2VsLmdldFJhbmdlQXQoMCkuY2xvbmVSYW5nZSgpO1xyXG4gICAgICAgICAgICAgICAgc2V0QXRSYW5nZShjdXJyZW50UmFuZ2UpO1xyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICAvLyBDYWxjdWxhdGUgcG9zaXRpb24gaW1tZWRpYXRlbHkgdG8gcHJldmVudCBmbGFzaFxyXG4gICAgICAgICAgICAgICAgaWYgKGRpdlJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5wdXRSZWN0ID0gZGl2UmVmLmN1cnJlbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3BhY2VCZWxvdyA9IHdpbmRvdy5pbm5lckhlaWdodCAtIGlucHV0UmVjdC5ib3R0b207XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc3BhY2VBYm92ZSA9IGlucHV0UmVjdC50b3A7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZHJvcGRvd25IZWlnaHQgPSAyNTA7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbWluU3BhY2VSZXF1aXJlZCA9IDEwMDtcclxuICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzaG91bGRTaG93QWJvdmUgPSBzcGFjZUJlbG93IDwgbWluU3BhY2VSZXF1aXJlZCAmJiBzcGFjZUFib3ZlID4gc3BhY2VCZWxvdztcclxuICAgICAgICAgICAgICAgICAgICBzZXRTaG93QWJvdmUoc2hvdWxkU2hvd0Fib3ZlKTtcclxuICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBzY3JvbGxYID0gd2luZG93LnBhZ2VYT2Zmc2V0IHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxMZWZ0O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNjcm9sbFkgPSB3aW5kb3cucGFnZVlPZmZzZXQgfHwgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbFRvcDtcclxuICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgICAgICBzZXRQb3B1cFBvc2l0aW9uKHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogaW5wdXRSZWN0LmxlZnQgKyBzY3JvbGxYLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0b3A6IHNob3VsZFNob3dBYm92ZSBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gaW5wdXRSZWN0LnRvcCArIHNjcm9sbFkgLSBkcm9wZG93bkhlaWdodCAtIDJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogaW5wdXRSZWN0LmJvdHRvbSArIHNjcm9sbFkgKyAyXHJcbiAgICAgICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgc2V0TWVudGlvbk1vZGUodHJ1ZSk7XHJcbiAgICAgICAgICAgIHNldE1lbnRpb25TZWFyY2goXCJcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENsb3NlIHRoZSBtZW50aW9uIHBvcHVwLlxyXG4gICAgZnVuY3Rpb24gY2xvc2VNZW50aW9uUG9wdXAoKSB7XHJcbiAgICAgICAgc2V0TWVudGlvbk1vZGUoZmFsc2UpO1xyXG4gICAgICAgIHNldE1lbnRpb25TZWFyY2goXCJcIik7XHJcbiAgICAgICAgc2V0QXRSYW5nZShudWxsKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBFbmhhbmNlZCBmdXp6eSBmaWx0ZXJpbmcgdGhhdCBjaGVja3MgYm90aCBrZXlzIGFuZCBsYWJlbHNcclxuICAgIGZ1bmN0aW9uIGZ1enp5RmlsdGVyKHF1ZXJ5OiBzdHJpbmcsIGl0ZW1zOiBzdHJpbmdbXSk6IHN0cmluZ1tdIHtcclxuICAgICAgICBpZiAoIXF1ZXJ5LnRyaW0oKSkgcmV0dXJuIGl0ZW1zLnNsaWNlKDAsIDMwKTtcclxuICAgICAgICBjb25zdCBub3JtYWxpemVkUXVlcnkgPSBxdWVyeS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpLnRvTG93ZXJDYXNlKCk7XHJcblxyXG4gICAgICAgIHR5cGUgUmFua2VkID0geyBpdGVtOiBzdHJpbmc7IHJhbms6IG51bWJlciB9O1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdHM6IFJhbmtlZFtdID0gW107XHJcblxyXG4gICAgICAgIGZvciAoY29uc3Qga2V5IG9mIGl0ZW1zKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IGluZm8gPSBrZXlNYXBba2V5XTtcclxuICAgICAgICAgICAgY29uc3Qgbm9ybWFsaXplZEtleSA9IGtleS5yZXBsYWNlKC9cXHMrL2csIFwiXCIpLnRvTG93ZXJDYXNlKCk7XHJcbiAgICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRMYWJlbCA9IGluZm8ubGFiZWwucmVwbGFjZSgvXFxzKy9nLCBcIlwiKS50b0xvd2VyQ2FzZSgpO1xyXG5cclxuICAgICAgICAgICAgLy8gUmFuayBwcmlvcml0aWVzIChsb3dlciBpcyBiZXR0ZXIpOlxyXG4gICAgICAgICAgICAvLyAxOiBLZXkgc3RhcnRzIHdpdGggcXVlcnlcclxuICAgICAgICAgICAgLy8gMjogTGFiZWwgc3RhcnRzIHdpdGggcXVlcnlcclxuICAgICAgICAgICAgLy8gMzogS2V5IGNvbnRhaW5zIHF1ZXJ5XHJcbiAgICAgICAgICAgIC8vIDQ6IExhYmVsIGNvbnRhaW5zIHF1ZXJ5XHJcblxyXG4gICAgICAgICAgICBpZiAobm9ybWFsaXplZEtleS5zdGFydHNXaXRoKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiAxfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9ybWFsaXplZExhYmVsLnN0YXJ0c1dpdGgobm9ybWFsaXplZFF1ZXJ5KSkge1xyXG4gICAgICAgICAgICAgICAgcmVzdWx0cy5wdXNoKHtpdGVtOiBrZXksIHJhbms6IDJ9KTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChub3JtYWxpemVkS2V5LmluY2x1ZGVzKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiAzfSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAobm9ybWFsaXplZExhYmVsLmluY2x1ZGVzKG5vcm1hbGl6ZWRRdWVyeSkpIHtcclxuICAgICAgICAgICAgICAgIHJlc3VsdHMucHVzaCh7aXRlbToga2V5LCByYW5rOiA0fSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJlc3VsdHMuc29ydCgoYSwgYikgPT4gYS5yYW5rIC0gYi5yYW5rKTtcclxuICAgICAgICByZXR1cm4gcmVzdWx0cy5tYXAoKHIpID0+IHIuaXRlbSkuc2xpY2UoMCwgMzApO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGFsbEtleXMgPSBPYmplY3Qua2V5cyhrZXlNYXApO1xyXG4gICAgY29uc3QgbWVudGlvbk1hdGNoZXMgPSBtZW50aW9uTW9kZSA/IGZ1enp5RmlsdGVyKG1lbnRpb25TZWFyY2gsIGFsbEtleXMpIDogW107XHJcblxyXG4gICAgLy8gV2hlbiBhIG1lbnRpb24gaXMgc2VsZWN0ZWQgZnJvbSB0aGUgcG9wdXAuXHJcbiAgICBmdW5jdGlvbiBvbk1lbnRpb25TZWxlY3QobWVudGlvbktleTogc3RyaW5nKSB7XHJcbiAgICAgICAgaWYgKCFkaXZSZWYuY3VycmVudCkgcmV0dXJuO1xyXG4gICAgICAgIGNvbnN0IHNlbCA9IHdpbmRvdy5nZXRTZWxlY3Rpb24oKTtcclxuICAgICAgICBpZiAoIXNlbCkge1xyXG4gICAgICAgICAgICBjbG9zZU1lbnRpb25Qb3B1cCgpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIEFsd2F5cyB1c2UgdGhlIHN0b3JlZCByYW5nZSBpZiBhdmFpbGFibGUuXHJcbiAgICAgICAgbGV0IHJhbmdlOiBSYW5nZSB8IG51bGwgPSBhdFJhbmdlIHx8IChzZWwucmFuZ2VDb3VudCA+IDAgPyBzZWwuZ2V0UmFuZ2VBdCgwKSA6IG51bGwpO1xyXG4gICAgICAgIGlmICghcmFuZ2UpIHtcclxuICAgICAgICAgICAgY2xvc2VNZW50aW9uUG9wdXAoKTtcclxuICAgICAgICAgICAgcmV0dXJuO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLy8gSGVscGVyOiBTZWFyY2ggYmFja3dhcmRzIGluIGEgdGV4dCBub2RlIGZyb20gYSBnaXZlbiBvZmZzZXQgdG8gcmVtb3ZlIFwiQFwiLlxyXG4gICAgICAgIGZ1bmN0aW9uIHJlbW92ZUF0RnJvbVRleHROb2RlKHRleHROb2RlOiBUZXh0LCBwb3M6IG51bWJlcik6IFJhbmdlIHwgbnVsbCB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRleHQgPSB0ZXh0Tm9kZS5kYXRhO1xyXG4gICAgICAgICAgICBjb25zdCBzZWFyY2hTdGFydCA9IE1hdGgubWF4KDAsIHBvcyAtIDUpO1xyXG4gICAgICAgICAgICBjb25zdCBzZWFyY2hFbmQgPSBNYXRoLm1pbih0ZXh0Lmxlbmd0aCwgcG9zICsgNSk7XHJcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSBzZWFyY2hFbmQgLSAxOyBpID49IHNlYXJjaFN0YXJ0OyBpLS0pIHtcclxuICAgICAgICAgICAgICAgIGlmICh0ZXh0LmNoYXJBdChpKSA9PT0gXCJAXCIpIHtcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0Tm9kZS5kYXRhID0gdGV4dC5zdWJzdHJpbmcoMCwgaSkgKyB0ZXh0LnN1YnN0cmluZyhpICsgMSk7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3UmFuZ2UgPSBkb2N1bWVudC5jcmVhdGVSYW5nZSgpO1xyXG4gICAgICAgICAgICAgICAgICAgIG5ld1JhbmdlLnNldFN0YXJ0KHRleHROb2RlLCBpKTtcclxuICAgICAgICAgICAgICAgICAgICBuZXdSYW5nZS5jb2xsYXBzZSh0cnVlKTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3UmFuZ2U7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvLyBUcnkgdG8gcmVtb3ZlIFwiQFwiIGZyb20gdGhlIGN1cnJlbnQgdGV4dCBub2RlLlxyXG4gICAgICAgIGlmIChyYW5nZS5zdGFydENvbnRhaW5lci5ub2RlVHlwZSA9PT0gTm9kZS5URVhUX05PREUpIHtcclxuICAgICAgICAgICAgY29uc3QgdGV4dE5vZGUgPSByYW5nZS5zdGFydENvbnRhaW5lciBhcyBUZXh0O1xyXG4gICAgICAgICAgICBjb25zdCBwb3MgPSByYW5nZS5zdGFydE9mZnNldDtcclxuICAgICAgICAgICAgY29uc3QgbmV3Um5nID0gcmVtb3ZlQXRGcm9tVGV4dE5vZGUodGV4dE5vZGUsIHBvcyk7XHJcbiAgICAgICAgICAgIGlmIChuZXdSbmcpIHtcclxuICAgICAgICAgICAgICAgIHJhbmdlID0gbmV3Um5nO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgLy8gSWYgbm90IGEgdGV4dCBub2RlLCBjaGVjayBwcmV2aW91cyBzaWJsaW5nIChpZiB0ZXh0KSBmcm9tIHRoZSBjdXJyZW50IGNvbnRhaW5lci5cclxuICAgICAgICAgICAgY29uc3QgY29udGFpbmVyID0gcmFuZ2Uuc3RhcnRDb250YWluZXI7XHJcbiAgICAgICAgICAgIGlmIChjb250YWluZXIuY2hpbGROb2Rlcy5sZW5ndGggPiAwICYmIHJhbmdlLnN0YXJ0T2Zmc2V0ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcHJldiA9IGNvbnRhaW5lci5jaGlsZE5vZGVzW3JhbmdlLnN0YXJ0T2Zmc2V0IC0gMV07XHJcbiAgICAgICAgICAgICAgICBpZiAocHJldiAmJiBwcmV2Lm5vZGVUeXBlID09PSBOb2RlLlRFWFRfTk9ERSkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHRleHROb2RlID0gcHJldiBhcyBUZXh0O1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1JuZyA9IHJlbW92ZUF0RnJvbVRleHROb2RlKHRleHROb2RlLCB0ZXh0Tm9kZS5kYXRhLmxlbmd0aCk7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKG5ld1JuZykge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByYW5nZSA9IG5ld1JuZztcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGNvbnN0IGluZm8gPSBrZXlNYXBbbWVudGlvbktleV07XHJcbiAgICAgICAgaWYgKCFpbmZvKSB7XHJcbiAgICAgICAgICAgIGNsb3NlTWVudGlvblBvcHVwKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8vIENyZWF0ZSBhbmQgaW5zZXJ0IHRoZSBtZW50aW9uIHNwYW4uXHJcbiAgICAgICAgY29uc3Qgc3BhbiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIpO1xyXG4gICAgICAgIHNwYW4uY29udGVudEVkaXRhYmxlID0gXCJmYWxzZVwiO1xyXG4gICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKFwiZGF0YS10YWdcIiwgaW5mby50YWcpO1xyXG4gICAgICAgIHNwYW4udGV4dENvbnRlbnQgPSBpbmZvLmxhYmVsO1xyXG4gICAgICAgIHNwYW4ub25kYmxjbGljayA9ICgpID0+IHtcclxuICAgICAgICAgICAgb3BlbkRlZmF1bHRNb2RhbChzcGFuKTtcclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBza2lwSW5uZXJIdG1sVXBkYXRlLmN1cnJlbnQgPSB0cnVlO1xyXG4gICAgICAgIHJhbmdlLmluc2VydE5vZGUoc3Bhbik7XHJcblxyXG4gICAgICAgIC8vIEluc2VydCBhIHplcm8td2lkdGggc3BhY2UgZmlsbGVyIG5vZGUgYWZ0ZXIgdGhlIG1lbnRpb24gc3Bhbi5cclxuICAgICAgICBjb25zdCBmaWxsZXIgPSBkb2N1bWVudC5jcmVhdGVUZXh0Tm9kZShcIlxcdTIwMEJcIik7XHJcbiAgICAgICAgc3Bhbi5wYXJlbnROb2RlPy5pbnNlcnRCZWZvcmUoZmlsbGVyLCBzcGFuLm5leHRTaWJsaW5nKTtcclxuXHJcbiAgICAgICAgLy8gUG9zaXRpb24gdGhlIGN1cnNvciBhZnRlciB0aGUgZmlsbGVyIG5vZGUuXHJcbiAgICAgICAgY29uc3QgbmV3UmFuZ2UgPSBkb2N1bWVudC5jcmVhdGVSYW5nZSgpO1xyXG4gICAgICAgIG5ld1JhbmdlLnNldFN0YXJ0QWZ0ZXIoZmlsbGVyKTtcclxuICAgICAgICBuZXdSYW5nZS5jb2xsYXBzZSh0cnVlKTtcclxuICAgICAgICBzZWwucmVtb3ZlQWxsUmFuZ2VzKCk7XHJcbiAgICAgICAgc2VsLmFkZFJhbmdlKG5ld1JhbmdlKTtcclxuXHJcbiAgICAgICAgZGl2UmVmLmN1cnJlbnQuZm9jdXMoKTtcclxuICAgICAgICBjbG9zZU1lbnRpb25Qb3B1cCgpO1xyXG4gICAgICAgIHVwZGF0ZVZhbHVlKCk7XHJcblxyXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgICAgICBza2lwSW5uZXJIdG1sVXBkYXRlLmN1cnJlbnQgPSBmYWxzZTtcclxuICAgICAgICB9LCAwKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbG9zZSBtZW50aW9uIHBvcHVwIG9uIEVTQyBrZXkgb3Igb3V0c2lkZSBjbGljay5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgZnVuY3Rpb24gb25LZXkoZTogS2V5Ym9hcmRFdmVudCkge1xyXG4gICAgICAgICAgICBpZiAoZS5rZXkgPT09IFwiRXNjYXBlXCIpIHtcclxuICAgICAgICAgICAgICAgIGNsb3NlTWVudGlvblBvcHVwKCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGZ1bmN0aW9uIG9uQ2xpY2tPdXRzaWRlKGU6IE1vdXNlRXZlbnQpIHtcclxuICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZS50YXJnZXQgYXMgTm9kZTtcclxuICAgICAgICAgICAgLy8gQ2hlY2sgaWYgY2xpY2sgaXMgb3V0c2lkZSBib3RoIHRoZSBwb3B1cCBhbmQgdGhlIGlucHV0IGZpZWxkXHJcbiAgICAgICAgICAgIGlmIChwb3B1cFJlZi5jdXJyZW50ICYmICFwb3B1cFJlZi5jdXJyZW50LmNvbnRhaW5zKHRhcmdldCkgJiYgXHJcbiAgICAgICAgICAgICAgICBkaXZSZWYuY3VycmVudCAmJiAhZGl2UmVmLmN1cnJlbnQuY29udGFpbnModGFyZ2V0KSkge1xyXG4gICAgICAgICAgICAgICAgY2xvc2VNZW50aW9uUG9wdXAoKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKG1lbnRpb25Nb2RlKSB7XHJcbiAgICAgICAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIG9uS2V5KTtcclxuICAgICAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLCBvbkNsaWNrT3V0c2lkZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIG9uS2V5KTtcclxuICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm1vdXNlZG93blwiLCBvbkNsaWNrT3V0c2lkZSk7XHJcbiAgICAgICAgfTtcclxuICAgIH0sIFttZW50aW9uTW9kZV0pO1xyXG5cclxuICAgIC8vIEF1dG9mb2N1cyB0aGUgbWVudGlvbiBwb3B1cCBzZWFyY2ggaW5wdXQgd2hlbiBpdCBvcGVucy5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKG1lbnRpb25Nb2RlKSB7XHJcbiAgICAgICAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZSgoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBtZW50aW9uSW5wdXRSZWYuY3VycmVudD8uZm9jdXMoKTtcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgfSwgW21lbnRpb25Nb2RlXSk7XHJcblxyXG4gICAgLy8gVXBkYXRlIHBvc2l0aW9uIG9uIHdpbmRvdyByZXNpemUgb3Igc2Nyb2xsXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmIChtZW50aW9uTW9kZSAmJiBkaXZSZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVQb3NpdGlvbiA9ICgpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGlucHV0UmVjdCA9IGRpdlJlZi5jdXJyZW50Py5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTtcclxuICAgICAgICAgICAgICAgIGlmICghaW5wdXRSZWN0KSByZXR1cm47XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNwYWNlQmVsb3cgPSB3aW5kb3cuaW5uZXJIZWlnaHQgLSBpbnB1dFJlY3QuYm90dG9tO1xyXG4gICAgICAgICAgICAgICAgY29uc3Qgc3BhY2VBYm92ZSA9IGlucHV0UmVjdC50b3A7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkcm9wZG93bkhlaWdodCA9IDI1MDtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1pblNwYWNlUmVxdWlyZWQgPSAxMDA7XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNob3VsZFNob3dBYm92ZSA9IHNwYWNlQmVsb3cgPCBtaW5TcGFjZVJlcXVpcmVkICYmIHNwYWNlQWJvdmUgPiBzcGFjZUJlbG93O1xyXG4gICAgICAgICAgICAgICAgc2V0U2hvd0Fib3ZlKHNob3VsZFNob3dBYm92ZSk7XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIGNvbnN0IHNjcm9sbFggPSB3aW5kb3cucGFnZVhPZmZzZXQgfHwgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnNjcm9sbExlZnQ7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBzY3JvbGxZID0gd2luZG93LnBhZ2VZT2Zmc2V0IHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxUb3A7XHJcbiAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAgIHNldFBvcHVwUG9zaXRpb24oe1xyXG4gICAgICAgICAgICAgICAgICAgIGxlZnQ6IGlucHV0UmVjdC5sZWZ0ICsgc2Nyb2xsWCxcclxuICAgICAgICAgICAgICAgICAgICB0b3A6IHNob3VsZFNob3dBYm92ZSBcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBpbnB1dFJlY3QudG9wICsgc2Nyb2xsWSAtIGRyb3Bkb3duSGVpZ2h0IC0gMlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGlucHV0UmVjdC5ib3R0b20gKyBzY3JvbGxZICsgMlxyXG4gICAgICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAvLyBPbmx5IGxpc3RlbiBmb3IgcmVzaXplIGFuZCBzY3JvbGwgZXZlbnRzLCBkb24ndCBydW4gaW1tZWRpYXRlbHlcclxuICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHVwZGF0ZVBvc2l0aW9uKTtcclxuICAgICAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVBvc2l0aW9uLCB0cnVlKTtcclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdXBkYXRlUG9zaXRpb24pO1xyXG4gICAgICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIHVwZGF0ZVBvc2l0aW9uLCB0cnVlKTtcclxuICAgICAgICAgICAgfTtcclxuICAgICAgICB9XHJcbiAgICB9LCBbbWVudGlvbk1vZGVdKTtcclxuXHJcblxyXG5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbiAgICAvLyBEb3VibGUtY2xpY2sgbWVudGlvbiA9PiBvcGVuIGEgbW9kYWwgZm9yIGVkaXRpbmcgZGVmYXVsdC5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG4gICAgZnVuY3Rpb24gb3BlbkRlZmF1bHRNb2RhbChzcGFuOiBIVE1MU3BhbkVsZW1lbnQpIHtcclxuICAgICAgICBpZiAoZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICBjb25zdCBtZW50aW9uVmFsdWUgPSBzcGFuLmdldEF0dHJpYnV0ZShcImRhdGEtdGFnXCIpIHx8IFwiXCI7XHJcbiAgICAgICAgY29uc3QgcGF0dGVybiA9IC9eXFx7XFx7KFteL31dKykoPzpcXC8oLio/KSk/XFx9XFx9XFxzKiQvO1xyXG4gICAgICAgIGNvbnN0IG1hdGNoID0gbWVudGlvblZhbHVlLm1hdGNoKHBhdHRlcm4pO1xyXG4gICAgICAgIGlmICghbWF0Y2gpIHJldHVybjtcclxuXHJcbiAgICAgICAgY29uc3QgbWVudGlvbktleSA9IG1hdGNoWzFdO1xyXG4gICAgICAgIGNvbnN0IGV4aXN0aW5nRGVmYXVsdCA9IG1hdGNoWzJdIHx8IFwiXCI7XHJcbiAgICAgICAgc2V0TW9kYWxTdGF0ZSh7XHJcbiAgICAgICAgICAgIGlzT3BlbjogdHJ1ZSxcclxuICAgICAgICAgICAgc3BhbixcclxuICAgICAgICAgICAgbWVudGlvbktleSxcclxuICAgICAgICAgICAgY3VycmVudERlZmF1bHQ6IGV4aXN0aW5nRGVmYXVsdCxcclxuICAgICAgICB9KTtcclxuICAgICAgICBzZXREcmFmdERlZmF1bHQoZXhpc3RpbmdEZWZhdWx0KTtcclxuICAgIH1cclxuXHJcbiAgICBmdW5jdGlvbiBjb25maXJtRGVmYXVsdCgpIHtcclxuICAgICAgICBjb25zdCB7c3BhbiwgbWVudGlvbktleX0gPSBtb2RhbFN0YXRlO1xyXG4gICAgICAgIGlmICghc3Bhbikge1xyXG4gICAgICAgICAgICBjbG9zZU1vZGFsKCk7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgaW5mbyA9IGtleU1hcFttZW50aW9uS2V5XTtcclxuICAgICAgICBpZiAoIWluZm8pIHtcclxuICAgICAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCB1c2VyRGVmYXVsdCA9IGRyYWZ0RGVmYXVsdC50cmltKCk7XHJcbiAgICAgICAgbGV0IG5ld1ZhbHVlID0gYHt7JHttZW50aW9uS2V5fX19YDtcclxuICAgICAgICBsZXQgbmV3TGFiZWwgPSBpbmZvLmxhYmVsO1xyXG4gICAgICAgIGlmICh1c2VyRGVmYXVsdCAhPT0gXCJcIikge1xyXG4gICAgICAgICAgICBuZXdWYWx1ZSA9IGB7eyR7bWVudGlvbktleX0vJHt1c2VyRGVmYXVsdH19fWA7XHJcbiAgICAgICAgICAgIG5ld0xhYmVsID0gYCR7aW5mby5sYWJlbH0gLyAke3VzZXJEZWZhdWx0fWA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZShcImRhdGEtdGFnXCIsIG5ld1ZhbHVlKTtcclxuICAgICAgICBzcGFuLnRleHRDb250ZW50ID0gbmV3TGFiZWw7XHJcbiAgICAgICAgc3Bhbi5vbmRibGNsaWNrID0gKCkgPT4ge1xyXG4gICAgICAgICAgICBvcGVuRGVmYXVsdE1vZGFsKHNwYW4pO1xyXG4gICAgICAgIH07XHJcblxyXG4gICAgICAgIHVwZGF0ZVZhbHVlKCk7XHJcbiAgICAgICAgY2xvc2VNb2RhbCgpO1xyXG4gICAgfVxyXG5cclxuICAgIGZ1bmN0aW9uIGNsb3NlTW9kYWwoKSB7XHJcbiAgICAgICAgc2V0TW9kYWxTdGF0ZSh7XHJcbiAgICAgICAgICAgIGlzT3BlbjogZmFsc2UsXHJcbiAgICAgICAgICAgIHNwYW46IG51bGwsXHJcbiAgICAgICAgICAgIG1lbnRpb25LZXk6IFwiXCIsXHJcbiAgICAgICAgICAgIGN1cnJlbnREZWZhdWx0OiBcIlwiLFxyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEVuc3VyZSBleGlzdGluZyBtZW50aW9uIHNwYW5zIGFyZSBjbGlja2FibGUgdG8gb3BlbiB0aGUgbW9kYWwuXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGlmICghZGl2UmVmLmN1cnJlbnQgfHwgZGlzYWJsZWQpIHJldHVybjtcclxuICAgICAgICBjb25zdCBtZW50aW9uU3BhbnMgPSBkaXZSZWYuY3VycmVudC5xdWVyeVNlbGVjdG9yQWxsKFwic3BhbltkYXRhLXRhZ11cIik7XHJcbiAgICAgICAgbWVudGlvblNwYW5zLmZvckVhY2goKGVsKSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHNwYW4gPSBlbCBhcyBIVE1MU3BhbkVsZW1lbnQ7XHJcbiAgICAgICAgICAgIGlmICghc3Bhbi5vbmRibGNsaWNrKSB7XHJcbiAgICAgICAgICAgICAgICBzcGFuLm9uZGJsY2xpY2sgPSAoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgb3BlbkRlZmF1bHRNb2RhbChzcGFuKTtcclxuICAgICAgICAgICAgICAgIH07XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgIH0sIFtkaXNhYmxlZF0pO1xyXG5cclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcbiAgICAvLyBSZW5kZXJcclxuICAgIC8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXHJcblxyXG4gICAgLy8gY29uc29sZS5sb2coXCJSZW5kZXI6XCIsIHtrZXlNYXAsIHZhbHVlLCBkZWZhdWx0VmFsdWV9KVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgbUkgcmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgaWQ9e2lkfVxyXG4gICAgICAgICAgICAgICAgcmVmPXtkaXZSZWZ9XHJcbiAgICAgICAgICAgICAgICAvLyBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIHJvdW5kZWQtbm9uZSB0ZXh0LXhzIGZvbnQtbWVkaXVtIGZvY3VzOmJvcmRlci1ibGFjayBmb2N1cy13aXRoaW46Ym9yZGVyLWJsYWNrIGdyb3VwXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKGBib3JkZXIgYm9yZGVyLW5ldXRyYWwtMzAwIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLW5vbmUgcHgtMyBweS0xIHNoYWRvdy1zbSB0ZXh0LXhzIG91dGxpbmUtbm9uZSBmb2N1cy13aXRoaW46Ym9yZGVyLWJsYWNrIG1pbi1oLTcgZm9udC1tZWRpdW0gbGVhZGluZy02YCwgY2xhc3NOYW1lKX1cclxuICAgICAgICAgICAgICAgIGNvbnRlbnRFZGl0YWJsZT17IWRpc2FibGVkfVxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3t3aGl0ZVNwYWNlOiBcInByZS13cmFwXCJ9fVxyXG4gICAgICAgICAgICAgICAgc3VwcHJlc3NDb250ZW50RWRpdGFibGVXYXJuaW5nXHJcbiAgICAgICAgICAgICAgICBkYXRhLXBsYWNlaG9sZGVyPXshZGlzYWJsZWQgPyBwbGFjZWhvbGRlciA6IFwiXCJ9XHJcbiAgICAgICAgICAgICAgICBvbklucHV0PXtvbklucHV0fVxyXG4gICAgICAgICAgICAgICAgb25LZXlEb3duPXtvbktleURvd259XHJcbiAgICAgICAgICAgICAgICBvbkJsdXI9e2hhbmRsZUJsdXJ9XHJcbiAgICAgICAgICAgICAgICBhcmlhLWRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIHttZW50aW9uTW9kZSAmJiAhZGlzYWJsZWQgJiYgdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyAmJiBjcmVhdGVQb3J0YWwoXHJcbiAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgcmVmPXtwb3B1cFJlZn1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCB6LVs5OTk5OTldIGJnLXdoaXRlIGJvcmRlciByb3VuZGVkLW5vbmUgc2hhZG93IHRleHQteHMgbWluLXctODAgbWF4LXctMnhsXCJcclxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsZWZ0OiBgJHtwb3B1cFBvc2l0aW9uLmxlZnR9cHhgLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0b3A6IGAke3BvcHVwUG9zaXRpb24udG9wfXB4YCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4SGVpZ2h0OiBzaG93QWJvdmUgPyAnMjUwcHgnIDogJzI1MHB4J1xyXG4gICAgICAgICAgICAgICAgICAgIH19PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtYXgtaC02MFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtbm9uZVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXttZW50aW9uSW5wdXRSZWZ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWIgcC0yLjUgZm9udC1tZWRpdW0gdy1mdWxsIG91dGxpbmUtbm9uZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggZm9yIG1lbnRpb24uLi5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXttZW50aW9uU2VhcmNofVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TWVudGlvblNlYXJjaChlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy1hdXRvIGZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbnRpb25NYXRjaGVzLmxlbmd0aCA9PT0gMCA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC02MDAgZm9udC1tZWRpdW0gdGV4dC1zbSBpdGFsaWMgcC0yLjVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTm8gcmVzdWx0c1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVudGlvbk1hdGNoZXMubWFwKChtS2V5KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpbmZvID0ga2V5TWFwW21LZXldO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17bUtleX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctbmV1dHJhbC0xMDAgcC0yLjUgZm9udC1tZWRpdW0gdGV4dC14cyBvdmVyZmxvdy1oaWRkZW4gdGV4dC1lbGxpcHNpcyB3aGl0ZXNwYWNlLW5vd3JhcFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VEb3duPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25NZW50aW9uU2VsZWN0KG1LZXkpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2luZm8ubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+LFxyXG4gICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keVxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAge21vZGFsU3RhdGUuaXNPcGVuICYmICFkaXNhYmxlZCAmJiA8PlxyXG4gICAgICAgICAgICAgICAgPERpYWxvZyBkZWZhdWx0T3BlbiBvbk9wZW5DaGFuZ2U9e2Nsb3NlTW9kYWx9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cIm1heC13LVs2MDBweF0gIXJvdW5kZWQtbm9uZSBwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPERpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJmb250LWJvbGRcIj5TZXQgZGVmYXVsdCBmb3Ige21vZGFsU3RhdGUuc3Bhbj8uaW5uZXJUZXh0IHx8IG1vZGFsU3RhdGUubWVudGlvbktleX08L0RpYWxvZ1RpdGxlPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0yIHB5LTQgcHQtMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGZsZXgtMSBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIGxlYWRpbmctNiB0ZXh0LWdyYXktOTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEN1cnJlbnQgZGVmYXVsdDogXCJ7bW9kYWxTdGF0ZS5jdXJyZW50RGVmYXVsdH1cIiAobGVhdmUgYmxhbmsgdG8gcmVtb3ZlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0NhcGl0YWxpemU9XCJub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0NvcnJlY3Q9XCJvZmZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9e2B3b3JrZmxvdy1pbnB1dC1uYW1lYH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RyYWZ0RGVmYXVsdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJUeXBlIG5ldyBkZWZhdWx0Li4uXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXREcmFmdERlZmF1bHQoZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLW5vbmUgdGV4dC14c1wiLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb25maXJtRGVmYXVsdH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBwLTIgcHgtMyBoLWF1dG8gdy1hdXRvIHJvdW5kZWQtZnVsbCBmb250LXNlbWlib2xkIGdhcC0xIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ29uZmlybVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgICAgICAgICAgIDwvRGlhbG9nPlxyXG4gICAgICAgICAgICA8Lz59XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJjcmVhdGVQb3J0YWwiLCJEaWFsb2ciLCJEaWFsb2dDb250ZW50IiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJMYWJlbCIsIklucHV0IiwiQnV0dG9uIiwiY24iLCJNZW50aW9uSW5wdXQiLCJrZXlNYXAiLCJ2YWx1ZSIsImRlZmF1bHRWYWx1ZSIsIm9uQ2hhbmdlIiwib25CbHVyIiwib25EZWJvdW5jZUNoYW5nZSIsImRlYm91bmNlVGltZW91dE1TIiwicGxhY2Vob2xkZXIiLCJjbGFzc05hbWUiLCJpZCIsImRpc2FibGVkIiwia2VlcE5ld0xpbmUiLCJtb2RhbFN0YXRlIiwiZGl2UmVmIiwiY3VycmVudE91dHB1dCIsImRlYm91bmNlVGltZXJSZWYiLCJtZW50aW9uTW9kZSIsInNldE1lbnRpb25Nb2RlIiwibWVudGlvblNlYXJjaCIsInNldE1lbnRpb25TZWFyY2giLCJwb3B1cFJlZiIsInNob3dBYm92ZSIsInNldFNob3dBYm92ZSIsInBvcHVwUG9zaXRpb24iLCJzZXRQb3B1cFBvc2l0aW9uIiwidG9wIiwibGVmdCIsImF0UmFuZ2UiLCJzZXRBdFJhbmdlIiwibWVudGlvbklucHV0UmVmIiwic2tpcElubmVySHRtbFVwZGF0ZSIsInNldE1vZGFsU3RhdGUiLCJpc09wZW4iLCJzcGFuIiwibWVudGlvbktleSIsImN1cnJlbnREZWZhdWx0IiwiZHJhZnREZWZhdWx0Iiwic2V0RHJhZnREZWZhdWx0IiwicGFyc2VUZXh0VG9IdG1sIiwidGV4dCIsIlN0cmluZyIsIm1lbnRpb25SZWdleCIsImxhc3RJbmRleCIsInJlc3VsdEh0bWwiLCJtYXRjaCIsIm1hdGNoQWxsIiwic3RhcnQiLCJpbmRleCIsInBsYWluIiwic2FmZUh0bWwiLCJzbGljZSIsInJlcGxhY2UiLCJtZW50aW9uRGVmYXVsdCIsImFjdHVhbEtleSIsImZpbmRDYXNlSW5zZW5zaXRpdmVLZXkiLCJpbmZvIiwibGFiZWwiLCJ0cmltIiwiZGF0YVZhbHVlIiwic3Bhbkh0bWwiLCJlc2NhcGVBdHRyIiwibGVuZ3RoIiwib2JqIiwia2V5IiwibG93ZXJLZXkiLCJ0b0xvd2VyQ2FzZSIsImsiLCJPYmplY3QiLCJrZXlzIiwic3RyIiwidW5kZWZpbmVkIiwiY3VycmVudCIsImN1cnJlbnRIdG1sIiwiaW5uZXJIVE1MIiwibmV3SHRtbCIsImRvY3VtZW50IiwiYWN0aXZlRWxlbWVudCIsImdldEZpbmFsT3V0cHV0IiwicmVzdWx0IiwidHJhdmVyc2UiLCJub2RlIiwibm9kZVR5cGUiLCJOb2RlIiwiVEVYVF9OT0RFIiwibm9kZVZhbHVlIiwiRUxFTUVOVF9OT0RFIiwiZWwiLCJoYXNBdHRyaWJ1dGUiLCJnZXRBdHRyaWJ1dGUiLCJub2RlTmFtZSIsIkFycmF5IiwiZnJvbSIsImNoaWxkTm9kZXMiLCJmb3JFYWNoIiwiY2hpbGQiLCJ1cGRhdGVWYWx1ZSIsIm91dHB1dCIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJoYW5kbGVCbHVyIiwib25JbnB1dCIsIm9uS2V5RG93biIsImUiLCJ3aW5kb3ciLCJzZWwiLCJnZXRTZWxlY3Rpb24iLCJyYW5nZUNvdW50IiwiY3VycmVudFJhbmdlIiwiZ2V0UmFuZ2VBdCIsImNsb25lUmFuZ2UiLCJpbnB1dFJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJzcGFjZUJlbG93IiwiaW5uZXJIZWlnaHQiLCJib3R0b20iLCJzcGFjZUFib3ZlIiwiZHJvcGRvd25IZWlnaHQiLCJtaW5TcGFjZVJlcXVpcmVkIiwic2hvdWxkU2hvd0Fib3ZlIiwic2Nyb2xsWCIsInBhZ2VYT2Zmc2V0IiwiZG9jdW1lbnRFbGVtZW50Iiwic2Nyb2xsTGVmdCIsInNjcm9sbFkiLCJwYWdlWU9mZnNldCIsInNjcm9sbFRvcCIsImNsb3NlTWVudGlvblBvcHVwIiwiZnV6enlGaWx0ZXIiLCJxdWVyeSIsIml0ZW1zIiwibm9ybWFsaXplZFF1ZXJ5IiwicmVzdWx0cyIsIm5vcm1hbGl6ZWRLZXkiLCJub3JtYWxpemVkTGFiZWwiLCJzdGFydHNXaXRoIiwicHVzaCIsIml0ZW0iLCJyYW5rIiwiaW5jbHVkZXMiLCJzb3J0IiwiYSIsImIiLCJtYXAiLCJyIiwiYWxsS2V5cyIsIm1lbnRpb25NYXRjaGVzIiwib25NZW50aW9uU2VsZWN0IiwicmFuZ2UiLCJyZW1vdmVBdEZyb21UZXh0Tm9kZSIsInRleHROb2RlIiwicG9zIiwiZGF0YSIsInNlYXJjaFN0YXJ0IiwiTWF0aCIsIm1heCIsInNlYXJjaEVuZCIsIm1pbiIsImkiLCJjaGFyQXQiLCJzdWJzdHJpbmciLCJuZXdSYW5nZSIsImNyZWF0ZVJhbmdlIiwic2V0U3RhcnQiLCJjb2xsYXBzZSIsInN0YXJ0Q29udGFpbmVyIiwic3RhcnRPZmZzZXQiLCJuZXdSbmciLCJjb250YWluZXIiLCJwcmV2IiwiY3JlYXRlRWxlbWVudCIsImNvbnRlbnRFZGl0YWJsZSIsInNldEF0dHJpYnV0ZSIsInRhZyIsInRleHRDb250ZW50Iiwib25kYmxjbGljayIsIm9wZW5EZWZhdWx0TW9kYWwiLCJpbnNlcnROb2RlIiwiZmlsbGVyIiwiY3JlYXRlVGV4dE5vZGUiLCJwYXJlbnROb2RlIiwiaW5zZXJ0QmVmb3JlIiwibmV4dFNpYmxpbmciLCJzZXRTdGFydEFmdGVyIiwicmVtb3ZlQWxsUmFuZ2VzIiwiYWRkUmFuZ2UiLCJmb2N1cyIsIm9uS2V5Iiwib25DbGlja091dHNpZGUiLCJ0YXJnZXQiLCJjb250YWlucyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwidXBkYXRlUG9zaXRpb24iLCJtZW50aW9uVmFsdWUiLCJwYXR0ZXJuIiwiZXhpc3RpbmdEZWZhdWx0IiwiY29uZmlybURlZmF1bHQiLCJjbG9zZU1vZGFsIiwidXNlckRlZmF1bHQiLCJuZXdWYWx1ZSIsIm5ld0xhYmVsIiwibWVudGlvblNwYW5zIiwicXVlcnlTZWxlY3RvckFsbCIsImRpdiIsInJlZiIsInN0eWxlIiwid2hpdGVTcGFjZSIsInN1cHByZXNzQ29udGVudEVkaXRhYmxlV2FybmluZyIsImRhdGEtcGxhY2Vob2xkZXIiLCJhcmlhLWRpc2FibGVkIiwibWF4SGVpZ2h0IiwiaW5wdXQiLCJtS2V5Iiwib25Nb3VzZURvd24iLCJwcmV2ZW50RGVmYXVsdCIsImJvZHkiLCJkZWZhdWx0T3BlbiIsIm9uT3BlbkNoYW5nZSIsImlubmVyVGV4dCIsInR5cGUiLCJhdXRvQ2FwaXRhbGl6ZSIsImF1dG9Db3JyZWN0IiwiYXV0b0NvbXBsZXRlIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\n"));

/***/ })

});