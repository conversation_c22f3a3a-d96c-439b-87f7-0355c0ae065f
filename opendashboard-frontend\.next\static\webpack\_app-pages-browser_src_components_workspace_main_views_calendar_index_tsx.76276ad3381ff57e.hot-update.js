"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx":
/*!****************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/index.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CalendarView: function() { return /* binding */ CalendarView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/subMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addWeeks/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addMonths/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,addMonths,addWeeks,format,isSameDay,startOfDay,subMonths,subWeeks!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/providers/stackedpeek */ \"(app-pages-browser)/./src/providers/stackedpeek.tsx\");\n/* harmony import */ var _views_day__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./views/day */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/day.tsx\");\n/* harmony import */ var _views_week__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./views/week */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/week.tsx\");\n/* harmony import */ var _views_month__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./views/month */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/views/month.tsx\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventitem */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventitem.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/custom-ui/customSelect */ \"(app-pages-browser)/./src/components/custom-ui/customSelect.tsx\");\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @dnd-kit/core */ \"(app-pages-browser)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/utils/dragconstraints */ \"(app-pages-browser)/./src/utils/dragconstraints.ts\");\n/* harmony import */ var _components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/workspace/main/views/calendar/components/eventsegment */ \"(app-pages-browser)/./src/components/workspace/main/views/calendar/components/eventsegment.tsx\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInMilliseconds/index.js\");\n/* harmony import */ var _barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=differenceInDays,differenceInMilliseconds!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/differenceInDays/index.js\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Custom hook to track previous value\nconst usePrevious = (value)=>{\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        ref.current = value;\n    });\n    return ref.current;\n};\n_s(usePrevious, \"8uVE59eA/r6b92xF80p7sH8rXLk=\");\nconst CalendarView = (props)=>{\n    _s1();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage)();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize)();\n    const maybeRecord = (0,_providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared)();\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [viewType, setViewType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"week\");\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSideCalendar, setShowSideCalendar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [activeDragData, setActiveDragData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const savedScrollTop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const pointerCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const isInRecordTab = !!maybeRecord;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase, deleteRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews)();\n    const { sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering)();\n    const prevPeekRecordId = usePrevious(peekRecordId);\n    const { openRecord } = (0,_providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek)();\n    const sensors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensors)((0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.PointerSensor, {\n        activationConstraint: {\n            distance: 8\n        }\n    }), (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensor)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.TouchSensor, {\n        activationConstraint: {\n            delay: 150,\n            tolerance: 5\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n        const container = document.getElementById(containerId);\n        if (container) {\n            requestAnimationFrame(()=>{\n                container.scrollTop = savedScrollTop.current;\n            });\n        }\n    }, [\n        selectedEvent,\n        viewType\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowSideCalendar(!isMobile);\n    }, [\n        isMobile\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (prevPeekRecordId && !peekRecordId) {\n            refreshDatabase(definition.databaseId);\n        }\n    }, [\n        peekRecordId,\n        prevPeekRecordId,\n        definition.databaseId,\n        refreshDatabase\n    ]);\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 124,\n        columnNumber: 25\n    }, undefined);\n    const getEvents = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, sorts.length ? sorts : definition.sorts || [], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        const filteredRows = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_9__.searchFilteredRecords)(search || \"\", rows);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getDatabaseTitleCol)(database.database);\n        return filteredRows.map((row)=>{\n            const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];\n            let startDate;\n            if (startValue && typeof startValue === \"string\") {\n                startDate = new Date(startValue);\n            } else {\n                startDate = new Date();\n            }\n            let endDate;\n            if (definition.eventEndColumnId) {\n                const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];\n                if (endValue && typeof endValue === \"string\") {\n                    endDate = new Date(endValue);\n                } else {\n                    endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n                }\n            } else {\n                endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);\n            }\n            const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getRecordTitle)(row.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n            // Calculate isMultiDay and isAllDay properties\n            const isMultiDay = !(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_28__[\"default\"])(startDate, endDate);\n            const durationHours = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60);\n            const isAllDay = durationHours >= 23 || isMultiDay && durationHours >= 22;\n            return {\n                id: row.id,\n                title,\n                start: startDate,\n                end: endDate,\n                record: row.record,\n                processedRecord: row.processedRecord,\n                isMultiDay,\n                isAllDay\n            };\n        });\n    };\n    const getFilteredEvents = ()=>{\n        const baseEvents = getEvents();\n        if (!searchTerm.trim()) {\n            return baseEvents;\n        }\n        return baseEvents.filter((event)=>{\n            const searchLower = searchTerm.toLowerCase();\n            return event.title.toLowerCase().includes(searchLower);\n        });\n    };\n    const onDragStart = (event)=>{\n        if (event.active.data.current) {\n            var _event_active_rect_current_translated, _event_active_rect_current, _event_active_rect_current_translated1, _event_active_rect_current_translated2;\n            const initialPointerY = pointerCoordinates.current.y;\n            var _event_active_rect_current_translated_top;\n            const initialEventTop = (_event_active_rect_current_translated_top = (_event_active_rect_current = event.active.rect.current) === null || _event_active_rect_current === void 0 ? void 0 : (_event_active_rect_current_translated = _event_active_rect_current.translated) === null || _event_active_rect_current_translated === void 0 ? void 0 : _event_active_rect_current_translated.top) !== null && _event_active_rect_current_translated_top !== void 0 ? _event_active_rect_current_translated_top : 0;\n            const grabOffsetY = initialPointerY - initialEventTop;\n            // Get the exact dimensions from the DOM element\n            // Handle both event and segment types\n            const { payload, type } = event.active.data.current;\n            const eventId = type === \"segment\" ? payload.originalEvent.id : payload.id;\n            const draggedElement = document.getElementById(\"event-\".concat(eventId));\n            const width = draggedElement ? draggedElement.offsetWidth : (_event_active_rect_current_translated1 = event.active.rect.current.translated) === null || _event_active_rect_current_translated1 === void 0 ? void 0 : _event_active_rect_current_translated1.width;\n            const height = draggedElement ? draggedElement.offsetHeight : (_event_active_rect_current_translated2 = event.active.rect.current.translated) === null || _event_active_rect_current_translated2 === void 0 ? void 0 : _event_active_rect_current_translated2.height;\n            setActiveDragData({\n                ...event.active.data.current,\n                grabOffsetY,\n                width,\n                height\n            });\n            document.addEventListener(\"mousemove\", handleMouseMove);\n            document.addEventListener(\"touchmove\", handleTouchMove);\n        }\n    };\n    const onDragEnd = async (param)=>{\n        let { active, over } = param;\n        document.removeEventListener(\"mousemove\", handleMouseMove);\n        document.removeEventListener(\"touchmove\", handleTouchMove);\n        setActiveDragData(null);\n        if (!over || !active || !canEditData || active.id === over.id) {\n            return;\n        }\n        const activeData = active.data.current;\n        const overData = over.data.current;\n        if (!activeData || !overData) {\n            return;\n        }\n        const { payload, type } = activeData;\n        const eventToUpdate = type === \"segment\" ? payload.originalEvent : payload;\n        const originalStart = new Date(eventToUpdate.start);\n        const originalEnd = new Date(eventToUpdate.end);\n        const duration = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_29__[\"default\"])(originalEnd, originalStart);\n        let newStart;\n        if (overData.type.startsWith(\"allday\")) {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(originalStart, dayDifference);\n            newStart.setHours(0, 0, 0, 0);\n        } else if (overData.type === \"timeslot-minute\") {\n            // Handle precise minute-based drops\n            newStart = new Date(overData.date);\n            newStart.setHours(overData.hour, overData.minute, 0, 0);\n        } else if (overData.type === \"daycell\") {\n            const dayDifference = (0,_barrel_optimize_names_differenceInDays_differenceInMilliseconds_date_fns__WEBPACK_IMPORTED_MODULE_30__[\"default\"])(overData.date, (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_31__[\"default\"])(originalStart));\n            newStart = (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(originalStart, dayDifference);\n        } else {\n            return;\n        }\n        const newEnd = new Date(newStart.getTime() + duration);\n        const recordId = eventToUpdate.record.id;\n        const newValues = {\n            [definition.eventStartColumnId]: newStart.toISOString(),\n            ...definition.eventEndColumnId && {\n                [definition.eventEndColumnId]: newEnd.toISOString()\n            }\n        };\n        try {\n            await updateRecordValues(definition.databaseId, [\n                recordId\n            ], newValues);\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success('Event \"'.concat(eventToUpdate.title, '\" updated.'));\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to update event.\");\n        }\n    };\n    const handleMouseMove = (event)=>{\n        pointerCoordinates.current = {\n            x: event.clientX,\n            y: event.clientY\n        };\n    };\n    const handleTouchMove = (event)=>{\n        const touch = event.touches[0];\n        pointerCoordinates.current = {\n            x: touch.clientX,\n            y: touch.clientY\n        };\n    };\n    const events = getFilteredEvents();\n    const goToToday = ()=>setSelectedDate(new Date());\n    const goToPrevious = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, -1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_33__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_34__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const goToNext = ()=>{\n        switch(viewType){\n            case \"day\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(prevDate, 1));\n                break;\n            case \"week\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_35__[\"default\"])(prevDate, 1));\n                break;\n            case \"month\":\n                setSelectedDate((prevDate)=>(0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_36__[\"default\"])(prevDate, 1));\n                break;\n        }\n    };\n    const handleRequestCreateEvent = function(date) {\n        let useSystemTime = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (useSystemTime) {\n            const now = new Date();\n            const newDate = new Date(date);\n            newDate.setHours(now.getHours(), now.getMinutes(), now.getSeconds(), now.getMilliseconds());\n            handleCreateEvent(newDate);\n        } else {\n            handleCreateEvent(date);\n        }\n    };\n    const handleCreateEvent = async function() {\n        let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();\n        if (!canEditData) return;\n        const startTime = new Date(date);\n        const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);\n        const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_22__.getDatabaseTitleCol)(database.database);\n        try {\n            const recordValues = {\n                [definition.eventStartColumnId]: startTime.toISOString(),\n                ...definition.eventEndColumnId && {\n                    [definition.eventEndColumnId]: endTime.toISOString()\n                }\n            };\n            if (titleColOpts.titleColId) {\n                recordValues[titleColOpts.titleColId] = \"New Event\";\n            }\n            const result = await createRecords(definition.databaseId, [\n                recordValues\n            ]);\n            if (result && result.records && result.records.length > 0) {\n                const newRecordId = result.records[0].id;\n                if (newRecordId) {\n                    await refreshDatabase(definition.databaseId);\n                    setPeekRecordId(newRecordId);\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"New event created\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Error accessing the new event\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event properly\");\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to create event\");\n        }\n    };\n    const handleEventClick = (event)=>{\n        if (event && event.id) {\n            const containerId = viewType === \"day\" ? \"day-view-container\" : \"week-view-container\";\n            const container = document.getElementById(containerId);\n            if (container) {\n                savedScrollTop.current = container.scrollTop;\n            }\n            openRecord(event.id, event.record.databaseId);\n            setSelectedEvent(event.id);\n        }\n    };\n    const getHeaderDateDisplay = ()=>{\n        switch(viewType){\n            case \"day\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n            case \"week\":\n                return \"\".concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(selectedDate, -selectedDate.getDay()), \"MMM d\"), \" - \").concat((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])((0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_32__[\"default\"])(selectedDate, 6 - selectedDate.getDay()), \"MMM d, yyyy\"));\n            case \"month\":\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(selectedDate, \"MMMM yyyy\");\n            default:\n                return (0,_barrel_optimize_names_addDays_addMonths_addWeeks_format_isSameDay_startOfDay_subMonths_subWeeks_date_fns__WEBPACK_IMPORTED_MODULE_37__[\"default\"])(selectedDate, \"MMMM d, yyyy\");\n        }\n    };\n    const handleEventDelete = async (event)=>{\n        if (!canEditData) return;\n        try {\n            await deleteRecords(database.database.id, [\n                event.record.id\n            ]);\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Failed to delete event\");\n        }\n    };\n    const viewTypeOptions = [\n        {\n            id: \"day\",\n            value: \"day\",\n            title: \"Day\",\n            data: \"day\"\n        },\n        {\n            id: \"week\",\n            value: \"week\",\n            title: \"Week\",\n            data: \"week\"\n        },\n        {\n            id: \"month\",\n            value: \"month\",\n            title: \"Month\",\n            data: \"month\"\n        }\n    ];\n    const selectedViewOption = viewType === \"day\" ? [\n        \"day\"\n    ] : viewType === \"week\" ? [\n        \"week\"\n    ] : [\n        \"month\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"border-b border-neutral-300 bg-white\", isInRecordTab && \"py-1\"),\n                children: [\n                    isMobile ? /* Mobile Header Layout */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"p-2\", isInRecordTab && \"py-1\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black truncate flex-1 mr-2\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(!showSideCalendar),\n                                        className: \"rounded-full h-8 px-3 text-xs text-black hover:bg-gray-50\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 12\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToToday,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToPrevious,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleLeftIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 18\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                        variant: \"ghost\",\n                                                        onClick: goToNext,\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleRightIcon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 20\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 18\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__.CustomSelect, {\n                                                options: viewTypeOptions,\n                                                selectedIds: selectedViewOption,\n                                                onChange: (selected)=>{\n                                                    if (selected.length > 0) {\n                                                        setViewType(selected[0]);\n                                                    }\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black w-20\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                placeholder: \"View\",\n                                                hideSearch: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.PlusIcon, {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 20\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 18\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 12\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 10\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex items-center justify-between px-4\", isInRecordTab ? \"py-1\" : \"py-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: goToToday,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs text-black hover:bg-gray-50\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToPrevious,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleLeftIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                                variant: \"ghost\",\n                                                onClick: goToNext,\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full p-1 text-black hover:bg-gray-50\", isInRecordTab ? \"h-6 w-6\" : \"h-8 w-8\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.AngleRightIcon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 16\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 14\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: getHeaderDateDisplay()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 10\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                                placeholder: \"Search events...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-48 pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.MagnifyingGlassIcon, {\n                                                className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 16\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 14\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_customSelect__WEBPACK_IMPORTED_MODULE_23__.CustomSelect, {\n                                        options: viewTypeOptions,\n                                        selectedIds: selectedViewOption,\n                                        onChange: (selected)=>{\n                                            if (selected.length > 0) {\n                                                setViewType(selected[0]);\n                                            }\n                                        },\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"px-3 text-xs border-neutral-300 rounded-full bg-white hover:bg-gray-50 text-black\", isInRecordTab ? \"h-6 w-20\" : \"h-8 w-28\"),\n                                        placeholder: \"View\",\n                                        hideSearch: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 12\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        onClick: ()=>handleRequestCreateEvent(selectedDate, true),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"rounded-full px-3 text-xs border border-neutral-300 bg-white hover:bg-gray-50 text-black gap-1\", isInRecordTab ? \"h-6\" : \"h-8\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.PlusIcon, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 16\n                                            }, undefined),\n                                            !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Add Event\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 35\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 14\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 10\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 8\n                    }, undefined),\n                    isMobile && !isInRecordTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_12__.Input, {\n                                    placeholder: \"Search events...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pr-8 h-8 text-xs border-neutral-300 bg-transparent shadow-none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 599,\n                                    columnNumber: 12\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_27__.MagnifyingGlassIcon, {\n                                    className: \"h-3 w-3 absolute right-2 top-1/2 transform -translate-y-1/2 text-neutral-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 12\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 10\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 8\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 419,\n                columnNumber: 6\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex min-h-0\",\n                children: [\n                    showSideCalendar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(\"flex-none bg-white\", isMobile ? \"w-full absolute z-50 backdrop-blur-sm h-full shadow-lg\" : \"w-fit border-r border-neutral-300\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center p-2 border-b border-neutral-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-black\",\n                                        children: \"Calendar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 10\n                                    }, undefined),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: ()=>setShowSideCalendar(false),\n                                        className: \"rounded-full h-8 w-8 p-1 text-black hover:bg-neutral-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Close\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 14\n                                            }, undefined),\n                                            \"\\xd7\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 12\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 8\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                mode: \"single\",\n                                selected: selectedDate,\n                                onSelect: (date)=>{\n                                    if (date) {\n                                        setSelectedDate(date);\n                                        if (isMobile) {\n                                            setShowSideCalendar(false);\n                                        }\n                                    }\n                                },\n                                className: \"rounded-md border-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 8\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 6\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.DndContext, {\n                        sensors: sensors,\n                        onDragStart: onDragStart,\n                        onDragEnd: onDragEnd,\n                        modifiers: [\n                            _utils_dragconstraints__WEBPACK_IMPORTED_MODULE_25__.restrictToCalendarContainer\n                        ],\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                \"data-calendar-content\": \"true\",\n                                children: [\n                                    viewType === \"day\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_day__WEBPACK_IMPORTED_MODULE_18__.DayView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"week\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_week__WEBPACK_IMPORTED_MODULE_19__.WeekView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: handleRequestCreateEvent,\n                                        canEditData: canEditData,\n                                        savedScrollTop: savedScrollTop,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 7\n                                    }, undefined),\n                                    viewType === \"month\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_views_month__WEBPACK_IMPORTED_MODULE_20__.MonthView, {\n                                        selectedDate: selectedDate,\n                                        events: events,\n                                        selectedEvent: selectedEvent,\n                                        setSelectedEvent: setSelectedEvent,\n                                        setSelectedDate: setSelectedDate,\n                                        openAddEventForm: (date)=>handleRequestCreateEvent(date, true),\n                                        canEditData: canEditData,\n                                        handleEventClick: handleEventClick,\n                                        activeDragData: activeDragData\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 7\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 6\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.DragOverlay, {\n                                dropAnimation: null,\n                                children: activeDragData && activeDragData.type === \"segment\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventsegment__WEBPACK_IMPORTED_MODULE_26__.CalendarEventSegment, {\n                                    segment: activeDragData.payload,\n                                    view: viewType === \"day\" ? \"day\" : \"week\",\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 698,\n                                    columnNumber: 11\n                                }, undefined) : activeDragData && activeDragData.type === \"event\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_calendar_components_eventitem__WEBPACK_IMPORTED_MODULE_21__.CalendarEventItem, {\n                                    event: activeDragData.payload,\n                                    view: viewType,\n                                    onClick: ()=>{},\n                                    style: {\n                                        width: activeDragData.width,\n                                        height: activeDragData.height\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 11\n                                }, undefined) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 6\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 6\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n                lineNumber: 612,\n                columnNumber: 4\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\lo\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\index.tsx\",\n        lineNumber: 418,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(CalendarView, \"+7bRlCRHgQ8LDvRl8YePmx4LT0w=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_page__WEBPACK_IMPORTED_MODULE_5__.usePage,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_15__.useScreenSize,\n        _providers_record__WEBPACK_IMPORTED_MODULE_16__.useMaybeRecord,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_6__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_7__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViews,\n        _providers_views__WEBPACK_IMPORTED_MODULE_8__.useViewFiltering,\n        usePrevious,\n        _providers_stackedpeek__WEBPACK_IMPORTED_MODULE_17__.useStackedPeek,\n        _dnd_kit_core__WEBPACK_IMPORTED_MODULE_24__.useSensors\n    ];\n});\n_c = CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/index.tsx\n"));

/***/ })

});