import React, { useMemo, useState, useCallback } from 'react';
import { format, startOfWeek, endOfWeek, isToday, addDays, setHours } from 'date-fns';
import { cn } from '@/lib/utils';
import { CalendarEvent } from '@/typings/page';
import { CalendarEventSegment } from '@/components/workspace/main/views/calendar/components/eventsegment';
import { AllDayRow } from '@/components/workspace/main/views/calendar/components/allday';
import { NoEvents } from '@/components/workspace/main/views/calendar/components/noevents';
import { eventsToSegments, getSegmentsForWeek, getSegmentsForDay, getAllDaySegments, getTimeSlotSegments, getSegmentHeight, getSegmentTopOffset } from '@/utils/multiDay';
import { calculateLayout } from '@/utils/eventCollision';
import { useDroppable } from '@dnd-kit/core';


interface WeekViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  setSelectedDate: (date: Date) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  savedScrollTop: React.MutableRefObject<number>;
  handleEventClick: (event: CalendarEvent) => void;
  activeDragData: any;
}

const TimeSlot = ({
  day,
  hour,
  children,
  onDoubleClick,
  isDragging
}: {
  day: Date;
  hour: number;
  children: React.ReactNode;
  onDoubleClick: (minute: number) => void;
  isDragging: boolean;
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  const [currentMinute, setCurrentMinute] = useState<number>(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const shouldShowPrecision = isHovering || isDragging;

  const calculateMinuteFromPosition = useCallback((clientY: number) => {
    if (!containerRef.current) return 0;

    const rect = containerRef.current.getBoundingClientRect();
    const y = clientY - rect.top;
    const minute = Math.floor((y / rect.height) * 60);
    return Math.max(0, Math.min(59, minute));
  }, []);

  const handleMouseEnter = useCallback(() => {
    setIsHovering(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (!isDragging) {
      setIsHovering(false);
      setMousePosition(null);
      setCurrentMinute(0);
    }
  }, [isDragging]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const minute = calculateMinuteFromPosition(e.clientY);
    setMousePosition({ x: e.clientX, y: e.clientY });
    setCurrentMinute(minute);
  }, [calculateMinuteFromPosition]);

  React.useEffect(() => {
    if (!isDragging) return;

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        if (e.clientX >= rect.left && e.clientX <= rect.right &&
          e.clientY >= rect.top && e.clientY <= rect.bottom) {
          const minute = calculateMinuteFromPosition(e.clientY);
          setMousePosition({ x: e.clientX, y: e.clientY });
          setCurrentMinute(minute);
          setIsHovering(true);
        }
      }
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    return () => document.removeEventListener('mousemove', handleGlobalMouseMove);
  }, [isDragging, calculateMinuteFromPosition]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (e.detail === 2) { // Double click
      onDoubleClick(currentMinute);
    }
  }, [currentMinute, onDoubleClick]);

  return (
    <div
      ref={containerRef}
      className="flex-1 border-r border-neutral-300 last:border-r-0 relative min-h-[60px] cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      onClick={handleClick}
    >
      <HourDropZone
        day={day}
        hour={hour}
        currentMinute={currentMinute}
        isActive={shouldShowPrecision}
      />

      {children}
    </div>
  );
};

const HourDropZone = ({
  day,
  hour,
  currentMinute,
  isActive
}: {
  day: Date;
  hour: number;
  currentMinute: number;
  isActive: boolean;
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `hour-${format(day, 'yyyy-MM-dd')}-${hour}`,
    data: {
      date: day,
      hour,
      minute: currentMinute, 
      type: 'timeslot-minute'
    }
  });

  return (
    <div
      ref={setNodeRef}
      className="absolute inset-0 w-full h-full"
    />
  );
};

export const WeekView: React.FC<WeekViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  setSelectedDate,
  openAddEventForm,
  canEditData,
  savedScrollTop,
  handleEventClick,
  activeDragData,
}) => {
  const weekCalculations = useMemo(() => {
    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 0 });
    const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 0 });
    const days = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
    const todayIndex = days.findIndex(day => isToday(day));

    return {
      weekStart,
      weekEnd,
      days,
      todayIndex
    };
  }, [selectedDate]);

  const { days, todayIndex } = weekCalculations;
  const hours = Array.from({ length: 24 }, (_, i) => i);

  const weekSegments = useMemo(() => {
    const allSegments = eventsToSegments(events);
    return getSegmentsForWeek(allSegments, weekCalculations.weekStart, weekCalculations.weekEnd);
  }, [events, weekCalculations.weekStart, weekCalculations.weekEnd]);

  const allDaySegments = useMemo(() => getAllDaySegments(weekSegments), [weekSegments]);
  const timeSlotSegments = useMemo(() => getTimeSlotSegments(weekSegments), [weekSegments]);

  const currentTimePosition = useMemo(() =>
    todayIndex !== -1
      ? {
        dayIndex: todayIndex,
        hour: new Date().getHours(),
        minutes: new Date().getMinutes()
      }
      : null,
    [todayIndex]
  );

  const getEventDurationInMinutes = (event: CalendarEvent): number => {
    const start = new Date(event.start);
    const end = new Date(event.end);
    return Math.max(20, (end.getTime() - start.getTime()) / (1000 * 60));
  };

  const renderEmptyState = () => (
    <NoEvents
      title="No events this week"
      message="Your week is completely free. Add some events to get organized!"
      showCreateButton={canEditData}
      onCreate={() => openAddEventForm(selectedDate)}
    />
  );

  const renderTimeSlots = () => (
    <div className="flex-1 relative bg-white border-b border-neutral-300 overflow-y-auto lg:overflow-auto" id="week-view-container">
      <div className="relative overflow-x-auto lg:overflow-x-visible">
        <div className="flex flex-col min-w-[700px] lg:min-w-0">
          <div className="relative">
            {hours.map((hour, i) => (
              <div
                key={hour}
                className={cn(
                  "flex border-b border-neutral-300 hover:bg-neutral-50 transition-colors",
                  i === hours.length - 1 && "border-b-neutral-300"
                )}
                style={{ height: '60px' }}
              >
                <div
                  data-time-labels="true"
                  className="sticky left-0 flex items-start justify-end pr-4 pt-2 text-xs font-medium text-black border-r border-neutral-300 bg-white z-20 w-14 lg:w-20"
                >
                  <div className="text-right">
                    <div className="text-xs font-semibold">
                      {format(setHours(new Date(), hour), 'h a')}
                    </div>
                  </div>
                </div>

                {days.map((day) => {
                  const daySegments = getSegmentsForDay(timeSlotSegments, day);
                  const { segmentLayouts } = calculateLayout(daySegments);

                  return (
                    <TimeSlot
                      key={`${day.toISOString()}-${hour}`}
                      day={day}
                      hour={hour}
                      isDragging={!!activeDragData}
                      onDoubleClick={(minute) => {
                        if (canEditData) {
                          const newDate = new Date(day);
                          newDate.setHours(hour, minute, 0, 0);
                          openAddEventForm(newDate);
                        }
                      }}
                    >
                      {segmentLayouts.map((layout) => {
                        const segmentStart = layout.segment.startTime;
                        const isFirstHour = segmentStart.getHours() === hour;

                        if (!isFirstHour) return null;

                        const segmentHeight = getSegmentHeight(layout.segment);
                        const topOffset = getSegmentTopOffset(layout.segment);

                        return (
                          <CalendarEventSegment
                            key={layout.segment.id}
                            segment={layout.segment}
                            style={{
                              height: `${segmentHeight}px`,
                              position: 'absolute',
                              top: `${topOffset}px`,
                              left: `${layout.left}%`,
                              width: `${layout.width}%`,
                              zIndex: activeDragData?.payload?.id === layout.segment.id ? 50 : layout.zIndex,
                              paddingRight: '2px',
                              border: layout.hasOverlap ? '1px solid white' : 'none',
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              const container = document.getElementById('week-view-container');
                              if (container) {
                                savedScrollTop.current = container.scrollTop;
                              }
                              setSelectedEvent(layout.segment.originalEventId);
                              handleEventClick(layout.segment.originalEvent);
                            }}
                            view="week"
                            isDragging={activeDragData?.payload?.id === layout.segment.id}
                          />
                        );
                      })}
                    </TimeSlot>
                  );
                })}
              </div>
            ))}
          </div>

          {currentTimePosition && (
            <div className="absolute top-0 bottom-0 left-14 lg:left-20 right-0 pointer-events-none z-30">
              <div className="relative h-full w-full">
                <div
                  className="absolute flex items-center"
                  style={{
                    top: `${(currentTimePosition.hour + currentTimePosition.minutes / 60) * 60}px`,
                    left: `${(currentTimePosition.dayIndex / 7) * 100}%`,
                    width: `${(1 / 7) * 100}%`,
                  }}
                >
                  <div className="w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5" />
                  <div className="flex-1 border-t-2 border-red-500 shadow-sm" />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col bg-white">
      <div
        data-day-headers="true"
        className="border-b border-neutral-300 bg-white sticky top-0 z-20"
      >
        <div className="flex overflow-x-hidden">
          <div className="sticky left-0 bg-white z-10 w-14 lg:w-20"></div>
          <div className="flex flex-1 min-w-[calc(100vw-3.5rem)] lg:min-w-0">
            {days.map((day, i) => (
              <div
                key={i}
                className="flex-1 text-center cursor-pointer py-3 px-0 lg:py-4"
                onClick={() => setSelectedDate(day)}
              >
                <div className={cn(
                  "font-semibold text-black mb-1",
                  "text-xs"
                )}>
                  {format(day, 'EEE')}
                </div>
                <div className={cn(
                  "inline-flex items-center justify-center font-medium text-xs w-6 h-6 rounded-full",
                  isToday(day)
                    ? "bg-black text-white"
                    : "text-black hover:bg-neutral-100"
                )}>
                  {format(day, 'd')}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* All-Day Row */}
      <AllDayRow
        selectedDate={selectedDate}
        segments={allDaySegments}
        selectedEvent={selectedEvent}
        setSelectedEvent={setSelectedEvent}
        handleEventClick={handleEventClick}
        canEditData={canEditData}
        openAddEventForm={openAddEventForm}
        view="week"
        activeDragData={activeDragData}
      />

      {/* Main Content */}
      {weekSegments.length === 0
        ? renderEmptyState()
        : renderTimeSlots()}
    </div>
  );
};